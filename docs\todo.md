**Issues**:
Note: use the supabase project for db, backend as needed, apply changes directly to the supabase project if needed.
- [X] In mobile size screen , the hero right side section containing image and text doesn't resposively displayed correctly instead the image height is very small in mobile screen, text displays in white color out of the image in a white background.
- [ ] use aws SES for the email service instead of mailgun to send emails to users.
- [ ] send automated email first and sms, 2 hours before checkout to guests
- [ ] separate the admin completely from the normal users (gusts, hosts) - use separate admin table for admins don't mix them with normal users profiles
- [ ] send automated email (first) and sms, 2 hours before checkout to guests
- [ ] increase the platform fee to 15%
- [ ] Add a checkbox at checkout: “I agree to the Terms & Safety Policy.”
- [ ] Add the content from policy terms documents inside docs directory to corresponding pages and connect the pages to the correct footer items.
- [X] In the landing page below "Featured Stays" section I propose you to have the categories like first "Accomodations (Hotels, Apartments, Lodges and Guest Houses), Car Rental, Restaurants and Night Life
- [X] update the nav items. remove "messages" from nav item and instead add a message icon to the right side items, if clicked it should navigate to messages. add a about nav item that bring to the about gesco stay section in landing page

- [ ] share button doesn't work as expected, there are no share and save buttons in single car page