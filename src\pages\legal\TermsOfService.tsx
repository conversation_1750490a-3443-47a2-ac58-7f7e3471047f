import Navbar from "@/components/layout/Navbar";

const TermsOfService = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      <div className="container mx-auto px-4 py-12">
        <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-8">Terms of Service</h1>
          
          <div className="prose prose-lg max-w-none">
            <p className="text-lg text-gray-600 mb-8">
              Welcome to GescoStay. By using our platform, you agree to the following terms:
            </p>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">Bookings</h2>
              <p className="text-gray-700 mb-4">
                All bookings must be made through our official website or approved partners.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">Payments</h2>
              <p className="text-gray-700 mb-4">
                Payments are processed securely via GescoPay, Mobile Money, or debit/credit card. 
                Bookings are only confirmed after successful payment.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">Cancellations</h2>
              <p className="text-gray-700 mb-4">
                Cancellations are subject to the host's cancellation policy. Refund eligibility varies by property.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">User Conduct</h2>
              <p className="text-gray-700 mb-4">
                Guests and hosts must treat each other respectfully and comply with all local laws. 
                Abuse, fraud, or harassment will result in account suspension.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">Platform Access</h2>
              <p className="text-gray-700 mb-4">
                GescoStay reserves the right to limit or revoke access to users found violating 
                platform policies or engaging in unlawful activity.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">Policies for Guests & Hosts</h2>
              
              <h3 className="text-xl font-semibold text-gray-900 mb-3">For Guests:</h3>
              <ul className="list-disc list-inside text-gray-700 mb-4 space-y-2">
                <li>Abide by host's house rules, including check-in/out times.</li>
                <li>No damage to property or inclusion of unregistered guests.</li>
                <li>Maintain respectful behavior towards host and property.</li>
              </ul>

              <h3 className="text-xl font-semibold text-gray-900 mb-3">For Hosts:</h3>
              <ul className="list-disc list-inside text-gray-700 mb-4 space-y-2">
                <li>Ensure property listings are accurate and updated.</li>
                <li>Maintain cleanliness, safety, and legal compliance of the property.</li>
                <li>Avoid any form of discrimination based on race, gender, religion, or nationality.</li>
              </ul>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">Consent</h2>
              <p className="text-gray-700 mb-4">
                By continuing to use GescoStay, you confirm that you have read, understood, and agreed 
                to our Terms of Service, Safety Policy, and Guest/Host Policies. If you do not agree, 
                please do not use the platform.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">Contact</h2>
              <p className="text-gray-700">
                For questions or more information, please contact our support team at{" "}
                <a href="mailto:<EMAIL>" className="text-accent hover:underline">
                  <EMAIL>
                </a>
              </p>
            </section>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TermsOfService;
