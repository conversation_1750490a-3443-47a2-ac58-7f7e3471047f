import React, { useRef, useEffect, useCallback } from "react";
import { GoogleMapsProvider } from "./GoogleMapsProvider";
import {
  getDefaultMapOptions,
  LocationData,
  getDirectionsUrl,
  getPlaceUrl,
  isGoogleMapsLoaded,
} from "@/lib/google-maps";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { MapPin, Navigation, ExternalLink } from "lucide-react";

interface MapDisplayProps {
  location: LocationData;
  title?: string;
  height?: string;
  className?: string;
  showNavigationButtons?: boolean;
  showInfoWindow?: boolean;
  zoom?: number;
}

const MapDisplayComponent: React.FC<MapDisplayProps> = ({
  location,
  title,
  height = "300px",
  className = "",
  showNavigationButtons = true,
  showInfoWindow = true,
  zoom = 15,
}) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<google.maps.Map | null>(null);
  const markerRef = useRef<
    google.maps.marker.AdvancedMarkerElement | google.maps.Marker | null
  >(null);
  const infoWindowRef = useRef<google.maps.InfoWindow | null>(null);

  // Initialize map
  const initializeMap = useCallback(() => {
    if (!mapRef.current || mapInstanceRef.current) return;

    const center = { lat: location.latitude, lng: location.longitude };

    const mapOptions: google.maps.MapOptions = {
      ...getDefaultMapOptions(),
      center,
      zoom,
      // Disable interaction for display-only map
      disableDefaultUI: true,
      zoomControl: true,
      gestureHandling: "cooperative",
    };

    mapInstanceRef.current = new google.maps.Map(mapRef.current, mapOptions);

    // Add marker
    addMarker();

    // Add info window if enabled
    if (showInfoWindow) {
      addInfoWindow();
    }
  }, [location, zoom, showInfoWindow]);

  // Add marker to map
  const addMarker = useCallback(() => {
    if (!mapInstanceRef.current) return;

    try {
      // Remove existing marker
      if (markerRef.current) {
        if (markerRef.current.map) {
          markerRef.current.map = null;
        } else if (markerRef.current.setMap) {
          markerRef.current.setMap(null);
        }
      }

      // Try to use AdvancedMarkerElement first, fallback to regular Marker
      if (google.maps.marker && google.maps.marker.AdvancedMarkerElement) {
        // Create custom marker element
        const markerElement = document.createElement("div");
        markerElement.innerHTML = `
          <div style="
            width: 40px;
            height: 40px;
            background: #DC2626;
            border: 4px solid white;
            border-radius: 50% 50% 50% 0;
            transform: rotate(-45deg);
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(0,0,0,0.4);
            display: flex;
            align-items: center;
            justify-content: center;
          ">
            <div style="
              width: 12px;
              height: 12px;
              background: white;
              border-radius: 50%;
              transform: rotate(45deg);
            "></div>
          </div>
        `;

        // Add new marker using AdvancedMarkerElement
        markerRef.current = new google.maps.marker.AdvancedMarkerElement({
          position: { lat: location.latitude, lng: location.longitude },
          map: mapInstanceRef.current,
          content: markerElement,
          title: title || location.formatted_address,
        });
      } else {
        // Fallback to regular Marker
        markerRef.current = new google.maps.Marker({
          position: { lat: location.latitude, lng: location.longitude },
          map: mapInstanceRef.current,
          title: title || location.formatted_address,
        }) as any;
      }

      // Add click listener to marker for info window
      if (showInfoWindow) {
        markerRef.current.addListener("click", () => {
          if (infoWindowRef.current && mapInstanceRef.current) {
            infoWindowRef.current.open(
              mapInstanceRef.current,
              markerRef.current
            );
          }
        });
      }
    } catch (error) {
      console.error("Error creating marker:", error);
    }
  }, [location, title, showInfoWindow]);

  // Add info window
  const addInfoWindow = useCallback(() => {
    if (!mapInstanceRef.current || !markerRef.current) return;

    const contentString = `
      <div style="padding: 8px; max-width: 250px;">
        <h3 style="margin: 0 0 8px 0; font-size: 16px; font-weight: 600; color: #1f2937;">
          ${title || "Location"}
        </h3>
        <p style="margin: 0; font-size: 14px; color: #6b7280; line-height: 1.4;">
          ${location.formatted_address}
        </p>
        <div style="margin-top: 12px; display: flex; gap: 8px;">
          <a 
            href="${getDirectionsUrl(
              location.latitude,
              location.longitude,
              title
            )}" 
            target="_blank" 
            rel="noopener noreferrer"
            style="
              display: inline-flex; 
              align-items: center; 
              gap: 4px; 
              padding: 6px 12px; 
              background-color: #dc2626; 
              color: white; 
              text-decoration: none; 
              border-radius: 6px; 
              font-size: 12px; 
              font-weight: 500;
            "
          >
            <span>Directions</span>
          </a>
          <a 
            href="${getPlaceUrl(location.latitude, location.longitude, title)}" 
            target="_blank" 
            rel="noopener noreferrer"
            style="
              display: inline-flex; 
              align-items: center; 
              gap: 4px; 
              padding: 6px 12px; 
              background-color: #f3f4f6; 
              color: #374151; 
              text-decoration: none; 
              border-radius: 6px; 
              font-size: 12px; 
              font-weight: 500;
            "
          >
            <span>View on Maps</span>
          </a>
        </div>
      </div>
    `;

    infoWindowRef.current = new google.maps.InfoWindow({
      content: contentString,
    });

    // Auto-open info window
    infoWindowRef.current.open(mapInstanceRef.current, markerRef.current);
  }, [location, title]);

  // Handle directions button click
  const handleGetDirections = useCallback(() => {
    const url = getDirectionsUrl(location.latitude, location.longitude, title);
    window.open(url, "_blank", "noopener,noreferrer");
  }, [location, title]);

  // Handle view on maps button click
  const handleViewOnMaps = useCallback(() => {
    const url = getPlaceUrl(location.latitude, location.longitude, title);
    window.open(url, "_blank", "noopener,noreferrer");
  }, [location, title]);

  // Initialize map when component mounts
  useEffect(() => {
    if (isGoogleMapsLoaded()) {
      initializeMap();
    }
  }, [initializeMap]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (infoWindowRef.current) {
        infoWindowRef.current.close();
      }
    };
  }, []);

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <MapPin className="h-5 w-5 text-red-600" />
          {title || "Location"}
        </CardTitle>
        <p className="text-sm text-gray-600">{location.formatted_address}</p>
      </CardHeader>
      <CardContent className="space-y-4">
        <div
          ref={mapRef}
          style={{ height }}
          className="w-full border border-gray-200 rounded-lg overflow-hidden"
        />

        {showNavigationButtons && (
          <div className="flex gap-2">
            <Button
              onClick={handleGetDirections}
              className="flex items-center gap-2 bg-red-600 hover:bg-red-700 text-white"
            >
              <Navigation className="h-4 w-4" />
              Get Directions
            </Button>
            <Button
              onClick={handleViewOnMaps}
              variant="outline"
              className="flex items-center gap-2"
            >
              <ExternalLink className="h-4 w-4" />
              View on Maps
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export const MapDisplay: React.FC<MapDisplayProps> = (props) => {
  return (
    <GoogleMapsProvider>
      <MapDisplayComponent {...props} />
    </GoogleMapsProvider>
  );
};
