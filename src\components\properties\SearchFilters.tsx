
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Search, ChevronDown } from "lucide-react";

interface SearchFiltersProps {
  searchTerm: string;
  onSearchChange: (term: string) => void;
  filters: {
    minPrice?: number;
    maxPrice?: number;
    propertyType: string;
    bedrooms: string;
    amenities: string[];
  };
  onFiltersChange: (filters: any) => void;
}

const SearchFilters = ({ searchTerm, onSearchChange, filters, onFiltersChange }: SearchFiltersProps) => {
  const [isFiltersOpen, setIsFiltersOpen] = useState(false);

  return (
    <div className="bg-white shadow-md rounded-lg p-4 mb-8">
      <div className="flex flex-col md:flex-row md:items-center gap-4">
        {/* Search input */}
        <div className="flex-grow">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
            <input
              type="text"
              placeholder="Search destinations, properties..."
              value={searchTerm}
              onChange={(e) => onSearchChange(e.target.value)}
              className="w-full pl-10 pr-4 py-2 h-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-secondary"
            />
          </div>
        </div>

        {/* Date selection */}
        <div className="grid grid-cols-2 gap-2">
          <div>
            <label className="block text-xs text-gray-600 mb-1">Check-in</label>
            <input
              type="date"
              className="w-full p-2 h-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-secondary"
            />
          </div>
          <div>
            <label className="block text-xs text-gray-600 mb-1">Check-out</label>
            <input
              type="date"
              className="w-full p-2 h-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-secondary"
            />
          </div>
        </div>

        {/* Toggle filters button */}
        <div className="flex items-center">
          <Button
            variant="outline"
            className="flex items-center gap-2 text-brown h-10"
            onClick={() => setIsFiltersOpen(!isFiltersOpen)}
          >
            Filters
            <ChevronDown size={16} className={`transition-transform duration-200 ${isFiltersOpen ? "rotate-180" : ""}`} />
          </Button>
          <Button className="ml-2 bg-accent hover:bg-accent/90 text-white h-10">
            Search
          </Button>
        </div>
      </div>

      {/* Advanced filters */}
      {isFiltersOpen && (
        <div className="mt-6 pt-6 border-t border-gray-200 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 animate-fade-in">
          {/* Price range */}
          <div>
            <label className="block text-sm font-medium text-brown mb-2">Price Range</label>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <input
                  type="number"
                  placeholder="Min"
                  value={filters.minPrice || ""}
                  onChange={(e) => onFiltersChange({...filters, minPrice: e.target.value ? Number(e.target.value) : undefined})}
                  className="w-full p-2 border border-gray-300 rounded-md"
                />
              </div>
              <div>
                <input
                  type="number"
                  placeholder="Max"
                  value={filters.maxPrice || ""}
                  onChange={(e) => onFiltersChange({...filters, maxPrice: e.target.value ? Number(e.target.value) : undefined})}
                  className="w-full p-2 border border-gray-300 rounded-md"
                />
              </div>
            </div>
          </div>

          {/* Property type */}
          <div>
            <label className="block text-sm font-medium text-brown mb-2">Property Type</label>
            <select
              value={filters.propertyType}
              onChange={(e) => onFiltersChange({...filters, propertyType: e.target.value})}
              className="w-full p-2 border border-gray-300 rounded-md"
            >
              <option value="">Any</option>
              <option value="house">House</option>
              <option value="apartment">Apartment</option>
              <option value="villa">Villa</option>
              <option value="lodge">Lodge</option>
            </select>
          </div>

          {/* Bedrooms */}
          <div>
            <label className="block text-sm font-medium text-brown mb-2">Bedrooms</label>
            <select
              value={filters.bedrooms}
              onChange={(e) => onFiltersChange({...filters, bedrooms: e.target.value})}
              className="w-full p-2 border border-gray-300 rounded-md"
            >
              <option value="">Any</option>
              <option value="1">1+</option>
              <option value="2">2+</option>
              <option value="3">3+</option>
              <option value="4">4+</option>
            </select>
          </div>

          {/* More filters */}
          <div>
            <label className="block text-sm font-medium text-brown mb-2">Amenities</label>
            <div className="space-y-2">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={filters.amenities.includes("wifi")}
                  onChange={(e) => {
                    const newAmenities = e.target.checked
                      ? [...filters.amenities, "wifi"]
                      : filters.amenities.filter(a => a !== "wifi");
                    onFiltersChange({...filters, amenities: newAmenities});
                  }}
                  className="rounded text-secondary focus:ring-secondary mr-2"
                />
                <span>Wi-Fi</span>
              </label>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={filters.amenities.includes("pool")}
                  onChange={(e) => {
                    const newAmenities = e.target.checked
                      ? [...filters.amenities, "pool"]
                      : filters.amenities.filter(a => a !== "pool");
                    onFiltersChange({...filters, amenities: newAmenities});
                  }}
                  className="rounded text-secondary focus:ring-secondary mr-2"
                />
                <span>Pool</span>
              </label>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={filters.amenities.includes("ac")}
                  onChange={(e) => {
                    const newAmenities = e.target.checked
                      ? [...filters.amenities, "ac"]
                      : filters.amenities.filter(a => a !== "ac");
                    onFiltersChange({...filters, amenities: newAmenities});
                  }}
                  className="rounded text-secondary focus:ring-secondary mr-2"
                />
                <span>Air Conditioning</span>
              </label>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SearchFilters;
