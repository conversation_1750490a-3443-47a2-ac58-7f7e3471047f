import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Link } from "react-router-dom";
import PropertyCard from "../properties/PropertyCard";

const FeaturedListings = () => {
  const { data: properties, isLoading } = useQuery({
    queryKey: ["featured-properties"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("properties")
        .select("*")
        .eq("status", "approved")
        .limit(4);

      if (error) throw error;

      // Fetch reviews and calculate ratings for each property
      const propertiesWithReviews = await Promise.all(
        (data || []).map(async (property) => {
          const { data: reviewsData } = await supabase
            .from("reviews")
            .select("rating")
            .eq("property_id", property.id);

          const avgRating =
            reviewsData && reviewsData.length > 0
              ? reviewsData.reduce((sum, review) => sum + review.rating, 0) /
                reviewsData.length
              : 0;

          return {
            id: property.id,
            title: property.title,
            location: property.location,
            price: Number(property.price),
            rating: avgRating,
            reviews: reviewsData?.length || 0,
            image: property.images?.[0] || "/placeholder.svg",
            beds: Number(property.beds),
            baths: Number(property.baths),
            isSuperHost: false,
            is_dummy: property.is_dummy,
          };
        })
      );

      return propertiesWithReviews;
    },
  });

  if (isLoading) {
    return (
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-brown mb-4">
              Featured Stays
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto mb-8">
              Discover our handpicked selection of exceptional properties across
              Africa
            </p>
          </div>
          <div className="flex justify-center py-20">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent"></div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-brown mb-4">
            Featured Stays
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto mb-8">
            Discover our handpicked selection of exceptional properties across
            Africa
          </p>
          <Link
            to="/listings"
            className="inline-flex items-center text-secondary font-medium hover:text-secondary/80 transition-colors"
          >
            View all properties →
          </Link>
        </div>

        {properties && properties.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {properties.map((property) => (
              <PropertyCard key={property.id} property={property} />
            ))}
          </div>
        ) : (
          <div className="text-center py-10">
            <p className="text-lg text-gray-600 mb-6">
              No featured properties available at the moment.
            </p>
            <Link
              to="/listings/create"
              className="inline-flex items-center px-6 py-3 bg-accent text-white rounded-lg hover:bg-accent/90 transition-colors"
            >
              List Your Property
            </Link>
          </div>
        )}
      </div>
    </section>
  );
};

export default FeaturedListings;
