-- Gesco Stay Database Schema - Triggers
-- This file contains all database triggers for the Gesco Stay application

-- Trigger for new user registration
-- Note: Function must be created with SECURITY DEFINER to work on auth.users
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- Profile triggers (Note: Your project has TWO triggers on profiles table)
CREATE OR REPLACE TRIGGER profiles_updated_at_trigger
  BEFORE UPDATE ON public.profiles
  FOR EACH ROW EXECUTE FUNCTION update_profiles_updated_at();

CREATE OR REPLACE TRIGGER update_profiles_updated_at
  BEFORE UPDATE ON public.profiles
  FOR EACH ROW EXECUTE FUNCTION update_modified_column();

-- Note: No user_preferences table in current Supabase project

CREATE OR REPLACE TRIGGER update_properties_updated_at
  BEFORE UPDATE ON public.properties
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE OR <PERSON><PERSON>LACE TRIGGER update_cars_updated_at
  BEFORE UPDATE ON public.cars
  FOR EACH ROW EXECUTE FUNCTION update_modified_column();

CREATE OR REPLACE TRIGGER update_hotels_updated_at
  BEFORE UPDATE ON public.hotels
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE OR REPLACE TRIGGER update_room_types_updated_at
  BEFORE UPDATE ON public.room_types
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE OR REPLACE TRIGGER update_room_inventory_updated_at
  BEFORE UPDATE ON public.room_inventory
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Note: No update_bookings_updated_at trigger in current Supabase project

CREATE OR REPLACE TRIGGER update_car_bookings_updated_at
  BEFORE UPDATE ON public.car_bookings
  FOR EACH ROW EXECUTE FUNCTION update_modified_column();

CREATE OR REPLACE TRIGGER update_hotel_bookings_updated_at
  BEFORE UPDATE ON public.hotel_bookings
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE OR REPLACE TRIGGER update_car_insurance_options_updated_at
  BEFORE UPDATE ON public.car_insurance_options
  FOR EACH ROW EXECUTE FUNCTION update_modified_column();

CREATE OR REPLACE TRIGGER update_host_payment_methods_updated_at
  BEFORE UPDATE ON public.host_payment_methods
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE OR REPLACE TRIGGER update_payout_requests_updated_at
  BEFORE UPDATE ON public.payout_requests
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Platform earnings triggers (Note: TWO separate triggers - INSERT and UPDATE)
CREATE OR REPLACE TRIGGER platform_earnings_fee_validation
  BEFORE INSERT ON public.platform_earnings
  FOR EACH ROW EXECUTE FUNCTION ensure_correct_platform_fee();

CREATE OR REPLACE TRIGGER platform_earnings_fee_validation
  BEFORE UPDATE ON public.platform_earnings
  FOR EACH ROW EXECUTE FUNCTION ensure_correct_platform_fee();

-- Messaging trigger (Note: Different name than expected)
CREATE OR REPLACE TRIGGER trigger_update_conversation_last_message
  AFTER INSERT ON public.messages
  FOR EACH ROW EXECUTE FUNCTION update_conversation_last_message();

-- Comments for documentation
COMMENT ON TRIGGER on_auth_user_created ON auth.users IS 'Creates profile record when new user registers';
COMMENT ON TRIGGER trigger_update_conversation_last_message ON public.messages IS 'Updates conversation timestamp when new message is sent';
COMMENT ON TRIGGER platform_earnings_fee_validation ON public.platform_earnings IS 'Ensures platform fee is calculated correctly at 15%';
