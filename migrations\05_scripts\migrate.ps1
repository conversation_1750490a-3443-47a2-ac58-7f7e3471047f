# Gesco Stay Supabase Migration Script (PowerShell)
# This script orchestrates the complete migration process on Windows

param(
    [string]$NewProjectId,
    [string]$NewProjectUrl,
    [string]$NewAnonKey,
    [string]$NewServiceRoleKey
)

# Colors for output
$Red = "Red"
$Green = "Green"
$Yellow = "Yellow"
$Blue = "Blue"

function Write-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor $Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor $Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor $Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor $Red
}

function Test-Dependencies {
    Write-Info "Checking dependencies..."
    
    # Check if supabase CLI is installed
    try {
        $null = Get-Command supabase -ErrorAction Stop
    }
    catch {
        Write-Error "Supabase CLI is not installed. Please install it first."
        exit 1
    }
    
    # Check if psql is installed
    try {
        $null = Get-Command psql -ErrorAction Stop
    }
    catch {
        Write-Error "PostgreSQL client (psql) is not installed. Please install it first."
        exit 1
    }
    
    Write-Success "All dependencies are available"
}

function Get-ProjectInfo {
    if (-not $NewProjectId) {
        $script:NewProjectId = Read-Host "New Project ID"
    }
    if (-not $NewProjectUrl) {
        $script:NewProjectUrl = Read-Host "New Project URL (https://xxx.supabase.co)"
    }
    if (-not $NewAnonKey) {
        $script:NewAnonKey = Read-Host "New Anon Key"
    }
    if (-not $NewServiceRoleKey) {
        $script:NewServiceRoleKey = Read-Host "New Service Role Key" -AsSecureString
        $script:NewServiceRoleKey = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($script:NewServiceRoleKey))
    }
    
    if (-not $script:NewProjectId -or -not $script:NewProjectUrl -or -not $script:NewAnonKey -or -not $script:NewServiceRoleKey) {
        Write-Error "All project information is required"
        exit 1
    }
    
    Write-Success "Project information collected"
}

function Invoke-DatabaseSchema {
    Write-Info "Creating database schema in new project..."
    
    $MigrationRoot = Split-Path -Parent $PSScriptRoot
    $DbUrl = $script:NewProjectUrl -replace "https://", "**********************************"
    $DbUrl = $DbUrl -replace "\.supabase\.co", ".supabase.co:5432/postgres"
    
    Write-Info "Running table creation..."
    $result = psql $DbUrl -f "$MigrationRoot\01_database\schema\01_tables.sql"
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Failed to create tables"
        exit 1
    }
    
    Write-Info "Running function creation..."
    $result = psql $DbUrl -f "$MigrationRoot\01_database\schema\02_functions.sql"
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Failed to create functions"
        exit 1
    }
    
    Write-Info "Running trigger creation..."
    $result = psql $DbUrl -f "$MigrationRoot\01_database\schema\03_triggers.sql"
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Failed to create triggers"
        exit 1
    }
    
    Write-Info "Running RLS policies creation..."
    $result = psql $DbUrl -f "$MigrationRoot\01_database\schema\04_rls_policies.sql"
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Failed to create RLS policies"
        exit 1
    }
    
    Write-Info "Running indexes creation..."
    $result = psql $DbUrl -f "$MigrationRoot\01_database\schema\05_indexes.sql"
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Failed to create indexes"
        exit 1
    }
    
    Write-Info "Running views creation..."
    $result = psql $DbUrl -f "$MigrationRoot\01_database\schema\06_views.sql"
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Failed to create views"
        exit 1
    }
    
    Write-Success "Database schema created successfully"
}

function Set-StorageConfiguration {
    Write-Info "Setting up storage buckets and policies..."
    
    $MigrationRoot = Split-Path -Parent $PSScriptRoot
    $DbUrl = $script:NewProjectUrl -replace "https://", "**********************************"
    $DbUrl = $DbUrl -replace "\.supabase\.co", ".supabase.co:5432/postgres"
    
    Write-Info "Creating storage buckets..."
    $result = psql $DbUrl -f "$MigrationRoot\03_storage\buckets.sql"
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Failed to create storage buckets"
        exit 1
    }
    
    Write-Info "Creating storage policies..."
    $result = psql $DbUrl -f "$MigrationRoot\03_storage\policies.sql"
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Failed to create storage policies"
        exit 1
    }
    
    Write-Success "Storage setup completed"
}

function Deploy-EdgeFunctions {
    Write-Info "Deploying edge functions..."
    
    $MigrationRoot = Split-Path -Parent $PSScriptRoot
    $FunctionsDir = "$MigrationRoot\02_edge_functions"
    
    # Deploy each function
    Get-ChildItem -Path $FunctionsDir -Directory | ForEach-Object {
        $FuncName = $_.Name
        Write-Info "Deploying function: $FuncName"
        
        $result = supabase functions deploy $FuncName --project-ref $script:NewProjectId --source $_.FullName
        if ($LASTEXITCODE -ne 0) {
            Write-Error "Failed to deploy function: $FuncName"
            exit 1
        }
    }
    
    Write-Success "Edge functions deployed successfully"
}

function Update-Configuration {
    Write-Info "Updating configuration files..."
    
    $MigrationRoot = Split-Path -Parent $PSScriptRoot
    
    # Create environment file
    $EnvContent = @"
# New Project Environment Variables
VITE_SUPABASE_URL=$($script:NewProjectUrl)
VITE_SUPABASE_ANON_KEY=$($script:NewAnonKey)
VITE_SUPABASE_SERVICE_ROLE_KEY=$($script:NewServiceRoleKey)

# Copy these to your application's .env file
"@
    
    $EnvContent | Out-File -FilePath "$MigrationRoot\.env.new" -Encoding UTF8
    
    Write-Success "Configuration files updated"
}

function Test-Migration {
    Write-Info "Verifying migration..."
    
    # Test basic connectivity
    try {
        $headers = @{
            "apikey" = $script:NewAnonKey
            "Authorization" = "Bearer $($script:NewAnonKey)"
        }
        
        $response = Invoke-RestMethod -Uri "$($script:NewProjectUrl)/rest/v1/profiles" -Headers $headers -Method Get
        Write-Success "Basic connectivity test passed"
    }
    catch {
        Write-Error "Failed to connect to new project API: $($_.Exception.Message)"
        exit 1
    }
    
    Write-Info "Please run additional tests to verify all functionality"
}

# Main execution
Write-Info "Starting Gesco Stay Supabase Migration"
Write-Info "======================================"

Test-Dependencies
Get-ProjectInfo

Write-Warning "This will migrate your Supabase project. Make sure you have:"
Write-Warning "1. Created a new Supabase project"
Write-Warning "2. Backed up your current project"
Write-Warning "3. Have all necessary credentials"

$confirm = Read-Host "Continue with migration? (y/N)"
if ($confirm -ne "y" -and $confirm -ne "Y") {
    Write-Info "Migration cancelled"
    exit 0
}

try {
    Invoke-DatabaseSchema
    Set-StorageConfiguration
    Deploy-EdgeFunctions
    Update-Configuration
    Test-Migration
    
    Write-Success "Migration completed successfully!"
    Write-Info "Next steps:"
    Write-Info "1. Update your application's environment variables"
    Write-Info "2. Test all functionality thoroughly"
    Write-Info "3. Update DNS/domain settings if needed"
    Write-Info "4. Monitor the new project for 24-48 hours"
    
    $MigrationRoot = Split-Path -Parent $PSScriptRoot
    Write-Info "Environment variables saved to: $MigrationRoot\.env.new"
}
catch {
    Write-Error "Migration failed: $($_.Exception.Message)"
    exit 1
}
