
import { Check } from "lucide-react";

interface PropertyAmenitiesProps {
  features?: string[];
}

const PropertyAmenities = ({ features = [] }: PropertyAmenitiesProps) => {
  if (!features || features.length === 0) {
    return null;
  }

  return (
    <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
      <h2 className="text-2xl font-bold mb-4">What this place offers</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {features.map((feature, index) => (
          <div key={index} className="flex items-center">
            <Check className="w-5 h-5 mr-2 text-accent" />
            <span>{feature}</span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default PropertyAmenities;
