/**
 * Google Maps configuration and utility functions
 */

// Google Maps API configuration
export const GOOGLE_MAPS_CONFIG = {
  apiKey: import.meta.env.VITE_GOOGLE_MAPS_API_KEY,
  libraries: ['places', 'geometry'] as const,
  version: 'weekly',
  region: 'US', // You can change this based on your target region
  language: 'en', // You can change this based on your target language
};

// Default map options factory function
export const getDefaultMapOptions = (): google.maps.MapOptions => ({
  zoom: 13,
  center: { lat: 0, lng: 0 }, // Will be overridden by component
  mapTypeId: google.maps.MapTypeId.ROADMAP,
  disableDefaultUI: false,
  zoomControl: true,
  streetViewControl: true,
  fullscreenControl: true,
  mapTypeControl: false,
  gestureHandling: 'cooperative',
  styles: [
    {
      featureType: 'poi',
      elementType: 'labels',
      stylers: [{ visibility: 'off' }],
    },
  ],
});

// Location interface for our application
export interface LocationData {
  latitude: number;
  longitude: number;
  formatted_address: string;
  place_id?: string;
}

// Utility function to get user's current location
export const getCurrentLocation = (): Promise<GeolocationPosition> => {
  return new Promise((resolve, reject) => {
    if (!navigator.geolocation) {
      reject(new Error('Geolocation is not supported by this browser.'));
      return;
    }

    navigator.geolocation.getCurrentPosition(
      (position) => resolve(position),
      (error) => reject(error),
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 300000, // 5 minutes
      }
    );
  });
};

// Utility function to geocode an address
export const geocodeAddress = async (
  address: string,
  geocoder: google.maps.Geocoder
): Promise<LocationData | null> => {
  try {
    const response = await geocoder.geocode({ address });
    
    if (response.results && response.results.length > 0) {
      const result = response.results[0];
      const location = result.geometry.location;
      
      return {
        latitude: location.lat(),
        longitude: location.lng(),
        formatted_address: result.formatted_address,
        place_id: result.place_id,
      };
    }
    
    return null;
  } catch (error) {
    console.error('Geocoding error:', error);
    return null;
  }
};

// Utility function to reverse geocode coordinates
export const reverseGeocode = async (
  lat: number,
  lng: number,
  geocoder: google.maps.Geocoder
): Promise<string | null> => {
  try {
    const response = await geocoder.geocode({
      location: { lat, lng },
    });
    
    if (response.results && response.results.length > 0) {
      return response.results[0].formatted_address;
    }
    
    return null;
  } catch (error) {
    console.error('Reverse geocoding error:', error);
    return null;
  }
};

// Utility function to calculate distance between two points
export const calculateDistance = (
  lat1: number,
  lng1: number,
  lat2: number,
  lng2: number
): number => {
  const R = 6371; // Radius of the Earth in kilometers
  const dLat = (lat2 - lat1) * (Math.PI / 180);
  const dLng = (lng2 - lng1) * (Math.PI / 180);
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * (Math.PI / 180)) *
      Math.cos(lat2 * (Math.PI / 180)) *
      Math.sin(dLng / 2) *
      Math.sin(dLng / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = R * c; // Distance in kilometers
  return distance;
};

// Utility function to generate Google Maps directions URL
export const getDirectionsUrl = (
  destinationLat: number,
  destinationLng: number,
  destinationName?: string
): string => {
  // Always use coordinates to ensure exact destination
  return `https://www.google.com/maps/dir/?api=1&destination=${destinationLat},${destinationLng}`;
};

// Utility function to generate Google Maps place URL
export const getPlaceUrl = (
  lat: number,
  lng: number,
  placeName?: string
): string => {
  // Use direct coordinate URL to show exact location
  return `https://www.google.com/maps/@${lat},${lng},15z`;
};

// Check if Google Maps API is loaded
export const isGoogleMapsLoaded = (): boolean => {
  return typeof window !== 'undefined' &&
         typeof window.google !== 'undefined' &&
         typeof window.google.maps !== 'undefined';
};

// Validate Google Maps API key
export const validateApiKey = (): boolean => {
  const apiKey = GOOGLE_MAPS_CONFIG.apiKey;
  return !!(apiKey && apiKey !== 'YOUR_GOOGLE_MAPS_API_KEY_HERE' && apiKey.length > 10);
};

// Default center coordinates (you can change this to your preferred default location)
export const DEFAULT_CENTER = {
  lat: -1.2921, // Nairobi, Kenya (good default for African market)
  lng: 36.8219,
};

// Utility to suppress Google Maps deprecation warnings (optional)
export const suppressDeprecationWarnings = () => {
  const originalConsoleWarn = console.warn;
  console.warn = function(...args) {
    const message = args.join(' ');
    // Suppress specific Google Maps deprecation warnings
    if (message.includes('google.maps.places.Autocomplete is not available to new customers') ||
        message.includes('google.maps.Marker is deprecated')) {
      return; // Don't log these specific warnings
    }
    originalConsoleWarn.apply(console, args);
  };
};
