const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://mbyiidayuburouqozgfq.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1ieWlpZGF5dWJ1cm91cW96Z2ZxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczMzQ5NzI5NCwiZXhwIjoyMDQ5MDczMjk0fQ.Ey6Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8';

const supabase = createClient(supabaseUrl, supabaseKey);

async function restoreDatabase() {
  console.log('Starting database restoration...');

  try {
    // Step 1: Drop existing tables
    console.log('Dropping existing tables...');
    await supabase.rpc('exec_sql', {
      sql: `
        DROP TABLE IF EXISTS messages CASCADE;
        DROP TABLE IF EXISTS conversations CASCADE;
        DROP TABLE IF EXISTS reviews CASCADE;
        DROP TABLE IF EXISTS hotel_bookings CASCADE;
        DROP TABLE IF EXISTS car_bookings CASCADE;
        DROP TABLE IF EXISTS bookings CASCADE;
        DROP TABLE IF EXISTS room_types CASCADE;
        DROP TABLE IF EXISTS hotels CASCADE;
        DROP TABLE IF EXISTS cars CASCADE;
        DROP TABLE IF EXISTS properties CASCADE;
      `
    });

    // Step 2: Create ENUM types
    console.log('Creating ENUM types...');
    await supabase.rpc('exec_sql', {
      sql: `
        DO $$ BEGIN
            CREATE TYPE listing_status AS ENUM ('pending', 'approved', 'rejected');
        EXCEPTION
            WHEN duplicate_object THEN null;
        END $$;

        DO $$ BEGIN
            CREATE TYPE car_type AS ENUM ('sedan', 'suv', 'hatchback', 'coupe', 'convertible', 'truck', 'van', 'luxury');
        EXCEPTION
            WHEN duplicate_object THEN null;
        END $$;

        DO $$ BEGIN
            CREATE TYPE rental_duration AS ENUM ('daily', 'weekly', 'monthly');
        EXCEPTION
            WHEN duplicate_object THEN null;
        END $$;
      `
    });

    // Step 3: Create properties table
    console.log('Creating properties table...');
    await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS public.properties (
          id BIGINT PRIMARY KEY DEFAULT generate_unique_property_id(),
          title TEXT NOT NULL,
          description TEXT NOT NULL,
          location TEXT NOT NULL,
          formatted_address TEXT,
          latitude DECIMAL(10, 8),
          longitude DECIMAL(11, 8),
          price DECIMAL(10, 2) NOT NULL CHECK (price > 0),
          beds INTEGER NOT NULL CHECK (beds > 0),
          baths INTEGER NOT NULL CHECK (baths > 0),
          owner_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
          images TEXT[] NOT NULL DEFAULT '{}',
          features TEXT[] NOT NULL DEFAULT '{}',
          status listing_status NOT NULL DEFAULT 'pending',
          is_dummy BOOLEAN DEFAULT false,
          created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
          updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
        );
      `
    });

    // Step 4: Create cars table
    console.log('Creating cars table...');
    await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS public.cars (
          id BIGINT PRIMARY KEY DEFAULT generate_unique_property_id(),
          owner_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
          title TEXT NOT NULL,
          description TEXT NOT NULL,
          car_type car_type NOT NULL,
          make TEXT NOT NULL,
          model TEXT NOT NULL,
          year INTEGER NOT NULL CHECK (year > 1900 AND year <= EXTRACT(YEAR FROM CURRENT_DATE) + 1),
          location TEXT NOT NULL,
          formatted_address TEXT,
          latitude DECIMAL(10, 8),
          longitude DECIMAL(11, 8),
          images TEXT[] NOT NULL DEFAULT '{}',
          price_day DECIMAL(10, 2) NOT NULL CHECK (price_day > 0),
          price_week DECIMAL(10, 2) NOT NULL CHECK (price_week > 0),
          price_month DECIMAL(10, 2) NOT NULL CHECK (price_month > 0),
          seats INTEGER NOT NULL CHECK (seats > 0),
          transmission TEXT NOT NULL,
          fuel_type TEXT NOT NULL,
          status listing_status DEFAULT 'approved',
          is_dummy BOOLEAN DEFAULT false,
          created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
          updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
        );
      `
    });

    // Step 5: Create hotels table
    console.log('Creating hotels table...');
    await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS public.hotels (
          id BIGINT PRIMARY KEY DEFAULT generate_unique_property_id(),
          title VARCHAR(255) NOT NULL,
          description TEXT NOT NULL,
          location VARCHAR(255) NOT NULL,
          formatted_address TEXT,
          latitude DECIMAL(10, 8),
          longitude DECIMAL(11, 8),
          owner_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
          images TEXT[] DEFAULT '{}',
          amenities TEXT[] DEFAULT '{}',
          policies JSONB DEFAULT '{}',
          check_in_time TIME DEFAULT '15:00:00',
          check_out_time TIME DEFAULT '11:00:00',
          status listing_status DEFAULT 'approved',
          is_dummy BOOLEAN DEFAULT false,
          created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
          updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
        );
      `
    });

    console.log('Database restoration completed successfully!');
    console.log('Tables now use BIGINT IDs while maintaining original structure.');

  } catch (error) {
    console.error('Error during restoration:', error);
  }
}

// Check if this is being run directly
if (require.main === module) {
  restoreDatabase();
}

module.exports = { restoreDatabase };
