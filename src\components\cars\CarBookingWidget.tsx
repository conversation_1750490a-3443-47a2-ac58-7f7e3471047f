import { useState } from "react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  useInsuranceOptions,
  InsuranceOption,
} from "@/hooks/useInsuranceOptions";
import { useCheckCarAvailability } from "@/hooks/useCheckCarAvailability";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/components/layout/AuthProvider";
import { useNavigate, Link } from "react-router-dom";
import { saveBookingIntent } from "@/utils/bookingContinuation";
import { Checkbox } from "@/components/ui/checkbox";
import { Database } from "@/integrations/supabase/types";
import { PlatformFeeService } from "@/services/PlatformFeeService";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { useCarBookedDates } from "@/hooks/useBookedDates";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogFooter,
  DialogTitle,
  DialogDescription,
  DialogClose,
} from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
  Calendar,
  ShieldCheck,
  CreditCard,
  Wallet,
  TestTube,
} from "lucide-react";
import ContactHostButton from "@/components/messaging/ContactHostButton";

interface CarBookingWidgetProps {
  carId: string;
  carTitle: string;
  priceDay: number;
  priceWeek: number;
  priceMonth: number;
  hostId?: string;
  isDummy?: boolean;
}

type DurationType = Database["public"]["Enums"]["rental_duration"];

const CarBookingWidget = ({
  carId,
  carTitle,
  priceDay,
  priceWeek,
  priceMonth,
  hostId,
  isDummy = false,
}: CarBookingWidgetProps) => {
  const { toast } = useToast();
  const { user } = useAuth();
  const navigate = useNavigate();

  const [startDate, setStartDate] = useState<Date>();
  const [endDate, setEndDate] = useState<Date>();
  const [durationType, setDurationType] = useState<DurationType>("day");
  const [selectedInsurance, setSelectedInsurance] = useState<string | null>(
    null
  );
  const [showPaymentDialog, setShowPaymentDialog] = useState(false);
  const [selectedPaymentMethod, setSelectedPaymentMethod] =
    useState<string>("card");
  const [selectedBooking, setSelectedBooking] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [agreeToTerms, setAgreeToTerms] = useState(false);

  // Fetch booked dates for this car
  const { data: bookedDates = [] } = useCarBookedDates(carId);

  const { data: insuranceOptions = [] } = useInsuranceOptions();

  const { data: availabilityData } = useCheckCarAvailability(
    carId,
    startDate,
    endDate
  );

  const calculateDays = () => {
    if (!startDate || !endDate) return 0;
    const diff = endDate.getTime() - startDate.getTime();
    return Math.ceil(diff / (1000 * 60 * 60 * 24));
  };

  const calculateTotalPrice = () => {
    const days = calculateDays();
    if (days <= 0) return 0;

    let basePrice = 0;

    if (durationType === "day") {
      basePrice = days * priceDay;
    } else if (durationType === "week") {
      const weeks = Math.ceil(days / 7);
      basePrice = weeks * priceWeek;
    } else if (durationType === "month") {
      const months = Math.ceil(days / 30);
      basePrice = months * priceMonth;
    }

    // Add insurance if selected
    const selectedInsuranceOption = insuranceOptions.find(
      (option) => option.id === selectedInsurance
    );

    const insurancePrice = selectedInsuranceOption
      ? selectedInsuranceOption.price_day * days
      : 0;

    return basePrice + insurancePrice;
  };

  const handleAuthRequired = () => {
    // Save booking intent before redirecting to auth
    saveBookingIntent({
      type: "car",
      id: carId,
      startDate: startDate?.toISOString().split("T")[0],
      endDate: endDate?.toISOString().split("T")[0],
      durationType,
      totalPrice: calculateTotalPrice(),
      returnUrl: window.location.pathname,
    });

    // Navigate to auth page
    navigate("/auth");
  };

  const handleBooking = async () => {
    if (isDummy) {
      toast({
        title: "Cannot book test listing",
        description:
          "This is a test listing and cannot be booked. Please choose a real listing.",
        variant: "destructive",
      });
      return;
    }

    if (!user) {
      handleAuthRequired();
      return;
    }

    if (!startDate || !endDate) {
      toast({
        title: "Dates required",
        description: "Please select pick-up and drop-off dates",
        variant: "destructive",
      });
      return;
    }

    if (startDate >= endDate) {
      toast({
        title: "Invalid dates",
        description: "Drop-off date must be after pick-up date",
        variant: "destructive",
      });
      return;
    }

    if (!availabilityData?.available) {
      toast({
        title: "Car unavailable",
        description: "This car is not available for the selected dates",
        variant: "destructive",
      });
      return;
    }

    try {
      // Don't create booking record yet - only show payment dialog
      // Booking will be created when payment is initiated
      setShowPaymentDialog(true);
    } catch (error: any) {
      toast({
        title: "Booking failed",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const handleStripePayment = async () => {
    if (!user || !startDate || !endDate) return;

    // Check if user has either email or phone for payment processing
    if (!user.email && !user.phone) {
      toast({
        title: "Missing contact information",
        description:
          "Please add an email address or phone number to your profile to continue with payment.",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    try {
      // Create the booking record with pending status (not booked until payment succeeds)
      const { data: booking, error: bookingError } = await supabase
        .from("car_bookings")
        .insert({
          car_id: carId,
          user_id: user.id,
          start_date: startDate.toISOString().split("T")[0],
          end_date: endDate.toISOString().split("T")[0],
          duration_type: durationType,
          total_price: calculateTotalPrice(),
          status: "pending", // Only mark as confirmed after successful payment
          payment_status: "pending",
        })
        .select()
        .single();

      if (bookingError) throw bookingError;

      const { data, error } = await supabase.functions.invoke(
        "process-stripe-payment",
        {
          body: {
            carBookingId: booking.id,
            amount: calculateTotalPrice(),
            returnUrl: window.location.origin + "/car-booking-success",
            userEmail: user.email || "", // Allow empty email if user registered with phone
          },
        }
      );

      if (error) throw error;

      if (data.sessionUrl) {
        window.location.href = data.sessionUrl;
      } else {
        throw new Error("No session URL returned");
      }
    } catch (error: any) {
      console.error("Payment error:", error);
      toast({
        title: "Payment failed",
        description: error.message,
        variant: "destructive",
      });
      setIsLoading(false);
    }
  };

  const handleWavePayment = async () => {
    if (!user || !startDate || !endDate) return;

    // Check if user has either email or phone for payment processing
    if (!user.email && !user.phone) {
      toast({
        title: "Missing contact information",
        description:
          "Please add an email address or phone number to your profile to continue with payment.",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    try {
      // Create the booking record first
      const { data: booking, error: bookingError } = await supabase
        .from("car_bookings")
        .insert({
          car_id: carId,
          user_id: user.id,
          start_date: startDate.toISOString().split("T")[0],
          end_date: endDate.toISOString().split("T")[0],
          duration_type: durationType,
          total_price: calculateTotalPrice(),
        })
        .select()
        .single();

      if (bookingError) throw bookingError;

      // Use supabase.functions.invoke to call the process-wave-payment edge function (cloud)
      const { data: result, error } = await supabase.functions.invoke(
        "process-wave-payment",
        {
          body: {
            bookingId: booking.id,
            bookingType: "car",
            amount: calculateTotalPrice(),
            returnUrl: window.location.origin,
            userEmail: user.email || "", // Allow empty email if user registered with phone
            userMobile: user.phone || "",
          },
        }
      );

      if (error) {
        toast({
          title: "Wave payment failed",
          description: error.message || "Could not initiate Wave payment.",
          variant: "destructive",
        });
      } else if (result?.success && result?.checkoutUrl) {
        // Check if the URL is a deep link (wave://) and handle accordingly
        if (result.checkoutUrl.startsWith("wave://")) {
          // For deep links, create a custom payment page
          const paymentUrl = `/wave-payment?checkout_id=${
            result.checkoutId
          }&booking_id=${booking.id}&amount=${calculateTotalPrice()}`;
          window.location.href = paymentUrl;
        } else {
          // For regular URLs, redirect directly
          window.location.href = result.checkoutUrl;
        }
      } else {
        toast({
          title: "Wave payment failed",
          description: result?.error || "Could not initiate Wave payment.",
          variant: "destructive",
        });
      }
    } catch (error: unknown) {
      const err = error as Error;
      console.error("Wave payment error:", err);
      toast({
        title: "Payment failed",
        description: err.message,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto shadow-lg rounded-2xl border-0 p-0 bg-card md:sticky md:top-24">
      <CardHeader className="bg-gradient-to-r from-warm-tan/10 to-muted-teal/10 rounded-t-2xl p-6 border-b">
        {isDummy && (
          <div className="mb-4">
            <Badge className="bg-soft-orange/20 text-rich-brown flex items-center gap-1 w-fit">
              <TestTube className="w-3 h-3" />
              Display Only
            </Badge>
          </div>
        )}
        <CardTitle className="flex items-center gap-2 text-xl font-bold">
          <Calendar className="h-5 w-5 text-muted-teal" /> Booking Details
        </CardTitle>
        <CardDescription className="text-muted-foreground mt-1">
          Select your rental period and options
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6 p-6">
        <div className="flex flex-col gap-2">
          <label className="text-sm font-medium flex items-center gap-2">
            <Calendar className="h-4 w-4 text-muted-teal" /> Pick-up & Drop-off
            Dates
          </label>
          <DateRangePicker
            checkInDate={startDate}
            checkOutDate={endDate}
            onCheckInChange={setStartDate}
            onCheckOutChange={setEndDate}
            disabledDates={bookedDates}
            checkInLabel="Pick-up Date"
            checkOutLabel="Drop-off Date"
          />
        </div>

        <div className="flex flex-col gap-2">
          <label className="text-sm font-medium flex items-center gap-2">
            <Calendar className="h-4 w-4 text-muted-teal" /> Rental Duration
            Type
          </label>
          <Select
            value={durationType}
            onValueChange={(value) => setDurationType(value as DurationType)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select duration type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="day">Daily (${priceDay}/day)</SelectItem>
              <SelectItem value="week">Weekly (${priceWeek}/week)</SelectItem>
              <SelectItem value="month">
                Monthly (${priceMonth}/month)
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex flex-col gap-2">
          <label className="text-sm font-medium flex items-center gap-2">
            <ShieldCheck className="h-4 w-4 text-muted-teal" /> Insurance Option
          </label>
          <Select
            value={selectedInsurance || undefined}
            onValueChange={setSelectedInsurance}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select insurance option" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="no-insurance">No Insurance</SelectItem>
              {insuranceOptions.map((option: InsuranceOption) => (
                <SelectItem key={option.id} value={option.id}>
                  {option.name} (${option.price_day}/day)
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {startDate && endDate && (
          <div className="mt-2">
            <div
              className={`text-sm font-medium flex items-center gap-2 ${
                availabilityData?.available
                  ? "text-muted-teal"
                  : "text-destructive"
              }`}
            >
              {availabilityData?.available ? (
                <span>✅ Car is available for the selected dates</span>
              ) : (
                <span>❌ Car is not available for the selected dates</span>
              )}
            </div>
          </div>
        )}

        <div className="border-t pt-4 space-y-2">
          <div className="flex justify-between text-sm">
            <span>Days:</span>
            <span>{calculateDays()} days</span>
          </div>
          {selectedInsurance && (
            <div className="flex justify-between text-sm">
              <span>Insurance:</span>
              <span>
                $
                {(insuranceOptions.find(
                  (option) => option.id === selectedInsurance
                )?.price_day || 0) * calculateDays()}
              </span>
            </div>
          )}
          <div className="flex justify-between font-bold text-lg">
            <span>Total:</span>
            <span>${calculateTotalPrice()}</span>
          </div>
        </div>
      </CardContent>
      <CardFooter className="p-6 pt-0">
        <div className="w-full md:static fixed left-0 bottom-0 bg-card md:bg-transparent p-4 md:p-0 z-20 border-t md:border-0 space-y-3">
          <Button
            className={`w-full py-3 text-base font-semibold rounded-lg shadow-md ${
              isDummy ? "cursor-not-allowed opacity-50" : ""
            }`}
            onClick={handleBooking}
            disabled={
              isDummy ||
              !startDate ||
              !endDate ||
              !availabilityData?.available ||
              isLoading
            }
          >
            {isDummy
              ? "Test Listing - Cannot Book"
              : isLoading
              ? "Processing..."
              : "Book Now"}
          </Button>

          {hostId && (
            <ContactHostButton
              hostId={hostId}
              carId={carId}
              carTitle={carTitle}
              variant="outline"
              className="w-full"
            />
          )}
        </div>
      </CardFooter>

      {/* Payment Dialog */}
      <Dialog open={showPaymentDialog} onOpenChange={setShowPaymentDialog}>
        <DialogContent className="sm:max-w-md rounded-2xl p-0 overflow-hidden">
          <DialogHeader className="bg-warm-tan/10 p-6 border-b">
            <DialogTitle className="text-lg font-bold">
              Select Payment Method
            </DialogTitle>
            <DialogDescription className="text-muted-foreground">
              Choose how you'd like to pay for your car rental
            </DialogDescription>
          </DialogHeader>
          <div className="p-6">
            <Tabs defaultValue="card" className="w-full">
              <TabsList className="grid w-full grid-cols-2 mb-4">
                <TabsTrigger
                  value="card"
                  onClick={() => setSelectedPaymentMethod("card")}
                  className="flex items-center gap-2"
                >
                  <CreditCard className="h-4 w-4" /> Credit Card
                </TabsTrigger>
                <TabsTrigger
                  value="wave"
                  onClick={() => setSelectedPaymentMethod("wave")}
                  className="flex items-center gap-2"
                >
                  <Wallet className="h-4 w-4" /> Wave
                </TabsTrigger>
              </TabsList>

              <TabsContent value="card" className="mt-2">
                <div className="flex items-center p-4 border rounded-md mb-4 gap-2">
                  <CreditCard className="h-6 w-6 text-muted-teal" />
                  <div>
                    <p className="font-medium">Pay with card</p>
                    <p className="text-sm text-muted-foreground">
                      Secure payment via Stripe
                    </p>
                  </div>
                </div>
                <Button
                  onClick={handleStripePayment}
                  disabled={isLoading || !agreeToTerms}
                  className="w-full py-3 text-base font-semibold rounded-lg"
                >
                  {isLoading ? "Processing..." : "Continue to Payment"}
                </Button>
              </TabsContent>

              <TabsContent value="wave" className="mt-2">
                <div className="flex items-center p-4 border rounded-md mb-4 gap-2">
                  <Wallet className="h-6 w-6 text-muted-teal" />
                  <div>
                    <p className="font-medium">Pay with Wave</p>
                    <p className="text-sm text-muted-foreground">
                      Mobile money payment
                    </p>
                  </div>
                </div>
                <Button
                  onClick={handleWavePayment}
                  disabled={isLoading || !agreeToTerms}
                  className="w-full py-3 text-base font-semibold rounded-lg"
                >
                  {isLoading ? "Processing..." : "Continue with Wave"}
                </Button>
              </TabsContent>
            </Tabs>

            {/* Terms and Conditions Checkbox */}
            <div className="flex items-center space-x-2 mt-4">
              <Checkbox
                id="terms"
                checked={agreeToTerms}
                onCheckedChange={(checked) =>
                  setAgreeToTerms(checked as boolean)
                }
              />
              <label
                htmlFor="terms"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                I agree to the{" "}
                <Link to="/terms" className="text-accent hover:underline">
                  Terms & Safety Policy
                </Link>
              </label>
            </div>
          </div>
          <DialogFooter className="flex justify-between items-center bg-muted p-4 border-t">
            <DialogClose asChild>
              <Button variant="outline">Cancel</Button>
            </DialogClose>
            <p className="text-lg font-semibold">
              Total: ${calculateTotalPrice()}
            </p>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
};

export default CarBookingWidget;
