import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { CarListing } from "./useCarListings";

export const useCarDetails = (id: string | undefined) => {
  return useQuery({
    queryKey: ["car", id],
    queryFn: async () => {
      if (!id) throw new Error("Car ID is required");

      // First get the car details
      const { data: carData, error: carError } = await supabase
        .from("cars")
        .select("*")
        .eq("id", id)
        .single();

      if (carError) throw carError;

      // Then get the owner profile separately (with better error handling)
      let profileData = null;
      try {
        const { data: profile, error: profileError } = await supabase
          .from("profiles")
          .select("id, first_name, last_name, avatar_url")
          .eq("id", carData.owner_id)
          .single();

        if (!profileError) {
          profileData = profile;
        }
      } catch (profileError) {
        console.log("Could not fetch owner profile:", profileError);
        // Continue without profile data
      }

      const data = {
        ...carData,
        profiles: profileData,
      };

      return {
        id: data.id,
        title: data.title,
        description: data.description,
        car_type: data.car_type,
        location: data.location,
        latitude: data.latitude,
        longitude: data.longitude,
        formatted_address: data.formatted_address,
        price_day: data.price_day,
        price_week: data.price_week,
        price_month: data.price_month,
        make: data.make,
        model: data.model,
        year: data.year,
        images: data.images || [],
        transmission: data.transmission,
        seats: data.seats,
        fuel_type: data.fuel_type,
        features: data.features || [],
        is_dummy: data.is_dummy || false,
        owner: data.profiles
          ? {
              id: data.profiles.id,
              name:
                `${data.profiles.first_name || ""} ${
                  data.profiles.last_name || ""
                }`.trim() || "Unknown Host",
              avatar_url: data.profiles.avatar_url,
            }
          : null,
      } as CarListing & {
        owner?: {
          id: string;
          name: string;
          avatar_url: string | null;
        } | null;
      };
    },
    enabled: !!id,
  });
};
