
import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.45.0";

const SUPABASE_URL = Deno.env.get("SUPABASE_URL") || "";
const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") || "";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Create Supabase client with admin privileges
    const supabaseAdmin = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY, { 
      auth: { persistSession: false } 
    });
    
    // Get current date and time
    const now = new Date();

    // Calculate 2 hours from now for checkout reminders
    const twoHoursFromNow = new Date(now.getTime() + (2 * 60 * 60 * 1000));
    const checkoutReminderDate = twoHoursFromNow.toISOString().split('T')[0];

    // Find bookings with checkout dates today that need 2-hour reminders
    const { data: bookingsNeedingReminders, error } = await supabaseAdmin
      .from("bookings")
      .select(`
        id,
        check_out,
        profiles:user_id(email, first_name),
        properties(title, location)
      `)
      .eq("status", "confirmed")
      .eq("check_out", checkoutReminderDate);
    
    if (error) throw error;
    
    console.log(`Found ${bookingsNeedingReminders?.length || 0} bookings that need checkout reminders`);

    // Send checkout reminders for each booking
    const reminderPromises = bookingsNeedingReminders?.map(async (booking) => {
      try {
        // Call send-booking-confirmation function with checkout reminder type
        const response = await fetch(`${SUPABASE_URL}/functions/v1/send-booking-confirmation`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${SUPABASE_SERVICE_ROLE_KEY}`
          },
          body: JSON.stringify({
            bookingId: booking.id,
            type: "checkout_reminder"
          })
        });

        const data = await response.json();
        return data;
      } catch (err) {
        console.error(`Error sending checkout reminder for booking ${booking.id}:`, err);
        return { error: err };
      }
    }) || [];
    
    const reminderResults = await Promise.all(reminderPromises);
    
    return new Response(
      JSON.stringify({ 
        success: true, 
        processed: bookingsNeedingReminders?.length || 0,
        results: reminderResults 
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200,
      }
    );
  } catch (error) {
    console.error("Error processing reminders:", error);
    return new Response(
      JSON.stringify({ success: false, error: error.message }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 500,
      }
    );
  }
});
