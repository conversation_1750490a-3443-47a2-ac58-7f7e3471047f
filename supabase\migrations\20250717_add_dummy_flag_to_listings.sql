-- Add is_dummy column to all listing tables for flagging test data
-- This allows us to distinguish between real listings and test/demo data

-- Add is_dummy column to properties table
ALTER TABLE public.properties 
ADD COLUMN IF NOT EXISTS is_dummy BOOLEAN DEFAULT false;

-- Add is_dummy column to cars table
ALTER TABLE public.cars 
ADD COLUMN IF NOT EXISTS is_dummy BOOLEAN DEFAULT false;

-- Add is_dummy column to hotels table
ALTER TABLE public.hotels 
ADD COLUMN IF NOT EXISTS is_dummy BOOLEAN DEFAULT false;

-- Add indexes for better query performance when filtering dummy data
CREATE INDEX IF NOT EXISTS idx_properties_is_dummy ON public.properties(is_dummy);
CREATE INDEX IF NOT EXISTS idx_cars_is_dummy ON public.cars(is_dummy);
CREATE INDEX IF NOT EXISTS idx_hotels_is_dummy ON public.hotels(is_dummy);

-- Add comments to document the purpose
COMMENT ON COLUMN public.properties.is_dummy IS 'Flag to indicate if this is test/demo data';
COMMENT ON COLUMN public.cars.is_dummy IS 'Flag to indicate if this is test/demo data';
COMMENT ON COLUMN public.hotels.is_dummy IS 'Flag to indicate if this is test/demo data';
