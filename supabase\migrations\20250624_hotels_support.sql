-- Hotels Support Migration
-- This migration adds comprehensive hotel support with room types, inventory, and booking capabilities

-- Create hotels table
CREATE TABLE IF NOT EXISTS public.hotels (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title VARCHAR(255) NOT NULL,
  description TEXT NOT NULL,
  location VARCHAR(255) NOT NULL,
  formatted_address TEXT,
  latitude DECIMAL(10, 8),
  longitude DECIMAL(11, 8),
  owner_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  images TEXT[] DEFAULT '{}',
  amenities TEXT[] DEFAULT '{}',
  policies JSONB DEFAULT '{}',
  check_in_time TIME DEFAULT '15:00:00',
  check_out_time TIME DEFAULT '11:00:00',
  star_rating INTEGER CHECK (star_rating >= 1 AND star_rating <= 5),
  status public.listing_status DEFAULT 'approved',
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  
  -- Ensure valid coordinates
  CHECK (latitude IS NULL OR (latitude >= -90 AND latitude <= 90)),
  CHECK (longitude IS NULL OR (longitude >= -180 AND longitude <= 180))
);

-- Create room types table
CREATE TABLE IF NOT EXISTS public.room_types (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  hotel_id UUID NOT NULL REFERENCES public.hotels(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  max_occupancy INTEGER NOT NULL CHECK (max_occupancy > 0),
  bed_configuration VARCHAR(255), -- e.g., "1 King Bed", "2 Queen Beds"
  room_size_sqm INTEGER,
  amenities TEXT[] DEFAULT '{}',
  images TEXT[] DEFAULT '{}',
  base_price DECIMAL(10, 2) NOT NULL CHECK (base_price > 0),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  
  -- Ensure unique room type names per hotel
  UNIQUE(hotel_id, name)
);

-- Create room inventory table for tracking available rooms
CREATE TABLE IF NOT EXISTS public.room_inventory (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  room_type_id UUID NOT NULL REFERENCES public.room_types(id) ON DELETE CASCADE,
  room_number VARCHAR(50) NOT NULL,
  floor_number INTEGER,
  status VARCHAR(20) DEFAULT 'available' CHECK (status IN ('available', 'occupied', 'maintenance', 'out_of_order')),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  
  -- Ensure unique room numbers per room type
  UNIQUE(room_type_id, room_number)
);

-- Create hotel bookings table
CREATE TABLE IF NOT EXISTS public.hotel_bookings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  hotel_id UUID NOT NULL REFERENCES public.hotels(id) ON DELETE CASCADE,
  room_type_id UUID NOT NULL REFERENCES public.room_types(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  check_in DATE NOT NULL,
  check_out DATE NOT NULL,
  adults INTEGER NOT NULL DEFAULT 1 CHECK (adults > 0),
  children INTEGER DEFAULT 0 CHECK (children >= 0),
  rooms_count INTEGER NOT NULL DEFAULT 1 CHECK (rooms_count > 0),
  total_price DECIMAL(10, 2) NOT NULL CHECK (total_price > 0),
  status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'cancelled', 'completed')),
  payment_status VARCHAR(20) DEFAULT 'pending' CHECK (payment_status IN ('pending', 'paid', 'failed', 'refunded')),
  payment_method VARCHAR(50),
  payment_id VARCHAR(255),
  stripe_session_id VARCHAR(255),
  special_requests TEXT,
  guest_details JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  
  -- Ensure check-out is after check-in
  CHECK (check_out > check_in),
  -- Ensure booking is for future dates
  CHECK (check_in >= CURRENT_DATE)
);

-- Create room assignments table to track which specific rooms are assigned to bookings
CREATE TABLE IF NOT EXISTS public.room_assignments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  hotel_booking_id UUID NOT NULL REFERENCES public.hotel_bookings(id) ON DELETE CASCADE,
  room_inventory_id UUID NOT NULL REFERENCES public.room_inventory(id) ON DELETE CASCADE,
  assigned_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  
  -- Ensure unique room assignment per booking
  UNIQUE(hotel_booking_id, room_inventory_id)
);

-- Create seasonal pricing table for dynamic room pricing
CREATE TABLE IF NOT EXISTS public.seasonal_pricing (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  room_type_id UUID NOT NULL REFERENCES public.room_types(id) ON DELETE CASCADE,
  start_date DATE NOT NULL,
  end_date DATE NOT NULL,
  price_multiplier DECIMAL(3, 2) NOT NULL DEFAULT 1.00 CHECK (price_multiplier > 0),
  description VARCHAR(255),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  
  -- Ensure end date is after start date
  CHECK (end_date >= start_date)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_hotels_owner_id ON public.hotels(owner_id);
CREATE INDEX IF NOT EXISTS idx_hotels_location ON public.hotels(location);
CREATE INDEX IF NOT EXISTS idx_hotels_status ON public.hotels(status);
CREATE INDEX IF NOT EXISTS idx_hotels_coordinates ON public.hotels(latitude, longitude);

CREATE INDEX IF NOT EXISTS idx_room_types_hotel_id ON public.room_types(hotel_id);
CREATE INDEX IF NOT EXISTS idx_room_types_price ON public.room_types(base_price);

CREATE INDEX IF NOT EXISTS idx_room_inventory_room_type_id ON public.room_inventory(room_type_id);
CREATE INDEX IF NOT EXISTS idx_room_inventory_status ON public.room_inventory(status);

CREATE INDEX IF NOT EXISTS idx_hotel_bookings_hotel_id ON public.hotel_bookings(hotel_id);
CREATE INDEX IF NOT EXISTS idx_hotel_bookings_user_id ON public.hotel_bookings(user_id);
CREATE INDEX IF NOT EXISTS idx_hotel_bookings_dates ON public.hotel_bookings(check_in, check_out);
CREATE INDEX IF NOT EXISTS idx_hotel_bookings_status ON public.hotel_bookings(status);

CREATE INDEX IF NOT EXISTS idx_room_assignments_booking_id ON public.room_assignments(hotel_booking_id);
CREATE INDEX IF NOT EXISTS idx_room_assignments_room_id ON public.room_assignments(room_inventory_id);

CREATE INDEX IF NOT EXISTS idx_seasonal_pricing_room_type_id ON public.seasonal_pricing(room_type_id);
CREATE INDEX IF NOT EXISTS idx_seasonal_pricing_dates ON public.seasonal_pricing(start_date, end_date);

-- Create updated_at triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_hotels_updated_at BEFORE UPDATE ON public.hotels
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_room_types_updated_at BEFORE UPDATE ON public.room_types
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_room_inventory_updated_at BEFORE UPDATE ON public.room_inventory
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_hotel_bookings_updated_at BEFORE UPDATE ON public.hotel_bookings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create view for popular hotels (similar to popular_properties)
CREATE OR REPLACE VIEW public.popular_hotels AS
SELECT
  h.*,
  COALESCE(AVG(r.rating), 0) as avg_rating,
  COUNT(r.id) as review_count,
  COUNT(DISTINCT hb.id) as booking_count
FROM public.hotels h
LEFT JOIN public.reviews r ON r.hotel_id = h.id
LEFT JOIN public.hotel_bookings hb ON hb.hotel_id = h.id
WHERE h.status = 'approved'
GROUP BY h.id
ORDER BY booking_count DESC, avg_rating DESC;

-- Update reviews table to support hotels
ALTER TABLE public.reviews
ADD COLUMN IF NOT EXISTS hotel_id UUID REFERENCES public.hotels(id) ON DELETE SET NULL;

-- Update conversations table to support hotels
ALTER TABLE public.conversations
ADD COLUMN IF NOT EXISTS hotel_id UUID REFERENCES public.hotels(id) ON DELETE SET NULL;

-- Update platform_earnings table to support hotel bookings
ALTER TABLE public.platform_earnings
ADD COLUMN IF NOT EXISTS hotel_booking_id UUID REFERENCES public.hotel_bookings(id) ON DELETE SET NULL;

-- Create function to check room availability
CREATE OR REPLACE FUNCTION check_room_availability(
  p_room_type_id UUID,
  p_check_in DATE,
  p_check_out DATE,
  p_rooms_needed INTEGER DEFAULT 1
)
RETURNS INTEGER AS $$
DECLARE
  total_rooms INTEGER;
  booked_rooms INTEGER;
  available_rooms INTEGER;
BEGIN
  -- Get total rooms for this room type
  SELECT COUNT(*) INTO total_rooms
  FROM public.room_inventory ri
  WHERE ri.room_type_id = p_room_type_id
    AND ri.status = 'available';

  -- Get booked rooms for the date range
  SELECT COALESCE(SUM(hb.rooms_count), 0) INTO booked_rooms
  FROM public.hotel_bookings hb
  WHERE hb.room_type_id = p_room_type_id
    AND hb.status IN ('confirmed', 'pending')
    AND (
      (hb.check_in <= p_check_in AND hb.check_out > p_check_in) OR
      (hb.check_in < p_check_out AND hb.check_out >= p_check_out) OR
      (hb.check_in >= p_check_in AND hb.check_out <= p_check_out)
    );

  available_rooms := total_rooms - booked_rooms;

  RETURN GREATEST(0, available_rooms);
END;
$$ LANGUAGE plpgsql;
