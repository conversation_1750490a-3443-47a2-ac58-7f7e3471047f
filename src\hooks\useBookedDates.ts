import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

export const usePropertyBookedDates = (propertyId: string) => {
  return useQuery({
    queryKey: ["property-booked-dates", propertyId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("bookings")
        .select("check_in, check_out")
        .eq("property_id", propertyId)
        .eq("status", "confirmed"); // Only show confirmed bookings as unavailable

      if (error) throw error;

      // Generate array of all booked dates
      const bookedDates: Date[] = [];

      data?.forEach((booking) => {
        const checkIn = new Date(booking.check_in);
        const checkOut = new Date(booking.check_out);

        // Add all dates between check-in and check-out (inclusive)
        for (
          let date = new Date(checkIn);
          date <= checkOut;
          date.setDate(date.getDate() + 1)
        ) {
          bookedDates.push(new Date(date));
        }
      });

      return bookedDates;
    },
    enabled: !!propertyId,
  });
};

export const useCarBookedDates = (carId: string) => {
  return useQuery({
    queryKey: ["car-booked-dates", carId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("car_bookings")
        .select("start_date, end_date")
        .eq("car_id", carId)
        .eq("status", "confirmed"); // Only show confirmed bookings as unavailable

      if (error) throw error;

      // Generate array of all booked dates
      const bookedDates: Date[] = [];

      data?.forEach((booking) => {
        const startDate = new Date(booking.start_date);
        const endDate = new Date(booking.end_date);

        // Add all dates between start and end (inclusive)
        for (
          let date = new Date(startDate);
          date <= endDate;
          date.setDate(date.getDate() + 1)
        ) {
          bookedDates.push(new Date(date));
        }
      });

      return bookedDates;
    },
    enabled: !!carId,
  });
};
