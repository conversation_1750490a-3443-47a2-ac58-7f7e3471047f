import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { MessageCircle } from "lucide-react";
import { useAuth } from "@/components/layout/AuthProvider";
import { useNavigate } from "react-router-dom";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

interface ContactHostButtonProps {
  hostId: string;
  propertyId?: string;
  carId?: string;
  propertyTitle?: string;
  carTitle?: string;
  variant?: "default" | "outline" | "secondary" | "ghost" | "link" | "destructive";
  size?: "default" | "sm" | "lg" | "icon";
  className?: string;
}

const ContactHostButton = ({
  hostId,
  propertyId,
  carId,
  propertyTitle,
  carTitle,
  variant = "outline",
  size = "default",
  className = "",
}: ContactHostButtonProps) => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [isOpen, setIsOpen] = useState(false);
  const [message, setMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const handleContactHost = () => {
    if (!user) {
      toast.error("Please sign in to contact the host");
      navigate("/auth");
      return;
    }

    if (user.id === hostId) {
      toast.error("You cannot message yourself");
      return;
    }

    setIsOpen(true);
  };

  const handleSendMessage = async () => {
    if (!message.trim()) {
      toast.error("Please enter a message");
      return;
    }

    if (!user) {
      toast.error("Please sign in to send a message");
      return;
    }

    setIsLoading(true);

    try {
      // Create or get conversation
      const { data: conversationId, error: conversationError } = await supabase.rpc(
        "get_or_create_conversation",
        {
          other_user_id: hostId,
          property_id_param: propertyId || null,
          car_id_param: carId || null,
        }
      );

      if (conversationError) throw conversationError;

      // Send the message
      const { error: messageError } = await supabase.rpc("send_message", {
        conversation_id_param: conversationId,
        content_param: message.trim(),
      });

      if (messageError) throw messageError;

      toast.success("Message sent successfully!");
      setMessage("");
      setIsOpen(false);

      // Navigate to messages page
      navigate("/messages");
    } catch (error: any) {
      console.error("Error sending message:", error);
      toast.error(error.message || "Failed to send message");
    } finally {
      setIsLoading(false);
    }
  };

  const itemTitle = propertyTitle || carTitle;
  const itemType = propertyId ? "property" : "car";

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button
          variant={variant}
          size={size}
          className={className}
          onClick={handleContactHost}
        >
          <MessageCircle className="w-4 h-4 mr-2" />
          Contact Host
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Contact Host</DialogTitle>
          <DialogDescription>
            Send a message to the host about{" "}
            {itemTitle ? `"${itemTitle}"` : `this ${itemType}`}
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="message">Your message</Label>
            <Textarea
              id="message"
              placeholder="Hi! I'm interested in your property and have a few questions..."
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              rows={4}
              className="resize-none"
            />
          </div>
        </div>
        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => setIsOpen(false)}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            type="button"
            onClick={handleSendMessage}
            disabled={isLoading || !message.trim()}
          >
            {isLoading ? "Sending..." : "Send Message"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ContactHostButton;
