-- Update platform fee calculation from 10% to 15%
-- This migration ensures the platform fee function uses the correct 15% rate

-- Drop the existing function if it exists
DROP FUNCTION IF EXISTS public.calculate_platform_fee(numeric, text);

-- Recreate the function with 15% platform fee
CREATE OR REPLACE FUNCTION public.calculate_platform_fee(
  booking_amount numeric,
  booking_type text DEFAULT 'property'
) 
RETURNS NUMERIC
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  platform_fee NUMERIC;
BEGIN
  -- Calculate 15% platform fee (updated from 10%)
  platform_fee := booking_amount * 0.15;
  RETURN platform_fee;
END;
$$;

-- Add comment to document the change
COMMENT ON FUNCTION public.calculate_platform_fee(numeric, text) IS 'Calculates 15% platform fee for bookings. Updated from 10% to 15% on 2025-06-20.';

-- Create a helper function to calculate host payout (85% after 15% platform fee)
CREATE OR REPLACE FUNCTION public.calculate_host_payout(
  booking_amount numeric,
  booking_type text DEFAULT 'property'
) 
RETURNS NUMERIC
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  host_payout NUMERIC;
BEGIN
  -- Calculate host payout: 85% of booking amount (after 15% platform fee)
  host_payout := booking_amount * 0.85;
  RETURN host_payout;
END;
$$;

-- Add comment to document the helper function
COMMENT ON FUNCTION public.calculate_host_payout(numeric, text) IS 'Calculates host payout amount (85% of booking amount after 15% platform fee).';
