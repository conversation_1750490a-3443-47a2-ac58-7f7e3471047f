-- Gesco Stay Migration Verification Script
-- Run this script after migration to verify everything was migrated correctly

-- Check all tables exist
SELECT 'Tables Check' as verification_type, 
       COUNT(*) as expected_count,
       (SELECT COUNT(*) FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name IN (
          'profiles', 'properties', 'cars', 'hotels',
          'room_types', 'room_inventory', 'seasonal_pricing', 'bookings',
          'car_bookings', 'hotel_bookings', 'room_assignments', 'reviews',
          'car_insurance_options', 'conversations', 'messages',
          'host_payment_methods', 'payout_requests', 'platform_earnings',
          'payment_logs', 'admin_users', 'admin_sessions', 'checkout_reminders_sent'
        )) as actual_count
FROM (SELECT 22 as expected) t;

-- Check custom types exist
SELECT 'Custom Types Check' as verification_type,
       4 as expected_count,
       COUNT(*) as actual_count
FROM pg_type 
WHERE typname IN ('user_role', 'listing_status', 'rental_duration', 'car_type');

-- Check user roles
SELECT 'User Roles Check' as verification_type,
       5 as expected_count,
       COUNT(*) as actual_count
FROM pg_enum e
JOIN pg_type t ON e.enumtypid = t.oid
WHERE t.typname = 'user_role';

-- Check functions exist
SELECT 'Functions Check' as verification_type,
       COUNT(*) as expected_count,
       (SELECT COUNT(*) FROM information_schema.routines 
        WHERE routine_schema = 'public' 
        AND routine_name IN (
          'handle_new_user', 'handle_new_user_manual', 'is_admin',
          'check_property_availability', 'check_car_availability', 'check_room_availability',
          'calculate_platform_fee', 'calculate_host_payout', 'ensure_correct_platform_fee',
          'get_or_create_conversation', 'send_message', 'mark_messages_as_read',
          'update_conversation_last_message', 'get_user_conversations',
          'create_initial_super_admin', 'authenticate_admin', 'validate_admin_session',
          'logout_admin', 'get_host_payout_requests', 'get_payout_requests_with_details',
          'get_first_user_id', 'schedule_checkout_reminders'
        )) as actual_count
FROM (SELECT 22 as expected) t;

-- Check triggers exist
SELECT 'Triggers Check' as verification_type,
       COUNT(*) as expected_count,
       (SELECT COUNT(*) FROM information_schema.triggers 
        WHERE trigger_schema = 'public' 
        AND trigger_name LIKE '%updated_at%'
        OR trigger_name = 'update_conversation_last_message_trigger'
        OR trigger_name = 'ensure_platform_fee_trigger') as actual_count
FROM (SELECT 15 as expected) t;

-- Check RLS is enabled on all tables
SELECT 'RLS Enabled Check' as verification_type,
       COUNT(*) as tables_with_rls_enabled
FROM pg_class c
JOIN pg_namespace n ON c.relnamespace = n.oid
WHERE n.nspname = 'public'
AND c.relkind = 'r'
AND c.relrowsecurity = true;

-- Check storage buckets exist
SELECT 'Storage Buckets Check' as verification_type,
       5 as expected_count,
       COUNT(*) as actual_count
FROM storage.buckets
WHERE name IN ('property-images', 'car-images', 'hotel-images', 'profile-avatars', 'documents');

-- Check indexes exist
SELECT 'Indexes Check' as verification_type,
       COUNT(*) as total_indexes
FROM pg_indexes
WHERE schemaname = 'public';

-- Sample data verification
SELECT 'Sample Data Check' as verification_type,
       'profiles' as table_name,
       COUNT(*) as record_count
FROM public.profiles
UNION ALL
SELECT 'Sample Data Check', 'properties', COUNT(*) FROM public.properties
UNION ALL
SELECT 'Sample Data Check', 'cars', COUNT(*) FROM public.cars
UNION ALL
SELECT 'Sample Data Check', 'hotels', COUNT(*) FROM public.hotels
UNION ALL
SELECT 'Sample Data Check', 'bookings', COUNT(*) FROM public.bookings
UNION ALL
SELECT 'Sample Data Check', 'car_bookings', COUNT(*) FROM public.car_bookings
UNION ALL
SELECT 'Sample Data Check', 'hotel_bookings', COUNT(*) FROM public.hotel_bookings;

-- Check for orphaned records
SELECT 'Data Integrity Check' as verification_type,
       'Orphaned bookings' as issue,
       COUNT(*) as count
FROM public.bookings b
LEFT JOIN public.properties p ON b.property_id = p.id
WHERE b.property_id IS NOT NULL AND p.id IS NULL
UNION ALL
SELECT 'Data Integrity Check', 'Orphaned car bookings', COUNT(*)
FROM public.car_bookings cb
LEFT JOIN public.cars c ON cb.car_id = c.id
WHERE c.id IS NULL
UNION ALL
SELECT 'Data Integrity Check', 'Orphaned hotel bookings', COUNT(*)
FROM public.hotel_bookings hb
LEFT JOIN public.hotels h ON hb.hotel_id = h.id
WHERE h.id IS NULL;

-- Final summary
SELECT 'Migration Summary' as verification_type,
       'Complete' as status,
       now() as verified_at;
