-- Remove custom email_otps table since we're using Supabase's built-in email verification
-- This migration cleans up the custom OTP system in favor of Supabase's native email verification

-- Drop the cleanup function first
DROP FUNCTION IF EXISTS cleanup_expired_otps();

-- Drop the email_otps table
DROP TABLE IF EXISTS email_otps;

-- Remove any scheduled jobs related to OTP cleanup (if they exist)
-- Note: This requires pg_cron extension, but we'll handle it gracefully
DO $$
BEGIN
    -- Try to unschedule the cleanup job if it exists
    IF EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'pg_cron') THEN
        PERFORM cron.unschedule('cleanup-expired-otps');
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        -- Ignore errors if pg_cron is not available or job doesn't exist
        NULL;
END $$;

COMMENT ON COLUMN profiles.email_verified IS 'Whether the user email has been verified via Supabase auth';
COMMENT ON COLUMN profiles.phone_verified IS 'Whether the user phone has been verified via Supabase auth';
COMMENT ON COLUMN profiles.email_verified_at IS 'When the email was verified via Supabase auth';
COMMENT ON COLUMN profiles.phone_verified_at IS 'When the phone was verified via Supabase auth';
