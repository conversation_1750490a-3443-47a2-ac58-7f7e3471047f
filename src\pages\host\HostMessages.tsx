import { useState, useEffect } from "react";
import { useAuth } from "@/components/layout/AuthProvider";
import { supabase } from "@/integrations/supabase/client";
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ScrollArea } from "@/components/ui/scroll-area";
import { format } from "date-fns";
import { toast } from "sonner";

type Message = {
  id: string;
  sender_id: string;
  content: string;
  created_at: string;
  read_at: string | null;
};

type Conversation = {
  id: string;
  other_user_id: string;
  other_user_name: string;
  other_user_avatar: string | null;
  property_id: string | null;
  property_title: string | null;
  car_id: string | null;
  car_title: string | null;
  last_message_content: string | null;
  last_message_at: string;
  last_message_sender_id: string | null;
  unread_count: number;
  created_at: string;
};

// Real data will be fetched from Supabase

const HostMessages = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);
  const [conversation, setConversation] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState("");
  const [sendingMessage, setSendingMessage] = useState(false);

  useEffect(() => {
    if (user) {
      fetchConversations();
    }
  }, [user]);

  const fetchConversations = async () => {
    if (!user) return;

    setLoading(true);
    try {
      // Fetch conversations where the current user is a participant
      const { data: conversations, error } = await supabase
        .from("conversations")
        .select(
          `
          id,
          created_at,
          updated_at,
          participant_1_id,
          participant_2_id,
          profiles!conversations_participant_1_id_fkey(id, first_name, last_name, avatar_url),
          profiles!conversations_participant_2_id_fkey(id, first_name, last_name, avatar_url),
          messages(
            id,
            content,
            created_at,
            read,
            sender_id
          )
        `
        )
        .or(`participant_1_id.eq.${user.id},participant_2_id.eq.${user.id}`)
        .order("updated_at", { ascending: false });

      if (error) throw error;

      // Transform conversations into contacts
      const contactsList: Contact[] =
        conversations?.map((conv) => {
          // Determine the other participant (not the current user)
          const otherParticipant =
            conv.participant_1_id === user.id
              ? conv.profiles[1]
              : conv.profiles[0];

          // Get the latest message
          const latestMessage = conv.messages?.[0];

          // Count unread messages from the other participant
          const unreadCount =
            conv.messages?.filter(
              (msg) => msg.sender_id !== user.id && !msg.read
            ).length || 0;

          return {
            id: conv.id,
            name:
              `${otherParticipant?.first_name || ""} ${
                otherParticipant?.last_name || ""
              }`.trim() || "Unknown User",
            avatar_url: otherParticipant?.avatar_url || null,
            last_message: latestMessage?.content || "No messages yet",
            last_message_time: latestMessage
              ? new Date(latestMessage.created_at)
              : new Date(conv.created_at),
            unread_count: unreadCount,
            created_at: conv.created_at,
          };
        }) || [];

      setContacts(contactsList);

      // Select the first contact if available
      if (contactsList.length > 0 && !selectedContact) {
        setSelectedContact(contactsList[0]);
      }
    } catch (error) {
      console.error("Error fetching conversations:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (selectedContact) {
      loadConversation(selectedContact.id);
    }
  }, [selectedContact]);

  const loadConversation = async (conversationId: string) => {
    try {
      // Fetch messages for the selected conversation
      const { data: messages, error } = await supabase
        .from("messages")
        .select("*")
        .eq("conversation_id", conversationId)
        .order("created_at", { ascending: true });

      if (error) throw error;

      // Transform messages to match our Message interface
      const transformedMessages: Message[] =
        messages?.map((msg) => ({
          id: msg.id,
          sender_id: msg.sender_id,
          receiver_id: msg.receiver_id,
          content: msg.content,
          timestamp: new Date(msg.created_at),
          read: msg.read,
        })) || [];

      setConversation(transformedMessages);

      // Mark messages as read if they're from the other participant
      const unreadMessages = messages?.filter(
        (msg) => msg.sender_id !== user?.id && !msg.read
      );

      if (unreadMessages && unreadMessages.length > 0) {
        await supabase
          .from("messages")
          .update({ read: true })
          .in(
            "id",
            unreadMessages.map((msg) => msg.id)
          );
      }
    } catch (error) {
      console.error("Error loading conversation:", error);
    }
  };

  const handleSendMessage = async () => {
    if (!newMessage.trim() || !selectedContact || !user) return;

    setSendingMessage(true);

    try {
      // Find the other participant in the conversation
      const { data: conversationData, error: convError } = await supabase
        .from("conversations")
        .select("participant_1_id, participant_2_id")
        .eq("id", selectedContact.id)
        .single();

      if (convError) throw convError;

      const receiverId =
        conversationData.participant_1_id === user.id
          ? conversationData.participant_2_id
          : conversationData.participant_1_id;

      // Send the message to the database
      const { data: messageData, error: messageError } = await supabase
        .from("messages")
        .insert({
          conversation_id: selectedContact.id,
          sender_id: user.id,
          receiver_id: receiverId,
          content: newMessage.trim(),
          read: false,
        })
        .select()
        .single();

      if (messageError) throw messageError;

      // Add the new message to the conversation
      const newMsg: Message = {
        id: messageData.id,
        sender_id: user.id,
        receiver_id: receiverId,
        content: newMessage.trim(),
        timestamp: new Date(messageData.created_at),
        read: false,
      };

      setConversation([...conversation, newMsg]);
      setNewMessage("");

      // Update the conversation's updated_at timestamp
      await supabase
        .from("conversations")
        .update({ updated_at: new Date().toISOString() })
        .eq("id", selectedContact.id);
    } catch (error) {
      console.error("Error sending message:", error);
    } finally {
      setSendingMessage(false);
    }
  };

  const formatTime = (date: Date) => {
    return format(date, "h:mm a");
  };

  const formatDate = (date: Date) => {
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return "Today";
    } else if (date.toDateString() === yesterday.toDateString()) {
      return "Yesterday";
    } else {
      return format(date, "MMM d, yyyy");
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center p-8">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-800"></div>
      </div>
    );
  }

  return (
    <div>
      <h1 className="text-2xl font-bold mb-6">Messages</h1>

      <div className="flex flex-col md:flex-row h-[calc(100vh-220px)] border rounded-lg overflow-hidden">
        {/* Contacts list */}
        <div className="w-full md:w-1/3 border-r">
          <div className="p-3 border-b">
            <Input placeholder="Search messages..." className="w-full" />
          </div>

          <ScrollArea className="h-[calc(100%-52px)]">
            {contacts.map((contact) => (
              <div
                key={contact.id}
                className={`flex items-center p-3 gap-3 cursor-pointer hover:bg-gray-100 ${
                  selectedContact?.id === contact.id ? "bg-gray-100" : ""
                }`}
                onClick={() => setSelectedContact(contact)}
              >
                <Avatar>
                  <AvatarImage src={contact.avatar_url || ""} />
                  <AvatarFallback>{contact.name.charAt(0)}</AvatarFallback>
                </Avatar>

                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <h3 className="font-medium truncate">{contact.name}</h3>
                    <span className="text-xs text-gray-500">
                      {formatDate(contact.last_message_time)}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 truncate">
                    {contact.last_message}
                  </p>
                </div>

                {contact.unread_count > 0 && (
                  <span className="bg-blue-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                    {contact.unread_count}
                  </span>
                )}
              </div>
            ))}
          </ScrollArea>
        </div>

        {/* Conversation */}
        <div className="flex-1 flex flex-col">
          {selectedContact ? (
            <>
              {/* Header */}
              <div className="p-3 border-b flex items-center gap-3">
                <Avatar>
                  <AvatarImage src={selectedContact.avatar_url || ""} />
                  <AvatarFallback>
                    {selectedContact.name.charAt(0)}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <h2 className="font-medium">{selectedContact.name}</h2>
                  <p className="text-xs text-gray-500">Guest</p>
                </div>
              </div>

              {/* Messages */}
              <ScrollArea className="flex-1 p-4">
                <div className="space-y-4">
                  {conversation.map((message) => (
                    <div
                      key={message.id}
                      className={`flex ${
                        message.sender_id === user?.id ||
                        message.sender_id === "host-1"
                          ? "justify-end"
                          : "justify-start"
                      }`}
                    >
                      <div
                        className={`max-w-[70%] rounded-lg p-3 ${
                          message.sender_id === user?.id ||
                          message.sender_id === "host-1"
                            ? "bg-blue-500 text-white"
                            : "bg-gray-100"
                        }`}
                      >
                        <div className="text-sm">{message.content}</div>
                        <div
                          className={`text-xs mt-1 ${
                            message.sender_id === user?.id ||
                            message.sender_id === "host-1"
                              ? "text-blue-50"
                              : "text-gray-500"
                          }`}
                        >
                          {formatTime(message.timestamp)}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>

              {/* Message Input */}
              <div className="p-3 border-t">
                <div className="flex gap-2">
                  <Input
                    placeholder="Type a message..."
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === "Enter" && !e.shiftKey) {
                        e.preventDefault();
                        handleSendMessage();
                      }
                    }}
                  />
                  <Button
                    disabled={!newMessage.trim() || sendingMessage}
                    onClick={handleSendMessage}
                  >
                    {sendingMessage ? "Sending..." : "Send"}
                  </Button>
                </div>
              </div>
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center text-gray-500">
              <div className="text-center">
                <p>Select a conversation to start messaging</p>
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="mt-4 text-center text-sm text-muted-foreground">
        <p>
          This is a UI demonstration. Real messaging functionality will be
          implemented in the future with database integration and real-time
          updates.
        </p>
      </div>
    </div>
  );
};

export default HostMessages;
