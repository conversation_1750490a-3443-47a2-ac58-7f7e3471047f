import { Link } from "react-router-dom";

const Footer = () => {
  return (
    <footer className="bg-gray-900 text-white py-12 mt-16">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <h3 className="text-xl font-bold mb-4">Gesco Stay</h3>
            <p className="text-gray-300">
              Discover unique stays and car rentals across Africa - The Spirit
              of African Hospitality. Book, Stay, Belong.
            </p>
          </div>
          <div>
            <h4 className="font-medium mb-4">Navigation</h4>
            <ul className="space-y-2">
              <li>
                <Link to="/" className="text-gray-300 hover:text-white">
                  Home
                </Link>
              </li>
              <li>
                <Link to="/listings" className="text-gray-300 hover:text-white">
                  Properties
                </Link>
              </li>
              <li>
                <Link to="/cars" className="text-gray-300 hover:text-white">
                  Car Rentals
                </Link>
              </li>
              <li>
                <Link to="#" className="text-gray-300 hover:text-white">
                  Contact
                </Link>
              </li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium mb-4">Host</h4>
            <ul className="space-y-2">
              <li>
                <Link
                  to="/listings/create"
                  className="text-gray-300 hover:text-white"
                >
                  List Property
                </Link>
              </li>
              <li>
                <Link
                  to="/cars/create"
                  className="text-gray-300 hover:text-white"
                >
                  List Car
                </Link>
              </li>
              <li>
                <a href="#" className="text-gray-300 hover:text-white">
                  Resources
                </a>
              </li>
              <li>
                <Link
                  to="/community"
                  className="text-gray-300 hover:text-white"
                >
                  Community
                </Link>
              </li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium mb-4">Support</h4>
            <ul className="space-y-2">
              <li>
                <a href="#" className="text-gray-300 hover:text-white">
                  Help Center
                </a>
              </li>
              <li>
                <Link to="/safety" className="text-gray-300 hover:text-white">
                  Safety
                </Link>
              </li>
              <li>
                <Link to="/terms" className="text-gray-300 hover:text-white">
                  Terms of Service
                </Link>
              </li>
              <li>
                <Link to="/privacy" className="text-gray-300 hover:text-white">
                  Privacy Policy
                </Link>
              </li>
            </ul>
          </div>
        </div>
        <div className="mt-12 pt-8 border-t border-gray-800 text-center text-gray-400">
          <p>© 2025 Gesco Group. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
