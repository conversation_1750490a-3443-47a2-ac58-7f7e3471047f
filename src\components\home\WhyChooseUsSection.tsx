import {
  Shield,
  Headphones,
  CreditCard,
  CheckCircle,
  Sparkles,
  Globe,
} from "lucide-react";
import { <PERSON> } from "react-router-dom";

const WhyChooseUsSection = () => {
  const features = [
    {
      icon: <Shield className="w-8 h-8 text-accent" />,
      title: "100% Verified",
      description:
        "Every property and host goes through our rigorous verification process for your safety and peace of mind.",
    },
    {
      icon: <Headphones className="w-8 h-8 text-secondary" />,
      title: "24/7 Support",
      description:
        "Our dedicated support team is available around the clock to assist you throughout your journey.",
    },
    {
      icon: <CreditCard className="w-8 h-8 text-accent" />,
      title: "Secure Payments",
      description:
        "Your payments are protected with bank-level security and encrypted transactions.",
    },
    {
      icon: <CheckCircle className="w-8 h-8 text-secondary" />,
      title: "Quality Guarantee",
      description:
        "We ensure every listing meets our high standards for cleanliness, safety, and authenticity.",
    },
    {
      icon: <Sparkles className="w-8 h-8 text-accent" />,
      title: "Curated Experiences",
      description:
        "Handpicked properties and experiences that showcase the best of African hospitality.",
    },
    {
      icon: <Globe className="w-8 h-8 text-secondary" />,
      title: "Local Expertise",
      description:
        "Built by locals, for locals and global travelers seeking authentic African experiences.",
    },
  ];

  return (
    <section className="py-20 bg-gradient-to-r from-accent/5 via-white to-secondary/5">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <div className="inline-flex items-center px-4 py-2 bg-accent/10 text-accent rounded-full text-sm font-medium mb-4">
            🚀 Launching Soon - Be Among the First!
          </div>
          <h2 className="text-3xl lg:text-4xl font-bold text-foreground mb-4">
            Why Choose Gescostay?
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            We're building Africa's most trusted travel platform with safety,
            authenticity, and exceptional service at our core
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div
              key={index}
              className="text-center p-8 bg-card rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 border border-border group"
            >
              <div className="flex justify-center mb-6">
                <div className="p-4 bg-muted rounded-full group-hover:bg-accent/10 transition-colors duration-300">
                  {feature.icon}
                </div>
              </div>
              <h3 className="text-xl font-bold text-foreground mb-4">
                {feature.title}
              </h3>
              <p className="text-muted-foreground leading-relaxed">
                {feature.description}
              </p>
            </div>
          ))}
        </div>

        {/* Launch announcement */}
        <div className="mt-16 text-center">
          <div className="bg-gradient-to-r from-accent/10 via-secondary/10 to-accent/10 rounded-2xl p-8 max-w-4xl mx-auto">
            <h3 className="text-2xl font-bold text-foreground mb-4">
              🎉 Join Our Launch Community
            </h3>
            <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
              Be the first to experience the future of travel in Africa. Early
              members get exclusive benefits and special launch offers.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Link
                to="/listings"
                className="bg-gradient-to-r from-accent to-secondary hover:from-accent/90 hover:to-secondary/90 text-white px-8 py-4 text-lg font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 inline-block text-center"
              >
                Become a Guest
              </Link>
              <Link
                to="/listings/create"
                className="border-2 border-secondary text-secondary hover:bg-secondary hover:text-white px-8 py-4 text-lg font-semibold rounded-xl transition-all duration-300 inline-block text-center"
              >
                Become a Host
              </Link>
            </div>
          </div>
        </div>

        {/* Trust indicators for new platform */}
        <div className="mt-12 text-center">
          <div className="flex flex-wrap justify-center items-center gap-8 opacity-70">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-muted-teal rounded-full flex items-center justify-center">
                <span className="text-white text-xs font-bold">✓</span>
              </div>
              <span className="text-sm font-medium text-muted-foreground">
                Locally Owned & Operated
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-secondary rounded-full flex items-center justify-center">
                <span className="text-white text-xs font-bold">🔒</span>
              </div>
              <span className="text-sm font-medium text-muted-foreground">
                SSL Secured Platform
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-rich-brown rounded-full flex items-center justify-center">
                <span className="text-white text-xs font-bold">AF</span>
              </div>
              <span className="text-sm font-medium text-muted-foreground">
                Proudly African
              </span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default WhyChooseUsSection;
