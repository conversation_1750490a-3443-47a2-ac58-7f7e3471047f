-- Fix unique constraints and validation for profiles table
-- This migration addresses issues with duplicate emails and phone numbers

-- 1. Add unique constraints for email and phone_number
-- First, let's check if there are any duplicates and clean them up

-- Create a backup table for safety
CREATE TABLE IF NOT EXISTS profiles_backup AS SELECT * FROM profiles;

-- Remove any duplicate emails (keep the first one created)
WITH duplicates AS (
  SELECT id, email, 
         ROW_NUMBER() OVER (PARTITION BY email ORDER BY created_at ASC) as rn
  FROM profiles 
  WHERE email IS NOT NULL AND email != ''
)
UPDATE profiles 
SET email = NULL 
WHERE id IN (
  SELECT id FROM duplicates WHERE rn > 1
);

-- Remove any duplicate phone numbers (keep the first one created)
WITH duplicates AS (
  SELECT id, phone_number, 
         ROW_NUMBER() OVER (PARTITION BY phone_number ORDER BY created_at ASC) as rn
  FROM profiles 
  WHERE phone_number IS NOT NULL AND phone_number != ''
)
UPDATE profiles 
SET phone_number = NULL 
WHERE id IN (
  SELECT id FROM duplicates WHERE rn > 1
);

-- Now add unique constraints (only if they don't exist)
DO $$
BEGIN
  -- Add unique constraint for email if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints
    WHERE constraint_name = 'unique_email'
    AND table_name = 'profiles'
    AND table_schema = 'public'
  ) THEN
    ALTER TABLE profiles
    ADD CONSTRAINT unique_email
    UNIQUE (email) DEFERRABLE INITIALLY DEFERRED;
  END IF;

  -- Add unique constraint for phone_number if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints
    WHERE constraint_name = 'unique_phone_number'
    AND table_name = 'profiles'
    AND table_schema = 'public'
  ) THEN
    ALTER TABLE profiles
    ADD CONSTRAINT unique_phone_number
    UNIQUE (phone_number) DEFERRABLE INITIALLY DEFERRED;
  END IF;
END $$;

-- 2. Create validation functions for registration

-- Function to check if email already exists
CREATE OR REPLACE FUNCTION check_email_exists(email_to_check TEXT)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM profiles 
    WHERE email = email_to_check 
    AND email IS NOT NULL 
    AND email != ''
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if phone number already exists
CREATE OR REPLACE FUNCTION check_phone_exists(phone_to_check TEXT)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM profiles 
    WHERE phone_number = phone_to_check 
    AND phone_number IS NOT NULL 
    AND phone_number != ''
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. Update the handle_new_user_manual function to handle conflicts gracefully
CREATE OR REPLACE FUNCTION handle_new_user_manual(
  user_id UUID,
  first_name TEXT DEFAULT '',
  last_name TEXT DEFAULT '',
  email TEXT DEFAULT '',
  phone_number TEXT DEFAULT '',
  primary_login_method TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  final_email TEXT;
  final_phone TEXT;
BEGIN
  -- Clean up empty strings to NULL
  final_email := CASE WHEN email = '' THEN NULL ELSE email END;
  final_phone := CASE WHEN phone_number = '' THEN NULL ELSE phone_number END;
  
  -- Check for existing email
  IF final_email IS NOT NULL AND check_email_exists(final_email) THEN
    RAISE EXCEPTION 'EMAIL_ALREADY_EXISTS: An account with this email already exists';
  END IF;
  
  -- Check for existing phone number
  IF final_phone IS NOT NULL AND check_phone_exists(final_phone) THEN
    RAISE EXCEPTION 'PHONE_ALREADY_EXISTS: An account with this phone number already exists';
  END IF;

  INSERT INTO public.profiles (
    id,
    first_name,
    last_name,
    phone_number,
    email,
    primary_login_method
  )
  VALUES (
    user_id,
    COALESCE(first_name, ''),
    COALESCE(last_name, ''),
    final_phone,
    final_email,
    primary_login_method
  )
  ON CONFLICT (id) DO UPDATE SET
    first_name = COALESCE(EXCLUDED.first_name, profiles.first_name),
    last_name = COALESCE(EXCLUDED.last_name, profiles.last_name),
    phone_number = COALESCE(EXCLUDED.phone_number, profiles.phone_number),
    email = COALESCE(EXCLUDED.email, profiles.email),
    primary_login_method = COALESCE(EXCLUDED.primary_login_method, profiles.primary_login_method),
    updated_at = now();

  RETURN user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. Grant necessary permissions
GRANT EXECUTE ON FUNCTION check_email_exists(TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION check_phone_exists(TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION handle_new_user_manual(UUID, TEXT, TEXT, TEXT, TEXT, TEXT) TO authenticated;
