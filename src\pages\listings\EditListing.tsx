import { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/components/layout/AuthProvider";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { toast } from "@/hooks/use-toast";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import FeatureSelector from "@/components/features/FeatureSelector";
import { LocationPicker } from "@/components/maps/LocationPicker";
import { LocationData } from "@/lib/google-maps";

const formSchema = z.object({
  title: z.string().min(1, "Title is required"),
  description: z.string().min(10, "Description must be at least 10 characters"),
  location: z.string().min(1, "Location is required"),
  price: z.number().min(1, "Price must be greater than 0"),
  beds: z.number().min(1, "Number of beds must be at least 1"),
  baths: z.number().min(1, "Number of baths must be at least 1"),
  images: z.any().optional(),
});

type EditListingForm = z.infer<typeof formSchema>;

type EditListingFormData = EditListingForm & {
  features: string[];
};

const EditListing = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const { user } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedFeatures, setSelectedFeatures] = useState<string[]>([]);
  const [selectedLocation, setSelectedLocation] = useState<LocationData | null>(null);
  const [existingImages, setExistingImages] = useState<string[]>([]);

  const form = useForm<EditListingForm>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: "",
      description: "",
      location: "",
      price: 0,
      beds: 1,
      baths: 1,
    },
  });

  useEffect(() => {
    if (!user || !id) {
      navigate("/auth");
      return;
    }
    fetchPropertyData();
  }, [user, id, navigate]);

  const fetchPropertyData = async () => {
    try {
      const { data: property, error } = await supabase
        .from("properties")
        .select("*")
        .eq("id", id)
        .eq("owner_id", user?.id)
        .single();

      if (error) throw error;

      if (!property) {
        toast({
          variant: "destructive",
          title: "Error",
          description: "Property not found or you don't have permission to edit it.",
        });
        navigate("/host/listings");
        return;
      }

      // Populate form with existing data
      form.reset({
        title: property.title,
        description: property.description,
        location: property.location,
        price: property.price,
        beds: property.beds,
        baths: property.baths,
      });

      setSelectedFeatures(property.features || []);
      setExistingImages(property.images || []);

      if (property.latitude && property.longitude) {
        setSelectedLocation({
          latitude: property.latitude,
          longitude: property.longitude,
          formatted_address: property.formatted_address || property.location,
        });
      }
    } catch (error) {
      console.error("Error fetching property:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to load property data.",
      });
      navigate("/host/listings");
    } finally {
      setIsLoading(false);
    }
  };

  const uploadImages = async (files: FileList): Promise<string[]> => {
    const imageUrls: string[] = [];

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const fileExt = file.name.split('.').pop();
      const fileName = `${Math.random()}.${fileExt}`;
      const filePath = `property-images/${fileName}`;

      const { error: uploadError } = await supabase.storage
        .from('property-images')
        .upload(filePath, file);

      if (uploadError) {
        throw uploadError;
      }

      const { data } = supabase.storage
        .from('property-images')
        .getPublicUrl(filePath);

      imageUrls.push(data.publicUrl);
    }

    return imageUrls;
  };

  const onSubmit = async (data: EditListingForm) => {
    if (!user) return;

    setIsSubmitting(true);
    try {
      let imageUrls = existingImages;

      // Upload new images if provided
      if (data.images && data.images.length > 0) {
        const newImageUrls = await uploadImages(data.images);
        imageUrls = [...existingImages, ...newImageUrls];
      }

      // Update the property listing
      const { error } = await supabase
        .from('properties')
        .update({
          title: data.title,
          description: data.description,
          location: data.location,
          latitude: selectedLocation?.latitude || null,
          longitude: selectedLocation?.longitude || null,
          formatted_address: selectedLocation?.formatted_address || null,
          price: data.price,
          beds: data.beds,
          baths: data.baths,
          images: imageUrls,
          features: selectedFeatures,
          updated_at: new Date().toISOString(),
        })
        .eq('id', id)
        .eq('owner_id', user.id);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Your property has been updated successfully!",
      });

      navigate("/host/listings");
    } catch (error) {
      console.error("Error updating property:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Something went wrong. Please try again.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-800"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <main className="container mx-auto px-4 py-8">
        <Card className="max-w-2xl mx-auto">
          <CardHeader>
            <CardTitle>Edit Property Listing</CardTitle>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Property Title</FormLabel>
                      <FormControl>
                        <Input placeholder="Beautiful 2-bedroom apartment" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Describe your property..."
                          className="min-h-[100px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="location"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Location</FormLabel>
                      <FormControl>
                        <Input placeholder="City, Country" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div>
                  <label className="text-sm font-medium">Select Location on Map</label>
                  <LocationPicker
                    onLocationSelect={setSelectedLocation}
                    initialLocation={selectedLocation}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <FormField
                    control={form.control}
                    name="price"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Price per night ($)</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            {...field}
                            onChange={(e) => field.onChange(Number(e.target.value))}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="beds"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Bedrooms</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min="1"
                            {...field}
                            onChange={(e) => field.onChange(Number(e.target.value))}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="baths"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Bathrooms</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min="1"
                            {...field}
                            onChange={(e) => field.onChange(Number(e.target.value))}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div>
                  <label className="text-sm font-medium">Features & Amenities</label>
                  <FeatureSelector
                    selectedFeatures={selectedFeatures}
                    onFeaturesChange={setSelectedFeatures}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="images"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Add More Images (Optional)</FormLabel>
                      <FormControl>
                        <Input
                          type="file"
                          multiple
                          accept="image/*"
                          onChange={(e) => field.onChange(e.target.files)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {existingImages.length > 0 && (
                  <div>
                    <label className="text-sm font-medium">Current Images</label>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mt-2">
                      {existingImages.map((image, index) => (
                        <img
                          key={index}
                          src={image}
                          alt={`Property image ${index + 1}`}
                          className="w-full h-24 object-cover rounded"
                        />
                      ))}
                    </div>
                  </div>
                )}

                <div className="flex gap-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => navigate("/host/listings")}
                    className="flex-1"
                  >
                    Cancel
                  </Button>
                  <Button type="submit" disabled={isSubmitting} className="flex-1">
                    {isSubmitting ? "Updating..." : "Update Property"}
                  </Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>
      </main>

      <Footer />
    </div>
  );
};

export default EditListing;
