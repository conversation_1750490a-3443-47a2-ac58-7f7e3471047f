
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON><PERSON>, LineChart } from "@/components/admin/charts";
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface RevenueReportProps {
  data: {
    daily: any;
    weekly: any;
    monthly: any;
    yearly: any;
  };
}

const RevenueReport: React.FC<RevenueReportProps> = ({ data }) => {
  const [view, setView] = React.useState<'daily' | 'weekly' | 'monthly' | 'yearly'>('monthly');
  const [chartType, setChartType] = React.useState<'bar' | 'line'>('bar');

  const viewOptions = [
    { label: 'Daily', value: 'daily' },
    { label: 'Weekly', value: 'weekly' },
    { label: 'Monthly', value: 'monthly' },
    { label: 'Yearly', value: 'yearly' }
  ];

  // Default to monthly data if the selected view doesn't exist
  const currentData = data[view] || data.monthly;

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <div>
          <CardTitle>Revenue Report</CardTitle>
          <CardDescription>
            Revenue breakdown by time period
          </CardDescription>
        </div>
        <div className="flex items-center space-x-2">
          <Select
            value={view}
            onValueChange={(value) => setView(value as 'daily' | 'weekly' | 'monthly' | 'yearly')}
          >
            <SelectTrigger className="w-[130px]">
              <SelectValue placeholder="Select view" />
            </SelectTrigger>
            <SelectContent>
              {viewOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Tabs 
            value={chartType} 
            onValueChange={(value) => setChartType(value as 'bar' | 'line')}
            className="w-[180px]"
          >
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="bar">Bar</TabsTrigger>
              <TabsTrigger value="line">Line</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </CardHeader>
      <CardContent>
        {chartType === 'bar' ? (
          <BarChart
            data={currentData}
            width={600}
            height={350}
            options={{
              maintainAspectRatio: false,
              scales: {
                y: {
                  beginAtZero: true,
                }
              }
            }}
          />
        ) : (
          <LineChart
            data={currentData}
            width={600}
            height={350}
            options={{
              maintainAspectRatio: false,
              scales: {
                y: {
                  beginAtZero: true
                }
              }
            }}
          />
        )}
      </CardContent>
    </Card>
  );
};

export default RevenueReport;
