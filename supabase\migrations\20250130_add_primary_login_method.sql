-- Add primary login method to profiles table
-- This stores which method (email or phone) the user chose as primary during registration

-- Add primary_login_method column to profiles table
ALTER TABLE public.profiles 
ADD COLUMN IF NOT EXISTS primary_login_method TEXT CHECK (primary_login_method IN ('email', 'phone'));

-- Set default primary_login_method based on existing data
-- If user has phone_confirmed_at, assume phone was primary
-- If user has email_confirmed_at, assume email was primary
-- Default to 'email' for users without either
UPDATE public.profiles 
SET primary_login_method = CASE 
  WHEN EXISTS (
    SELECT 1 FROM auth.users 
    WHERE auth.users.id = profiles.id 
    AND auth.users.phone_confirmed_at IS NOT NULL
  ) THEN 'phone'
  WHEN EXISTS (
    SELECT 1 FROM auth.users 
    WHERE auth.users.id = profiles.id 
    AND auth.users.email_confirmed_at IS NOT NULL
  ) THEN 'email'
  ELSE 'email'
END
WHERE primary_login_method IS NULL;

-- Create index for primary_login_method lookups
CREATE INDEX IF NOT EXISTS idx_profiles_primary_login_method ON public.profiles(primary_login_method);
