import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

export type Review = {
  id: string;
  property_id: string | null;
  car_id: string | null;
  hotel_id: string | null;
  user_id: string;
  rating: number;
  comment: string | null;
  created_at: string | null;
  profiles: {
    first_name: string | null;
    last_name: string | null;
    avatar_url: string | null;
  };
};

export const useReviews = (
  entityId: string | undefined,
  entityType: "property" | "car" | "hotel" = "property"
) => {
  return useQuery({
    queryKey: ["reviews", entityId, entityType],
    queryFn: async () => {
      if (!entityId) return [];

      const columnMap = {
        property: "property_id",
        car: "car_id",
        hotel: "hotel_id",
      };

      // First get the reviews
      const { data: reviewsData, error } = await supabase
        .from("reviews")
        .select("*")
        .eq(columnMap[entityType], entityId)
        .order("created_at", { ascending: false });

      if (error) throw error;

      // Then get the profile data for each review
      const reviewsWithProfiles = await Promise.all(
        (reviewsData || []).map(async (review) => {
          let profileData = null;

          if (review.user_id) {
            try {
              const { data: profile, error: profileError } = await supabase
                .from("profiles")
                .select("first_name, last_name, avatar_url")
                .eq("id", review.user_id)
                .single();

              if (!profileError) {
                profileData = profile;
              }
            } catch (profileError) {
              console.log(
                "Could not fetch review author profile:",
                profileError
              );
              // Continue without profile data
            }
          }

          return {
            ...review,
            profiles: profileData,
          };
        })
      );

      return reviewsWithProfiles as Review[];
    },
    enabled: !!entityId,
  });
};
