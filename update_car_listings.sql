-- Update car listings with correct vehicle details and pricing
-- Run this script in your Supabase SQL Editor

BEGIN;

-- 1. Mercedes-Benz GLE Coupe (21459414-7dc1-4221-b5ab-5671a1dd821d)
UPDATE public.cars 
SET 
  title = 'Mercedes-Benz GLE Coupe - Luxury SUV',
  make = 'Mercedes-Benz',
  model = 'GLE Coupe',
  car_type = 'luxury',
  year = 2023,
  seats = 5,
  fuel_type = 'Petrol',
  transmission = 'Automatic',
  price_day = 180.00,
  price_week = 1200.00,
  price_month = 4800.00,
  description = 'Experience luxury and performance with this stunning Mercedes-Benz GLE Coupe. Perfect for special occasions or business travel.',
  features = ARRAY['Leather Seats', 'Navigation System', 'Bluetooth', 'Air Conditioning', 'Premium Sound System', 'Sunroof', 'Parking Sensors'],
  updated_at = now()
WHERE id = '21459414-7dc1-4221-b5ab-5671a1dd821d';

-- 2. Nissan Juke (37a05b78-332f-4251-8912-f4819a18402c)
UPDATE public.cars 
SET 
  title = 'Nissan Juke - Subcompact Crossover SUV',
  make = 'Nissan',
  model = 'Juke',
  car_type = 'suv',
  year = 2022,
  seats = 5,
  fuel_type = 'Petrol',
  transmission = 'Automatic',
  price_day = 65.00,
  price_week = 420.00,
  price_month = 1680.00,
  description = 'Modern and stylish Nissan Juke with bold design and efficient performance. Perfect for city driving and weekend adventures.',
  features = ARRAY['LED Headlights', 'Bluetooth', 'Air Conditioning', 'USB Ports', 'Backup Camera', 'Keyless Entry'],
  updated_at = now()
WHERE id = '37a05b78-332f-4251-8912-f4819a18402c';

-- 3. Audi RS6 Avant (5990a14b-d1af-4264-852f-3348762b56d5)
UPDATE public.cars 
SET 
  title = 'Audi RS6 Avant - High-Performance Wagon',
  make = 'Audi',
  model = 'RS6 Avant',
  car_type = 'luxury',
  year = 2023,
  seats = 5,
  fuel_type = 'Petrol',
  transmission = 'Automatic',
  price_day = 220.00,
  price_week = 1500.00,
  price_month = 6000.00,
  description = 'Ultimate performance wagon with quattro AWD system. Combines luxury, practicality, and incredible performance.',
  features = ARRAY['Quattro AWD', 'Premium Leather', 'Bang & Olufsen Sound', 'Navigation', 'Adaptive Suspension', 'Sport Exhaust', 'Carbon Fiber Trim'],
  updated_at = now()
WHERE id = '5990a14b-d1af-4264-852f-3348762b56d5';

-- 4. Ford Expedition (697ab9ca-959b-4977-a9da-1e43c8349c45)
UPDATE public.cars 
SET 
  title = 'Ford Expedition - Full-Size SUV',
  make = 'Ford',
  model = 'Expedition',
  car_type = 'suv',
  year = 2022,
  seats = 8,
  fuel_type = 'Petrol',
  transmission = 'Automatic',
  price_day = 120.00,
  price_week = 800.00,
  price_month = 3200.00,
  description = 'Spacious full-size SUV perfect for large families or group travel. Comfortable seating for up to 8 passengers.',
  features = ARRAY['8 Passenger Seating', '4WD', 'Towing Package', 'Rear Entertainment', 'Climate Control', 'Power Liftgate', 'Bluetooth'],
  updated_at = now()
WHERE id = '697ab9ca-959b-4977-a9da-1e43c8349c45';

-- 5. BMW M5 Performance Sedan (891020fa-843a-4af2-91af-4c3b401b1646)
UPDATE public.cars 
SET 
  title = 'BMW M5 Performance Sedan - 2023',
  make = 'BMW',
  model = 'M5',
  car_type = 'luxury',
  year = 2023,
  seats = 5,
  fuel_type = 'Petrol',
  transmission = 'Automatic',
  price_day = 200.00,
  price_week = 1350.00,
  price_month = 5400.00,
  description = 'High-performance luxury sedan with M-series engineering. Experience the perfect blend of luxury and track-ready performance.',
  features = ARRAY['M Performance Package', 'Premium Leather', 'Harman Kardon Audio', 'Adaptive M Suspension', 'Carbon Fiber Interior', 'M Sport Exhaust'],
  updated_at = now()
WHERE id = '891020fa-843a-4af2-91af-4c3b401b1646';

-- 6. Volkswagen Golf GTI Mk6 (d57b8931-eb76-4a15-9eb1-f990e3ba14c7)
UPDATE public.cars 
SET 
  title = 'Volkswagen Golf GTI Mk6 - Hot Hatch',
  make = 'Volkswagen',
  model = 'Golf GTI',
  car_type = 'compact',
  year = 2012,
  seats = 5,
  fuel_type = 'Petrol',
  transmission = 'Manual',
  price_day = 55.00,
  price_week = 350.00,
  price_month = 1400.00,
  description = 'Classic hot hatch with sporty performance and practicality. Perfect for enthusiasts who appreciate driving dynamics.',
  features = ARRAY['Sport Suspension', 'GTI Interior Package', 'Bluetooth', 'Air Conditioning', 'Alloy Wheels', 'Fog Lights'],
  updated_at = now()
WHERE id = 'd57b8931-eb76-4a15-9eb1-f990e3ba14c7';

-- 7. Mercedes-AMG GT Coupe (d90c4b67-f897-448c-a690-97c5c35bb910)
UPDATE public.cars 
SET 
  title = 'Mercedes-AMG GT Coupe - High-Performance Sports Car',
  make = 'Mercedes-AMG',
  model = 'GT',
  car_type = 'luxury',
  year = 2023,
  seats = 2,
  fuel_type = 'Petrol',
  transmission = 'Automatic',
  price_day = 300.00,
  price_week = 2000.00,
  price_month = 8000.00,
  description = 'Ultimate luxury sports car with breathtaking performance. Perfect for special occasions and unforgettable driving experiences.',
  features = ARRAY['AMG Performance Package', 'Carbon Fiber Body', 'Premium Leather', 'AMG Track Pace', 'Adaptive Suspension', 'Performance Exhaust'],
  updated_at = now()
WHERE id = 'd90c4b67-f897-448c-a690-97c5c35bb910';

-- 8. Land Rover Range Rover Sport/Evoque (e7a9ed03-215d-4365-b0fa-90aef81044cf)
UPDATE public.cars 
SET 
  title = 'Land Rover Range Rover Sport - Luxury SUV',
  make = 'Land Rover',
  model = 'Range Rover Sport',
  car_type = 'luxury',
  year = 2023,
  seats = 5,
  fuel_type = 'Petrol',
  transmission = 'Automatic',
  price_day = 160.00,
  price_week = 1050.00,
  price_month = 4200.00,
  description = 'Luxury SUV with exceptional off-road capability and premium comfort. Perfect for both city driving and adventure.',
  features = ARRAY['Terrain Response', 'Premium Leather', 'Meridian Sound System', 'Panoramic Sunroof', 'Air Suspension', 'Navigation', 'Climate Control'],
  updated_at = now()
WHERE id = 'e7a9ed03-215d-4365-b0fa-90aef81044cf';

COMMIT;

-- Verify the updates
SELECT 
  id,
  title,
  make,
  model,
  year,
  car_type,
  seats,
  fuel_type,
  transmission,
  price_day,
  price_week,
  price_month
FROM public.cars 
WHERE id IN (
  '21459414-7dc1-4221-b5ab-5671a1dd821d',
  '37a05b78-332f-4251-8912-f4819a18402c',
  '5990a14b-d1af-4264-852f-3348762b56d5',
  '697ab9ca-959b-4977-a9da-1e43c8349c45',
  '891020fa-843a-4af2-91af-4c3b401b1646',
  'd57b8931-eb76-4a15-9eb1-f990e3ba14c7',
  'd90c4b67-f897-448c-a690-97c5c35bb910',
  'e7a9ed03-215d-4365-b0fa-90aef81044cf'
)
ORDER BY make, model;
