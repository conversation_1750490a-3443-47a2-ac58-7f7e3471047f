import { Car, Share, Heart, TestTube } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { CarListing } from "@/hooks/useCarListings";
import { toast } from "sonner";

interface CarDetailHeaderProps {
  car: CarListing;
}

const CarDetailHeader = ({ car }: CarDetailHeaderProps) => {
  const handleShare = async () => {
    const url = window.location.href;
    const shareData = {
      title: `${car.title} - Gesco Stay`,
      text: `Check out this amazing car rental: ${car.title} in ${car.location}`,
      url: url,
    };

    try {
      // Check if Web Share API is supported
      if (
        navigator.share &&
        navigator.canShare &&
        navigator.canShare(shareData)
      ) {
        await navigator.share(shareData);
        // Only show success message after share is completed
        toast.success("Shared successfully!");
      } else {
        // Fallback: Copy URL to clipboard
        await navigator.clipboard.writeText(url);
        toast.success("Car rental link copied to clipboard!");
      }
    } catch (error: any) {
      // Handle user cancellation or other errors
      if (error.name === "AbortError") {
        // User cancelled the share, don't show error
        return;
      }
      // If clipboard API fails, show the URL in a toast
      toast.info(`Share this car rental: ${url}`);
    }
  };

  return (
    <div className="space-y-3">
      <div className="flex justify-between items-start">
        <div className="flex-1">
          <h1 className="text-3xl font-bold">{car.title}</h1>
          {car.is_dummy && (
            <div className="mt-2">
              <Badge className="bg-orange-100 text-orange-800 flex items-center gap-1 w-fit">
                <TestTube className="w-3 h-3" />
                Display Only
              </Badge>
            </div>
          )}
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm" onClick={handleShare}>
            <Share className="w-4 h-4 mr-2" />
            Share
          </Button>
          <Button variant="outline" size="sm">
            <Heart className="w-4 h-4 mr-2" />
            Save
          </Button>
        </div>
      </div>

      <div className="flex flex-wrap items-center gap-2 text-sm">
        <Badge variant="outline" className="flex items-center gap-1">
          <Car className="h-4 w-4" />
          {car.make} {car.model} {car.year}
        </Badge>

        <Badge variant="secondary" className="capitalize">
          {car.car_type}
        </Badge>

        <div className="text-muted-foreground">{car.location}</div>
      </div>

      <div className="grid grid-cols-3 gap-3 md:flex md:gap-6 my-4">
        <div className="flex flex-col gap-1 items-center justify-center p-3 rounded-lg bg-accent/10">
          <span className="text-xs text-muted-foreground">Seats</span>
          <span className="font-semibold">{car.seats}</span>
        </div>

        <div className="flex flex-col gap-1 items-center justify-center p-3 rounded-lg bg-accent/10">
          <span className="text-xs text-muted-foreground">Transmission</span>
          <span className="font-semibold capitalize">{car.transmission}</span>
        </div>

        <div className="flex flex-col gap-1 items-center justify-center p-3 rounded-lg bg-accent/10">
          <span className="text-xs text-muted-foreground">Fuel</span>
          <span className="font-semibold capitalize">{car.fuel_type}</span>
        </div>
      </div>

      <div>
        <h2 className="text-lg font-semibold mb-2">Description</h2>
        <p className="text-muted-foreground">{car.description}</p>
      </div>

      {car.features && car.features.length > 0 && (
        <div>
          <h2 className="text-lg font-semibold mb-2">Features</h2>
          <div className="flex flex-wrap gap-2">
            {car.features.map((feature, index) => (
              <Badge key={index} variant="outline">
                {feature}
              </Badge>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default CarDetailHeader;
