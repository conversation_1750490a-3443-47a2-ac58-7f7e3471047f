# Environment Variables for Gesco Stay Migration

This document lists all the environment variables required for the new Supabase project.

## Supabase Configuration

### Frontend Environment Variables (.env.local)

```bash
# Supabase Configuration
VITE_SUPABASE_URL=https://YOUR_NEW_PROJECT_ID.supabase.co
VITE_SUPABASE_ANON_KEY=YOUR_NEW_ANON_KEY

# Optional: For development
VITE_SUPABASE_SERVICE_ROLE_KEY=YOUR_NEW_SERVICE_ROLE_KEY
```

### Edge Functions Environment Variables

Set these in the Supabase dashboard under Settings > Edge Functions > Environment Variables

```bash
# Supabase
SUPABASE_URL=https://YOUR_NEW_PROJECT_ID.supabase.co
SUPABASE_ANON_KEY=YOUR_NEW_ANON_KEY
SUPABASE_SERVICE_ROLE_KEY=YOUR_NEW_SERVICE_ROLE_KEY

# AWS SES Configuration (for email notifications)
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
AWS_REGION=eu-west-2

# SES Email Addresses for different purposes
SES_BOOKING_EMAIL=<EMAIL>
SES_NOREPLY_EMAIL=<EMAIL>
SES_SUPPORT_EMAIL=<EMAIL>
SES_NOTIFICATIONS_EMAIL=<EMAIL>

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_... (or sk_live_... for production)
STRIPE_PUBLISHABLE_KEY=pk_test_... (or pk_live_... for production)
STRIPE_WEBHOOK_SECRET=whsec_...

# Wave Payment Configuration
WAVE_API_KEY=your_wave_api_key
WAVE_WEBHOOK_SECRET=your_wave_webhook_secret

# Other API Keys
GOOGLE_MAPS_API_KEY=your_google_maps_api_key (if used)
```

## Current Project Information (for reference)

### Current Project Details

- **Project ID**: meakrzwthtkkumudxhzv
- **Project Name**: Gesco stay
- **Region**: ap-southeast-1
- **Organization**: lwxbdgnktzsmmaghugyx

### Current URLs (DO NOT USE IN NEW PROJECT)

- **Project URL**: https://meakrzwthtkkumudxhzv.supabase.co
- **Database URL**: db.meakrzwthtkkumudxhzv.supabase.co

## New Project Setup Checklist

### 1. Create New Supabase Project

- [ ] Create project in target organization
- [ ] Choose same region (ap-southeast-1) for better performance
- [ ] Note down new project credentials

### 2. Update Environment Variables

- [ ] Update frontend .env.local file
- [ ] Update CI/CD pipeline variables
- [ ] Update edge function environment variables
- [ ] Update any hardcoded references in code

### 3. External Service Configuration

- [ ] Update Stripe webhook endpoints to point to new project
- [ ] Update Wave webhook endpoints if applicable
- [ ] Update AWS SES configuration if needed
- [ ] Update any third-party service integrations

### 4. Domain and DNS (if applicable)

- [ ] Update custom domain settings
- [ ] Update DNS records
- [ ] Update SSL certificates

### 5. Authentication Configuration

- [ ] Configure OAuth providers (Google, Facebook, etc.)
- [ ] Update redirect URLs
- [ ] Configure email templates
- [ ] Set up SMTP settings

## Security Notes

### Sensitive Information

- **Never commit API keys to version control**
- **Use different keys for development and production**
- **Rotate keys regularly**
- **Use environment-specific configurations**

### Key Management

- Store production keys in secure key management systems
- Use Supabase's built-in environment variable management
- Implement proper access controls for key access

## Validation

### Test Environment Variables

After setting up the new project, verify all environment variables are working:

```bash
# Test Supabase connection
curl -H "apikey: YOUR_ANON_KEY" \
     -H "Authorization: Bearer YOUR_ANON_KEY" \
     https://YOUR_PROJECT_ID.supabase.co/rest/v1/profiles

# Test edge functions
curl -X POST https://YOUR_PROJECT_ID.supabase.co/functions/v1/send-booking-confirmation \
     -H "Authorization: Bearer YOUR_ANON_KEY" \
     -H "Content-Type: application/json" \
     -d '{"bookingId": "test"}'
```

### Frontend Testing

- [ ] Test user authentication
- [ ] Test database queries
- [ ] Test file uploads
- [ ] Test real-time subscriptions

### Backend Testing

- [ ] Test all edge functions
- [ ] Test webhook endpoints
- [ ] Test email notifications
- [ ] Test payment processing

## Migration Day Checklist

### Pre-Migration

- [ ] Backup current environment variables
- [ ] Prepare rollback plan
- [ ] Notify team of maintenance window

### During Migration

- [ ] Update all environment variables simultaneously
- [ ] Test critical functionality immediately
- [ ] Monitor error logs

### Post-Migration

- [ ] Verify all services are working
- [ ] Check payment processing
- [ ] Confirm email notifications
- [ ] Monitor for 24-48 hours

## Troubleshooting

### Common Issues

1. **CORS errors**: Check if URLs are updated correctly
2. **Authentication failures**: Verify anon key and service role key
3. **Database connection issues**: Check if project ID is correct
4. **Edge function errors**: Verify environment variables are set
5. **Payment failures**: Check Stripe/Wave webhook configurations

### Support Contacts

- Supabase Support: <EMAIL>
- Internal Team: [Your team contact information]
- Emergency Contact: [Emergency contact information]
