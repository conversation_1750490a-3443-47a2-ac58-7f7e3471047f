-- Restore Original Table Structure with BIGINT IDs
-- This script restores the original table structure but changes only the ID columns to BIGINT

-- Step 1: Drop current tables and restore from original schema with BIGINT IDs

-- Drop existing tables
DROP TABLE IF EXISTS messages CASCADE;
DROP TABLE IF EXISTS conversations CASCADE;
DROP TABLE IF EXIS<PERSON> reviews CASCADE;
DROP TABLE IF EXISTS hotel_bookings CASCADE;
DROP TABLE IF EXISTS car_bookings CASCADE;
DROP TABLE IF EXISTS bookings CASCADE;
DROP TABLE IF EXISTS room_types CASCADE;
DROP TABLE IF EXISTS hotels CASCADE;
DROP TABLE IF EXISTS cars CASCADE;
DROP TABLE IF EXISTS properties CASCADE;

-- Create ENUM types if they don't exist
DO $$ BEGIN
    CREATE TYPE listing_status AS ENUM ('pending', 'approved', 'rejected');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE car_type AS ENUM ('sedan', 'suv', 'hatchback', 'coupe', 'convertible', 'truck', 'van', 'luxury');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE rental_duration AS ENUM ('daily', 'weekly', 'monthly');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Properties table with BIGINT ID but original structure
CREATE TABLE IF NOT EXISTS public.properties (
  id BIGINT PRIMARY KEY DEFAULT generate_unique_property_id(),
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  location TEXT NOT NULL,
  formatted_address TEXT,
  latitude DECIMAL(10, 8),
  longitude DECIMAL(11, 8),
  price DECIMAL(10, 2) NOT NULL CHECK (price > 0),
  beds INTEGER NOT NULL CHECK (beds > 0),
  baths INTEGER NOT NULL CHECK (baths > 0),
  owner_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  images TEXT[] NOT NULL DEFAULT '{}',
  features TEXT[] NOT NULL DEFAULT '{}',
  status listing_status NOT NULL DEFAULT 'pending',
  is_dummy BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Cars table with BIGINT ID but original structure
CREATE TABLE IF NOT EXISTS public.cars (
  id BIGINT PRIMARY KEY DEFAULT generate_unique_property_id(),
  owner_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  car_type car_type NOT NULL,
  make TEXT NOT NULL,
  model TEXT NOT NULL,
  year INTEGER NOT NULL CHECK (year > 1900 AND year <= EXTRACT(YEAR FROM CURRENT_DATE) + 1),
  location TEXT NOT NULL,
  formatted_address TEXT,
  latitude DECIMAL(10, 8),
  longitude DECIMAL(11, 8),
  images TEXT[] NOT NULL DEFAULT '{}',
  price_day DECIMAL(10, 2) NOT NULL CHECK (price_day > 0),
  price_week DECIMAL(10, 2) NOT NULL CHECK (price_week > 0),
  price_month DECIMAL(10, 2) NOT NULL CHECK (price_month > 0),
  seats INTEGER NOT NULL CHECK (seats > 0),
  transmission TEXT NOT NULL,
  fuel_type TEXT NOT NULL,
  status listing_status DEFAULT 'approved',
  is_dummy BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Hotels table with BIGINT ID but original structure
CREATE TABLE IF NOT EXISTS public.hotels (
  id BIGINT PRIMARY KEY DEFAULT generate_unique_property_id(),
  title VARCHAR(255) NOT NULL,
  description TEXT NOT NULL,
  location VARCHAR(255) NOT NULL,
  formatted_address TEXT,
  latitude DECIMAL(10, 8),
  longitude DECIMAL(11, 8),
  owner_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  images TEXT[] DEFAULT '{}',
  amenities TEXT[] DEFAULT '{}',
  policies JSONB DEFAULT '{}',
  check_in_time TIME DEFAULT '15:00:00',
  check_out_time TIME DEFAULT '11:00:00',
  status listing_status DEFAULT 'approved',
  is_dummy BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Room types table with BIGINT ID but original structure
CREATE TABLE IF NOT EXISTS public.room_types (
  id BIGINT PRIMARY KEY DEFAULT generate_unique_room_type_id(),
  hotel_id BIGINT NOT NULL REFERENCES public.hotels(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  price_per_night DECIMAL(10, 2) NOT NULL CHECK (price_per_night > 0),
  max_occupancy INTEGER NOT NULL CHECK (max_occupancy > 0),
  amenities TEXT[] DEFAULT '{}',
  images TEXT[] DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Bookings table with BIGINT ID but original structure
CREATE TABLE IF NOT EXISTS public.bookings (
  id BIGINT PRIMARY KEY DEFAULT generate_unique_booking_id(),
  property_id BIGINT REFERENCES public.properties(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  check_in DATE NOT NULL,
  check_out DATE NOT NULL,
  total_price DECIMAL(10, 2) NOT NULL CHECK (total_price > 0),
  status TEXT NOT NULL DEFAULT 'pending',
  payment_method TEXT,
  payment_status TEXT DEFAULT 'pending',
  payment_id TEXT,
  stripe_session_id TEXT,
  wave_checkout_id TEXT,
  wave_transaction_id TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  CHECK (check_out > check_in)
);

-- Car bookings table with BIGINT ID but original structure
CREATE TABLE IF NOT EXISTS public.car_bookings (
  id BIGINT PRIMARY KEY DEFAULT generate_unique_booking_id(),
  car_id BIGINT NOT NULL REFERENCES public.cars(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  start_date DATE NOT NULL,
  end_date DATE NOT NULL,
  duration_type rental_duration NOT NULL,
  total_price DECIMAL(10, 2) NOT NULL CHECK (total_price > 0),
  status TEXT NOT NULL DEFAULT 'pending',
  payment_method TEXT,
  payment_status TEXT,
  payment_id TEXT,
  stripe_session_id TEXT,
  wave_checkout_id TEXT,
  wave_transaction_id TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  CHECK (end_date > start_date)
);

-- Hotel bookings table with BIGINT ID but original structure
CREATE TABLE IF NOT EXISTS public.hotel_bookings (
  id BIGINT PRIMARY KEY DEFAULT generate_unique_booking_id(),
  hotel_id BIGINT NOT NULL REFERENCES public.hotels(id) ON DELETE CASCADE,
  room_type_id BIGINT REFERENCES public.room_types(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  check_in DATE NOT NULL,
  check_out DATE NOT NULL,
  adults INTEGER NOT NULL DEFAULT 1 CHECK (adults > 0),
  children INTEGER DEFAULT 0 CHECK (children >= 0),
  rooms_count INTEGER DEFAULT 1 CHECK (rooms_count > 0),
  total_price DECIMAL(10, 2) NOT NULL CHECK (total_price > 0),
  status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'cancelled', 'completed')),
  payment_status VARCHAR(20) DEFAULT 'pending' CHECK (payment_status IN ('pending', 'paid', 'failed', 'refunded')),
  payment_method VARCHAR(50),
  payment_id VARCHAR(255),
  stripe_session_id VARCHAR(255),
  special_requests TEXT,
  guest_details JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  CHECK (check_out > check_in),
  CHECK (check_in >= CURRENT_DATE)
);

-- Reviews table with BIGINT ID but original structure
CREATE TABLE IF NOT EXISTS public.reviews (
  id BIGINT PRIMARY KEY DEFAULT generate_unique_review_id(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  property_id BIGINT REFERENCES public.properties(id) ON DELETE CASCADE,
  car_id BIGINT REFERENCES public.cars(id) ON DELETE CASCADE,
  hotel_id BIGINT REFERENCES public.hotels(id) ON DELETE CASCADE,
  rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
  comment TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  CHECK (
    (property_id IS NOT NULL AND car_id IS NULL AND hotel_id IS NULL) OR
    (property_id IS NULL AND car_id IS NOT NULL AND hotel_id IS NULL) OR
    (property_id IS NULL AND car_id IS NULL AND hotel_id IS NOT NULL)
  )
);

-- Conversations table with BIGINT ID but original structure
CREATE TABLE IF NOT EXISTS public.conversations (
  id BIGINT PRIMARY KEY DEFAULT generate_unique_conversation_id(),
  participant_1_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  participant_2_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  property_id BIGINT REFERENCES public.properties(id) ON DELETE SET NULL,
  car_id BIGINT REFERENCES public.cars(id) ON DELETE SET NULL,
  hotel_id BIGINT REFERENCES public.hotels(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  CHECK (participant_1_id != participant_2_id)
);

-- Messages table with BIGINT ID but original structure
CREATE TABLE IF NOT EXISTS public.messages (
  id BIGINT PRIMARY KEY DEFAULT generate_unique_message_id(),
  conversation_id BIGINT NOT NULL REFERENCES public.conversations(id) ON DELETE CASCADE,
  sender_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create indexes for performance (same as original)
CREATE INDEX IF NOT EXISTS idx_properties_owner_id ON public.properties(owner_id);
CREATE INDEX IF NOT EXISTS idx_properties_status ON public.properties(status);
CREATE INDEX IF NOT EXISTS idx_properties_location ON public.properties(location);
CREATE INDEX IF NOT EXISTS idx_properties_is_dummy ON public.properties(is_dummy);

CREATE INDEX IF NOT EXISTS idx_cars_owner_id ON public.cars(owner_id);
CREATE INDEX IF NOT EXISTS idx_cars_status ON public.cars(status);
CREATE INDEX IF NOT EXISTS idx_cars_location ON public.cars(location);
CREATE INDEX IF NOT EXISTS idx_cars_is_dummy ON public.cars(is_dummy);

CREATE INDEX IF NOT EXISTS idx_hotels_owner_id ON public.hotels(owner_id);
CREATE INDEX IF NOT EXISTS idx_hotels_status ON public.hotels(status);
CREATE INDEX IF NOT EXISTS idx_hotels_location ON public.hotels(location);
CREATE INDEX IF NOT EXISTS idx_hotels_is_dummy ON public.hotels(is_dummy);

CREATE INDEX IF NOT EXISTS idx_bookings_user_id ON public.bookings(user_id);
CREATE INDEX IF NOT EXISTS idx_bookings_property_id ON public.bookings(property_id);
CREATE INDEX IF NOT EXISTS idx_bookings_status ON public.bookings(status);
CREATE INDEX IF NOT EXISTS idx_bookings_check_in ON public.bookings(check_in);
CREATE INDEX IF NOT EXISTS idx_bookings_check_out ON public.bookings(check_out);

CREATE INDEX IF NOT EXISTS idx_car_bookings_user_id ON public.car_bookings(user_id);
CREATE INDEX IF NOT EXISTS idx_car_bookings_car_id ON public.car_bookings(car_id);
CREATE INDEX IF NOT EXISTS idx_car_bookings_status ON public.car_bookings(status);
CREATE INDEX IF NOT EXISTS idx_car_bookings_dates ON public.car_bookings(start_date, end_date);

CREATE INDEX IF NOT EXISTS idx_hotel_bookings_user_id ON public.hotel_bookings(user_id);
CREATE INDEX IF NOT EXISTS idx_hotel_bookings_hotel_id ON public.hotel_bookings(hotel_id);
CREATE INDEX IF NOT EXISTS idx_hotel_bookings_room_type_id ON public.hotel_bookings(room_type_id);
CREATE INDEX IF NOT EXISTS idx_hotel_bookings_status ON public.hotel_bookings(status);
CREATE INDEX IF NOT EXISTS idx_hotel_bookings_dates ON public.hotel_bookings(check_in, check_out);

CREATE INDEX IF NOT EXISTS idx_conversations_participants ON public.conversations(participant_1_id, participant_2_id);
CREATE INDEX IF NOT EXISTS idx_messages_conversation_id ON public.messages(conversation_id);
CREATE INDEX IF NOT EXISTS idx_messages_sender_id ON public.messages(sender_id);

-- Insert some sample data to test the system
INSERT INTO public.properties (owner_id, title, description, location, price, beds, baths, images, features, is_dummy, status)
SELECT
  (SELECT id FROM auth.users LIMIT 1),
  'Sample Property ' || generate_series,
  'A beautiful sample property for testing',
  'Sample Location ' || generate_series,
  100.00 + (generate_series * 50),
  2,
  1,
  '{}',
  '{}',
  true,
  'approved'
FROM generate_series(1, 5)
WHERE EXISTS (SELECT 1 FROM auth.users LIMIT 1);

INSERT INTO public.cars (owner_id, title, description, car_type, make, model, year, location, price_day, price_week, price_month, seats, transmission, fuel_type, is_dummy, status)
SELECT
  (SELECT id FROM auth.users LIMIT 1),
  'Sample Car ' || generate_series,
  'A reliable sample car for testing',
  'sedan',
  'Toyota',
  'Camry',
  2020,
  'Sample Location ' || generate_series,
  50.00,
  300.00,
  1000.00,
  5,
  'Automatic',
  'Gasoline',
  true,
  'approved'
FROM generate_series(1, 3)
WHERE EXISTS (SELECT 1 FROM auth.users LIMIT 1);

INSERT INTO public.hotels (title, description, location, owner_id, amenities, is_dummy, status)
SELECT
  'Sample Hotel ' || generate_series,
  'A comfortable sample hotel for testing',
  'Sample Location ' || generate_series,
  (SELECT id FROM auth.users LIMIT 1),
  '{}',
  true,
  'approved'
FROM generate_series(1, 3)
WHERE EXISTS (SELECT 1 FROM auth.users LIMIT 1);
