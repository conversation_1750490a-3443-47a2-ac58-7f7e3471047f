import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.45.0";

const SUPABASE_URL = Deno.env.get("SUPABASE_URL") || "";
const SUPABASE_SERVICE_ROLE_KEY =
  Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") || "";
const AWS_ACCESS_KEY_ID = Deno.env.get("AWS_ACCESS_KEY_ID") || "";
const AWS_SECRET_ACCESS_KEY = Deno.env.get("AWS_SECRET_ACCESS_KEY") || "";
const AWS_REGION = Deno.env.get("AWS_REGION") || "eu-west-2";

// Email addresses for different purposes
const EMAIL_ADDRESSES = {
  booking: Deno.env.get("SES_BOOKING_EMAIL") || "<EMAIL>",
  noreply: Deno.env.get("SES_NOREPLY_EMAIL") || "<EMAIL>",
  support: Deno.env.get("SES_SUPPORT_EMAIL") || "<EMAIL>",
  notifications:
    Deno.env.get("SES_NOTIFICATIONS_EMAIL") || "<EMAIL>",
};

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
};

interface EmailPayload {
  bookingId?: string;
  carBookingId?: string;
  type?: string; // "confirmation" | "reminder" | "checkout_reminder" | "status_update"
  status?: string;
}

// Helper function to get appropriate email address based on email type
function getFromEmail(emailType: string): string {
  switch (emailType) {
    case "confirmation":
    case "status_update":
      return EMAIL_ADDRESSES.booking;
    case "reminder":
    case "checkout_reminder":
      return EMAIL_ADDRESSES.notifications;
    default:
      return EMAIL_ADDRESSES.noreply;
  }
}

// Professional email template function
function createProfessionalEmailTemplate({
  title,
  greeting,
  mainMessage,
  bookingDetails,
  propertyDetails,
  additionalInfo,
  ctaText,
  ctaUrl,
  type,
}: {
  title: string;
  greeting: string;
  mainMessage: string;
  bookingDetails: Array<{ label: string; value: string }>;
  propertyDetails?: { address?: string; host?: string };
  additionalInfo?: string;
  ctaText?: string;
  ctaUrl?: string;
  type: "car" | "property";
}): string {
  const primaryColor = "#2563eb";
  const backgroundColor = "#f8fafc";
  const cardBackground = "#ffffff";

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${title}</title>
      <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 0; background-color: ${backgroundColor}; }
        .container { max-width: 600px; margin: 0 auto; background-color: ${cardBackground}; }
        .header { background: linear-gradient(135deg, ${primaryColor} 0%, #1d4ed8 100%); color: white; padding: 40px 30px; text-align: center; }
        .logo { font-size: 28px; font-weight: bold; margin-bottom: 10px; }
        .header-subtitle { font-size: 16px; opacity: 0.9; }
        .content { padding: 40px 30px; }
        .title { color: #1f2937; font-size: 24px; font-weight: 600; margin-bottom: 20px; }
        .greeting { color: #374151; font-size: 16px; margin-bottom: 15px; }
        .message { color: #4b5563; font-size: 16px; line-height: 1.6; margin-bottom: 30px; }
        .details-card { background-color: #f9fafb; border: 1px solid #e5e7eb; border-radius: 12px; padding: 25px; margin: 25px 0; }
        .details-title { color: #1f2937; font-size: 18px; font-weight: 600; margin-bottom: 15px; }
        .detail-row { display: flex; justify-content: space-between; align-items: center; padding: 8px 0; border-bottom: 1px solid #e5e7eb; }
        .detail-row:last-child { border-bottom: none; }
        .detail-label { color: #6b7280; font-weight: 500; }
        .detail-value { color: #1f2937; font-weight: 600; }
        .property-info { background-color: #eff6ff; border: 1px solid #bfdbfe; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .cta-button { display: inline-block; background-color: ${primaryColor}; color: white; padding: 14px 28px; text-decoration: none; border-radius: 8px; font-weight: 600; margin: 20px 0; }
        .additional-info { background-color: #fef3c7; border: 1px solid #fbbf24; border-radius: 8px; padding: 15px; margin: 20px 0; color: #92400e; }
        .footer { background-color: #f3f4f6; padding: 30px; text-align: center; color: #6b7280; }
        .footer-links { margin: 15px 0; }
        .footer-links a { color: ${primaryColor}; text-decoration: none; margin: 0 10px; }
        .social-icons { margin: 20px 0; }
        .icon { display: inline-block; margin: 0 5px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <div class="logo">🏠 Gesco Stay</div>
          <div class="header-subtitle">Your trusted travel companion</div>
        </div>

        <div class="content">
          <h1 class="title">${title}</h1>
          <p class="greeting">${greeting}</p>
          <p class="message">${mainMessage}</p>

          <div class="details-card">
            <h3 class="details-title">${
              type === "car" ? "🚗 Rental Details" : "🏠 Booking Details"
            }</h3>
            ${bookingDetails
              .map(
                (detail) => `
              <div class="detail-row">
                <span class="detail-label">${detail.label}:</span>
                <span class="detail-value">${detail.value}</span>
              </div>
            `
              )
              .join("")}
          </div>

          ${
            propertyDetails
              ? `
            <div class="property-info">
              <h3 style="margin-top: 0; color: #1e40af;">📍 Property Information</h3>
              ${
                propertyDetails.address
                  ? `<p><strong>Address:</strong> ${propertyDetails.address}</p>`
                  : ""
              }
              ${
                propertyDetails.host
                  ? `<p><strong>Your Host:</strong> ${propertyDetails.host}</p>`
                  : ""
              }
            </div>
          `
              : ""
          }

          ${
            additionalInfo
              ? `
            <div class="additional-info">
              <strong>💡 Important Information:</strong><br>
              ${additionalInfo}
            </div>
          `
              : ""
          }

          ${
            ctaText && ctaUrl
              ? `
            <div style="text-align: center; margin: 30px 0;">
              <a href="${ctaUrl}" class="cta-button">${ctaText}</a>
            </div>
          `
              : ""
          }

          <p style="color: #6b7280; margin-top: 30px;">
            If you have any questions or need assistance, please don't hesitate to contact our support team at
            <a href="mailto:<EMAIL>" style="color: ${primaryColor};"><EMAIL></a>
          </p>
        </div>

        <div class="footer">
          <p><strong>Thank you for choosing Gesco Stay!</strong></p>
          <div class="footer-links">
            <a href="https://gescostay.com">Visit Website</a> |
            <a href="https://gescostay.com/support">Support Center</a> |
            <a href="https://gescostay.com/privacy">Privacy Policy</a>
          </div>
          <p style="font-size: 14px; margin-top: 20px;">
            © 2024 Gesco Stay. All rights reserved.<br>
            Making travel memorable, one stay at a time.
          </p>
        </div>
      </div>
    </body>
    </html>
  `;
}

// AWS SES helper function
async function sendEmailWithSES(
  to: string,
  subject: string,
  htmlBody: string,
  fromEmail: string
) {
  const timestamp = new Date().toISOString().replace(/[:\-]|\.\d{3}/g, "");
  const date = timestamp.substr(0, 8);

  // Create URL-encoded payload for AWS SES
  const params = new URLSearchParams({
    Action: "SendEmail",
    Version: "2010-12-01",
    Source: fromEmail,
    "Destination.ToAddresses.member.1": to,
    "Message.Subject.Data": subject,
    "Message.Body.Html.Data": htmlBody,
  });

  const payload = params.toString();

  // Create AWS signature
  const algorithm = "AWS4-HMAC-SHA256";
  const service = "email"; // Correct service name for SES
  const host = `email.${AWS_REGION}.amazonaws.com`; // Correct endpoint format
  const amzDate = timestamp;
  const dateStamp = date;

  const canonicalUri = "/";
  const canonicalQuerystring = "";
  const canonicalHeaders = `host:${host}\nx-amz-date:${amzDate}\n`;
  const signedHeaders = "host;x-amz-date";
  const payloadHash = await crypto.subtle
    .digest("SHA-256", new TextEncoder().encode(payload))
    .then((buffer) =>
      Array.from(new Uint8Array(buffer))
        .map((b) => b.toString(16).padStart(2, "0"))
        .join("")
    );

  const canonicalRequest = `POST\n${canonicalUri}\n${canonicalQuerystring}\n${canonicalHeaders}\n${signedHeaders}\n${payloadHash}`;

  const credentialScope = `${dateStamp}/${AWS_REGION}/${service}/aws4_request`;
  const stringToSign = `${algorithm}\n${amzDate}\n${credentialScope}\n${await crypto.subtle
    .digest("SHA-256", new TextEncoder().encode(canonicalRequest))
    .then((buffer) =>
      Array.from(new Uint8Array(buffer))
        .map((b) => b.toString(16).padStart(2, "0"))
        .join("")
    )}`;

  const signingKey = await getSignatureKey(
    AWS_SECRET_ACCESS_KEY,
    dateStamp,
    AWS_REGION,
    service
  );
  const signature = await crypto.subtle
    .sign("HMAC", signingKey, new TextEncoder().encode(stringToSign))
    .then((buffer) =>
      Array.from(new Uint8Array(buffer))
        .map((b) => b.toString(16).padStart(2, "0"))
        .join("")
    );

  const authorizationHeader = `${algorithm} Credential=${AWS_ACCESS_KEY_ID}/${credentialScope}, SignedHeaders=${signedHeaders}, Signature=${signature}`;

  console.log("Sending email to:", to);
  console.log("Using SES endpoint:", `https://${host}/`);
  console.log("From email:", fromEmail);

  const response = await fetch(`https://${host}/`, {
    method: "POST",
    headers: {
      Authorization: authorizationHeader,
      "Content-Type": "application/x-www-form-urlencoded; charset=utf-8",
      "X-Amz-Date": amzDate,
    },
    body: payload,
  });

  if (!response.ok) {
    const errorText = await response.text();
    console.error("SES Error Response:", errorText);
    console.error("Response status:", response.status);
    console.error(
      "Response headers:",
      Object.fromEntries(response.headers.entries())
    );
    throw new Error(`SES API error: ${response.status} - ${errorText}`);
  }

  const responseText = await response.text();
  console.log("SES Success Response:", responseText);
  return response;
}

async function getSignatureKey(
  key: string,
  dateStamp: string,
  regionName: string,
  serviceName: string
) {
  const kDate = await crypto.subtle
    .importKey(
      "raw",
      new TextEncoder().encode("AWS4" + key),
      { name: "HMAC", hash: "SHA-256" },
      false,
      ["sign"]
    )
    .then((k) =>
      crypto.subtle.sign("HMAC", k, new TextEncoder().encode(dateStamp))
    );
  const kRegion = await crypto.subtle
    .importKey("raw", kDate, { name: "HMAC", hash: "SHA-256" }, false, ["sign"])
    .then((k) =>
      crypto.subtle.sign("HMAC", k, new TextEncoder().encode(regionName))
    );
  const kService = await crypto.subtle
    .importKey("raw", kRegion, { name: "HMAC", hash: "SHA-256" }, false, [
      "sign",
    ])
    .then((k) =>
      crypto.subtle.sign("HMAC", k, new TextEncoder().encode(serviceName))
    );
  const kSigning = await crypto.subtle
    .importKey("raw", kService, { name: "HMAC", hash: "SHA-256" }, false, [
      "sign",
    ])
    .then((k) =>
      crypto.subtle.sign("HMAC", k, new TextEncoder().encode("aws4_request"))
    );

  return crypto.subtle.importKey(
    "raw",
    kSigning,
    { name: "HMAC", hash: "SHA-256" },
    false,
    ["sign"]
  );
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const payload: EmailPayload = await req.json();
    const {
      bookingId,
      carBookingId,
      type = "confirmation",
      status,
      email,
      firstName,
      lastName,
    } = payload;

    // Determine if this is a car booking or property booking
    const isCarBooking = !!carBookingId;
    const finalBookingId = carBookingId || bookingId;

    console.log("Sending booking confirmation email for:", {
      isCarBooking,
      carBookingId,
      bookingId,
      finalBookingId,
      type,
    });

    // Create Supabase client with admin privileges
    const supabaseAdmin = createClient(
      SUPABASE_URL,
      SUPABASE_SERVICE_ROLE_KEY,
      {
        auth: { persistSession: false },
      }
    );

    // Get booking details based on booking type
    let booking, bookingError;

    if (isCarBooking) {
      // First get the car booking with car details
      const carBookingResult = await supabaseAdmin
        .from("car_bookings")
        .select(
          `
          *,
          cars (
            id,
            make,
            model,
            location,
            owner_id
          )
        `
        )
        .eq("id", finalBookingId)
        .single();

      if (carBookingResult.error) {
        console.error("Car booking fetch error:", carBookingResult.error);
        booking = null;
        bookingError = carBookingResult.error;
      } else {
        // Then get the user profile separately
        const profileResult = await supabaseAdmin
          .from("profiles")
          .select("id, first_name, last_name, email")
          .eq("id", carBookingResult.data.user_id)
          .single();

        if (profileResult.error) {
          console.error("Profile fetch error:", profileResult.error);
        }

        booking = {
          ...carBookingResult.data,
          profiles: profileResult.data,
        };
        bookingError = profileResult.error;
      }
    } else {
      // First get the booking with properties data
      const bookingResult = await supabaseAdmin
        .from("bookings")
        .select(
          `
          *,
          properties(*)
        `
        )
        .eq("id", finalBookingId)
        .single();

      if (bookingResult.error) {
        console.error("Booking fetch error:", bookingResult.error);
        booking = null;
        bookingError = bookingResult.error;
      } else {
        // Then get the user profile separately
        const profileResult = await supabaseAdmin
          .from("profiles")
          .select("id, first_name, last_name, email")
          .eq("id", bookingResult.data.user_id)
          .single();

        if (profileResult.error) {
          console.error("Profile fetch error:", profileResult.error);
        }

        booking = {
          ...bookingResult.data,
          profiles: profileResult.data,
        };
        bookingError = profileResult.error;
      }
    }

    if (bookingError || !booking) {
      throw new Error("Booking not found");
    }

    // Get owner details (property owner or car owner)
    let owner: any = null;
    let ownerError: any = null;
    const ownerId = isCarBooking
      ? booking.cars?.owner_id
      : booking.properties?.user_id;

    if (ownerId) {
      const result = await supabaseAdmin
        .from("profiles")
        .select("*")
        .eq("id", ownerId)
        .single();
      owner = result.data;
      ownerError = result.error;

      if (ownerError) {
        console.error("Could not fetch owner details:", ownerError);
      }
    } else {
      console.warn("No owner ID found for booking:", { isCarBooking, booking });
    }

    // Get user preferences for email notifications
    const { data: guestPrefs } = await supabaseAdmin
      .from("user_preferences")
      .select("*")
      .eq("user_id", booking.user_id)
      .maybeSingle();

    const { data: ownerPrefs } = ownerId
      ? await supabaseAdmin
          .from("user_preferences")
          .select("*")
          .eq("user_id", ownerId)
          .maybeSingle()
      : { data: null };

    // Check if user wants to receive this type of email
    const shouldSendToGuest =
      !guestPrefs ||
      (type === "confirmation" && guestPrefs.booking_confirmations) ||
      (type === "status_update" && guestPrefs.status_updates) ||
      (type === "reminder" && guestPrefs.reminders);

    const shouldSendToOwner =
      !ownerPrefs ||
      (type === "confirmation" && ownerPrefs.booking_confirmations) ||
      (type === "status_update" && ownerPrefs.status_updates) ||
      (type === "reminder" && ownerPrefs.reminders);

    // Format dates based on booking type
    let startDate, endDate;
    if (isCarBooking) {
      startDate = new Date(booking.start_date).toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      });
      endDate = new Date(booking.end_date).toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      });
    } else {
      startDate = new Date(booking.check_in).toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      });
      endDate = new Date(booking.check_out).toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      });
    }

    // For backward compatibility
    const checkIn = startDate;
    const checkOut = endDate;

    // Email content based on type and booking type
    let guestSubject = isCarBooking
      ? "Car Rental Confirmation"
      : "Booking Confirmation";
    let ownerSubject = isCarBooking
      ? "New Car Rental Booking"
      : "New Booking Notification";

    if (type === "reminder") {
      guestSubject = isCarBooking
        ? "Upcoming Car Rental Reminder"
        : "Upcoming Booking Reminder";
      ownerSubject = isCarBooking
        ? "Upcoming Car Rental Reminder"
        : "Upcoming Guest Arrival Reminder";
    } else if (type === "checkout_reminder") {
      guestSubject = isCarBooking
        ? "Car Return Reminder - 2 Hours Notice"
        : "Checkout Reminder - 2 Hours Notice";
      ownerSubject = isCarBooking
        ? "Car Return Reminder"
        : "Guest Checkout Reminder";
    } else if (type === "status_update" && status) {
      guestSubject = `${isCarBooking ? "Car Rental" : "Booking"} Status: ${
        status.charAt(0).toUpperCase() + status.slice(1)
      }`;
      ownerSubject = `${
        isCarBooking ? "Car Rental" : "Booking"
      } Status Update: ${status.charAt(0).toUpperCase() + status.slice(1)}`;
    }

    // Prepare email to guest
    let guestEmailContent = "";
    if (type === "confirmation") {
      if (isCarBooking) {
        guestEmailContent = createProfessionalEmailTemplate({
          title: "Your Car Rental is Confirmed! 🚗",
          greeting: `Dear ${booking.profiles.first_name || "Guest"},`,
          mainMessage:
            "Excellent news! Your car rental booking has been confirmed and payment has been processed successfully. Get ready for your upcoming adventure!",
          bookingDetails: [
            { label: "Booking ID", value: finalBookingId },
            {
              label: "Vehicle",
              value: `${booking.cars.make} ${booking.cars.model}`,
            },
            { label: "Location", value: booking.cars.location || "TBD" },
            { label: "Start Date", value: startDate },
            { label: "End Date", value: endDate },
            { label: "Duration Type", value: booking.duration_type },
            { label: "Total Amount", value: `$${booking.total_price}` },
            { label: "Status", value: "✅ Confirmed & Paid" },
          ],
          additionalInfo:
            "Please arrive at the pickup location 15 minutes before your scheduled time. Don't forget to bring a valid driver's license and the credit card used for booking.",
          ctaText: "View Rental Details",
          ctaUrl: `${
            Deno.env.get("FRONTEND_URL") || "https://gescostay.com"
          }/bookings/${finalBookingId}`,
          type: "car",
        });
      } else {
        guestEmailContent = createProfessionalEmailTemplate({
          title: "Your Booking is Confirmed! 🏠",
          greeting: `Dear ${booking.profiles.first_name || "Guest"},`,
          mainMessage:
            "Wonderful news! Your booking has been confirmed and payment has been processed successfully. We can't wait to welcome you!",
          bookingDetails: [
            { label: "Booking ID", value: finalBookingId },
            { label: "Property", value: booking.properties.title },
            {
              label: "Location",
              value:
                booking.properties.location ||
                booking.properties.address ||
                "TBD",
            },
            { label: "Check-in", value: checkIn },
            { label: "Check-out", value: checkOut },
            { label: "Guests", value: booking.guests?.toString() || "1" },
            { label: "Total Amount", value: `$${booking.total_price}` },
            { label: "Status", value: "✅ Confirmed & Paid" },
          ],
          propertyDetails: {
            address: booking.properties.address,
            host: `${owner?.first_name || ""} ${owner?.last_name || ""}`.trim(),
          },
          additionalInfo:
            "Check-in time is typically 3:00 PM and check-out is 11:00 AM. Your host will contact you with specific instructions and access details.",
          ctaText: "View Booking Details",
          ctaUrl: `https://gescostay.com/bookings/${finalBookingId}`,
          type: "property",
        });
      }
    } else if (type === "reminder") {
      // Calculate days until check-in
      const now = new Date();
      const checkInDate = new Date(booking.check_in);
      const daysUntil = Math.ceil(
        (checkInDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)
      );

      guestEmailContent = `
        <h1>Upcoming Booking Reminder</h1>
        <p>Dear ${booking.profiles.first_name || "Guest"},</p>
        <p>This is a friendly reminder that your stay at ${
          booking.properties.title
        } begins in ${daysUntil} day${daysUntil !== 1 ? "s" : ""}.</p>
        <ul>
          <li><strong>Property:</strong> ${booking.properties.title}</li>
          <li><strong>Location:</strong> ${booking.properties.location}</li>
          <li><strong>Check-in:</strong> ${checkIn}</li>
          <li><strong>Check-out:</strong> ${checkOut}</li>
        </ul>
        <p>We look forward to hosting you!</p>
        <p>Regards,<br/>GescosStay</p>
      `;
    } else if (type === "checkout_reminder") {
      guestEmailContent = `
        <h1>Checkout Reminder</h1>
        <p>Dear ${booking.profiles.first_name || "Guest"},</p>
        <p>This is a friendly reminder that your checkout time is approaching in approximately 2 hours.</p>
        <ul>
          <li><strong>Property:</strong> ${booking.properties.title}</li>
          <li><strong>Location:</strong> ${booking.properties.location}</li>
          <li><strong>Checkout Date:</strong> ${checkOut}</li>
        </ul>
        <p>Please ensure you have all your belongings and that the property is left in good condition.</p>
        <p>Thank you for staying with us!</p>
        <p>Regards,<br/>GescosStay</p>
      `;
    } else if (type === "status_update" && status) {
      guestEmailContent = `
        <h1>Booking Status Update</h1>
        <p>Dear ${booking.profiles.first_name || "Guest"},</p>
        <p>The status of your booking at ${
          booking.properties.title
        } has been updated to: <strong>${status}</strong>.</p>
        <ul>
          <li><strong>Property:</strong> ${booking.properties.title}</li>
          <li><strong>Location:</strong> ${booking.properties.location}</li>
          <li><strong>Check-in:</strong> ${checkIn}</li>
          <li><strong>Check-out:</strong> ${checkOut}</li>
        </ul>
        <p>If you have any questions about this update, please contact us.</p>
        <p>Regards,<br/>GescosStay</p>
      `;
    }

    // Prepare email to owner (property owner or car owner)
    let ownerEmailContent = "";
    if (type === "confirmation") {
      if (isCarBooking) {
        ownerEmailContent = `
          <h1>New Car Rental Booking</h1>
          <p>Dear ${owner?.first_name || "Owner"},</p>
          <p>You have a new confirmed car rental booking. Here are the details:</p>
          <ul>
            <li><strong>Car:</strong> ${booking.cars.make} ${
          booking.cars.model
        }</li>
            <li><strong>Renter Name:</strong> ${booking.profiles.first_name} ${
          booking.profiles.last_name
        }</li>
            <li><strong>Start Date:</strong> ${startDate}</li>
            <li><strong>End Date:</strong> ${endDate}</li>
            <li><strong>Duration Type:</strong> ${booking.duration_type}</li>
            <li><strong>Total Amount:</strong> $${booking.total_price}</li>
          </ul>
          <p>Please prepare your car for the rental period.</p>
          <p>Regards,<br/>GescosStay</p>
        `;
      } else {
        ownerEmailContent = `
          <h1>New Booking Notification</h1>
          <p>Dear ${owner?.first_name || "Owner"},</p>
          <p>You have a new confirmed booking for your property. Here are the details:</p>
          <ul>
            <li><strong>Property:</strong> ${booking.properties.title}</li>
            <li><strong>Guest Name:</strong> ${booking.profiles.first_name} ${
          booking.profiles.last_name
        }</li>
            <li><strong>Check-in:</strong> ${checkIn}</li>
            <li><strong>Check-out:</strong> ${checkOut}</li>
            <li><strong>Total Amount:</strong> $${booking.total_price}</li>
          </ul>
          <p>Please prepare for your guest's arrival.</p>
          <p>Regards,<br/>GescosStay</p>
        `;
      }
    } else if (type === "reminder") {
      // Calculate days until check-in
      const now = new Date();
      const checkInDate = new Date(booking.check_in);
      const daysUntil = Math.ceil(
        (checkInDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)
      );

      ownerEmailContent = `
        <h1>Upcoming Guest Arrival Reminder</h1>
        <p>Dear ${owner?.first_name || "Owner"},</p>
        <p>This is a friendly reminder that your guest will be arriving at ${
          booking.properties.title
        } in ${daysUntil} day${daysUntil !== 1 ? "s" : ""}.</p>
        <ul>
          <li><strong>Property:</strong> ${booking.properties.title}</li>
          <li><strong>Guest Name:</strong> ${booking.profiles.first_name} ${
        booking.profiles.last_name
      }</li>
          <li><strong>Check-in:</strong> ${checkIn}</li>
          <li><strong>Check-out:</strong> ${checkOut}</li>
        </ul>
        <p>Please ensure everything is ready for their arrival.</p>
        <p>Regards,<br/>GescosStay</p>
      `;
    } else if (type === "status_update" && status) {
      ownerEmailContent = `
        <h1>Booking Status Update</h1>
        <p>Dear ${owner?.first_name || "Owner"},</p>
        <p>The status of a booking for your property ${
          booking.properties.title
        } has been updated to: <strong>${status}</strong>.</p>
        <ul>
          <li><strong>Property:</strong> ${booking.properties.title}</li>
          <li><strong>Guest Name:</strong> ${booking.profiles.first_name} ${
        booking.profiles.last_name
      }</li>
          <li><strong>Check-in:</strong> ${checkIn}</li>
          <li><strong>Check-out:</strong> ${checkOut}</li>
        </ul>
        <p>Regards,<br/>GescosStay</p>
      `;
    }

    const emailPromises = [];
    const fromEmail = getFromEmail(type || "confirmation");

    // Send email to guest if they want to receive this type of email
    if (shouldSendToGuest && booking.profiles.email) {
      const guestEmailPromise = sendEmailWithSES(
        booking.profiles.email,
        guestSubject,
        guestEmailContent,
        fromEmail
      );
      emailPromises.push(guestEmailPromise);
    }

    // Send email to property owner if they want to receive this type of email
    if (shouldSendToOwner && owner?.email) {
      const ownerEmailPromise = sendEmailWithSES(
        owner.email,
        ownerSubject,
        ownerEmailContent,
        fromEmail
      );
      emailPromises.push(ownerEmailPromise);
    }

    // Wait for all emails to be sent
    await Promise.all(emailPromises);

    return new Response(
      JSON.stringify({
        success: true,
        message: "Email notifications sent successfully",
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200,
      }
    );
  } catch (error) {
    console.error("Error sending email notifications:", error);
    return new Response(
      JSON.stringify({ success: false, error: error.message }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 500,
      }
    );
  }
});
