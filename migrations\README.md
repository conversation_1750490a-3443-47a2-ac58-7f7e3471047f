# Supabase Project Migration Guide

This directory contains all the necessary files and scripts to migrate the complete Gesco Stay Supabase project to a new organization.

## Migration Structure

```
migrations/
├── README.md                           # This file
├── 01_database/                        # Database schema and data
│   ├── schema/                         # Database schema files
│   │   ├── 01_tables.sql              # All table definitions
│   │   ├── 02_functions.sql           # All database functions
│   │   ├── 03_triggers.sql            # All triggers
│   │   ├── 04_rls_policies.sql        # Row Level Security policies
│   │   ├── 05_indexes.sql             # Database indexes
│   │   └── 06_views.sql               # Database views
│   ├── data/                          # Data export/import scripts
│   │   ├── export_data.sql            # Script to export data from current project
│   │   ├── import_data.sql            # Script to import data to new project
│   │   └── sample_data.sql            # Sample/dummy data for testing
│   └── migrations/                    # Original migration files
├── 02_edge_functions/                  # Edge functions
│   ├── send-booking-confirmation/     # Booking confirmation function
│   ├── send-booking-reminders/        # Booking reminders function
│   ├── process-stripe-payment/        # Stripe payment processing
│   ├── verify-stripe-payment/         # Stripe payment verification
│   ├── process-wave-payment/          # Wave payment processing
│   └── verify-wave-payment/           # Wave payment verification
├── 03_storage/                        # Storage configuration
│   ├── buckets.sql                    # Storage bucket creation
│   └── policies.sql                   # Storage policies
├── 04_config/                         # Configuration files
│   ├── config.toml                    # Supabase configuration
│   ├── environment_variables.md       # Required environment variables
│   └── auth_settings.sql              # Auth configuration
├── 05_scripts/                        # Migration scripts
│   ├── migrate.sh                     # Main migration script
│   ├── setup_new_project.sh           # New project setup
│   ├── export_current.sh              # Export from current project
│   └── import_to_new.sh               # Import to new project
└── migration_checklist.md             # Step-by-step migration checklist
```

## Migration Process

1. **Preparation**: Review all files and update configuration
2. **Export**: Export current project data and configuration
3. **Setup**: Create new Supabase project in target organization
4. **Import**: Import schema, functions, and data to new project
5. **Configure**: Set up edge functions, storage, and auth
6. **Test**: Verify all functionality works correctly
7. **Deploy**: Update application configuration to use new project

## Important Notes

- Update all environment variables and API keys
- Test thoroughly before switching production traffic
- Keep backup of current project during migration
- Update DNS and domain settings if applicable

## Current Project Information

- **Project ID**: meakrzwthtkkumudxhzv
- **Project Name**: Gesco stay
- **Region**: ap-southeast-1
- **Organization**: lwxbdgnktzsmmaghugyx
- **Database Version**: PostgreSQL 15.8.1.073

## Required Environment Variables

See `04_config/environment_variables.md` for complete list of required environment variables.
