-- Fix Admin Approval Issue for Gesco Stay
-- This script addresses the issue where admin approvals appear to work but revert after page reload

-- 1. First, let's check the current state of the problematic property
SELECT 'Current property status' as check_type, id, title, status, updated_at, owner_id 
FROM properties 
WHERE id = 'a75a72e9-ea8b-498b-a893-aaddf95fb8bc';

-- 2. Check all current RLS policies for properties table
SELECT 'Current RLS Policies' as check_type, policyname, permissive, cmd, qual, with_check 
FROM pg_policies 
WHERE tablename = 'properties' 
ORDER BY policyname;

-- 3. Drop potentially conflicting policies and recreate them properly
DROP POLICY IF EXISTS "Users can update their own properties" ON public.properties;
DROP POLICY IF EXISTS "Admins can update all properties" ON public.properties;

-- 4. Recreate the policies with proper precedence (admin policies first)
CREATE POLICY "Admins can update all properties" ON public.properties
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

CREATE POLICY "Users can update their own properties" ON public.properties
  FOR UPDATE USING (auth.uid() = owner_id);

-- 5. Do the same for cars table
DROP POLICY IF EXISTS "Car owners can update their cars" ON public.cars;
DROP POLICY IF EXISTS "Admins can update all cars" ON public.cars;

CREATE POLICY "Admins can update all cars" ON public.cars
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

CREATE POLICY "Car owners can update their cars" ON public.cars
  FOR UPDATE USING (auth.uid() = owner_id);

-- 6. Add missing hotel policies
DROP POLICY IF EXISTS "Hotel owners can update their hotels" ON public.hotels;
DROP POLICY IF EXISTS "Admins can update all hotels" ON public.hotels;

CREATE POLICY "Admins can update all hotels" ON public.hotels
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

CREATE POLICY "Hotel owners can update their hotels" ON public.hotels
  FOR UPDATE USING (auth.uid() = owner_id);

-- 7. Ensure the is_admin function is working correctly
CREATE OR REPLACE FUNCTION is_admin(uid UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = uid AND role = 'admin'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 8. Grant explicit permissions
GRANT SELECT, UPDATE ON public.properties TO authenticated;
GRANT SELECT, UPDATE ON public.cars TO authenticated;
GRANT SELECT, UPDATE ON public.hotels TO authenticated;

-- 9. Test the admin function with the current admin user
SELECT 'Admin function test' as check_type, 
       email, 
       role, 
       is_admin(id) as is_admin_result 
FROM profiles 
WHERE email = '<EMAIL>';

-- 10. Test update on the problematic property (replace with actual admin user ID)
-- First get the admin user ID
DO $$
DECLARE
    admin_user_id UUID;
    test_property_id UUID := 'a75a72e9-ea8b-498b-a893-aaddf95fb8bc';
BEGIN
    -- Get admin user ID
    SELECT id INTO admin_user_id 
    FROM profiles 
    WHERE email = '<EMAIL>' AND role = 'admin';
    
    IF admin_user_id IS NOT NULL THEN
        -- Test if we can update the property
        UPDATE properties 
        SET status = 'approved', 
            updated_at = NOW()
        WHERE id = test_property_id;
        
        RAISE NOTICE 'Update test completed for property: %', test_property_id;
        RAISE NOTICE 'Admin user ID: %', admin_user_id;
    ELSE
        RAISE NOTICE 'Admin user not found!';
    END IF;
END $$;

-- 11. Verify the update worked
SELECT 'After update test' as check_type, id, title, status, updated_at 
FROM properties 
WHERE id = 'a75a72e9-ea8b-498b-a893-aaddf95fb8bc';

-- 12. Check for any triggers that might be interfering
SELECT 'Triggers check' as check_type, 
       trigger_name, 
       event_manipulation, 
       action_timing, 
       action_statement 
FROM information_schema.triggers 
WHERE event_object_table = 'properties';

-- 13. Final verification - check all policies are in place
SELECT 'Final policy check' as check_type, 
       tablename, 
       policyname, 
       cmd, 
       permissive 
FROM pg_policies 
WHERE tablename IN ('properties', 'cars', 'hotels') 
AND policyname LIKE '%admin%' 
ORDER BY tablename, policyname;
