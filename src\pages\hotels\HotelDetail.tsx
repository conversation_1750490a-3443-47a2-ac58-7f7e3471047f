import { useParams } from "react-router-dom";
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  MapPin,
  Star,
  Clock,
  Bed,
  Users,
  Wifi,
  Car,
  TestTube,
} from "lucide-react";
import { Tables } from "@/integrations/supabase/types";
import HotelBookingWidget from "@/components/hotels/HotelBookingWidget";
import Reviews from "@/components/properties/Reviews";

type Hotel = Tables<"hotels">;
type RoomType = Tables<"room_types">;

interface HotelWithRoomTypes extends Hotel {
  room_types: RoomType[];
  avg_rating?: number;
  review_count?: number;
}

const HotelDetail = () => {
  const { id } = useParams<{ id: string }>();
  const [hotel, setHotel] = useState<HotelWithRoomTypes | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (id) {
      fetchHotelDetails();
    }
  }, [id]);

  const fetchHotelDetails = async () => {
    try {
      setIsLoading(true);

      // Fetch hotel with room types
      const { data: hotelData, error: hotelError } = await supabase
        .from("hotels")
        .select(
          `
          *,
          room_types (*)
        `
        )
        .eq("id", id)
        .single();

      if (hotelError) throw hotelError;

      // Fetch reviews to calculate rating
      const { data: reviewsData, error: reviewsError } = await supabase
        .from("reviews")
        .select("rating")
        .eq("hotel_id", id);

      if (reviewsError) throw reviewsError;

      const avg_rating =
        reviewsData.length > 0
          ? reviewsData.reduce((sum, review) => sum + review.rating, 0) /
            reviewsData.length
          : 0;

      setHotel({
        ...hotelData,
        avg_rating,
        review_count: reviewsData.length,
      });
    } catch (error) {
      console.error("Error fetching hotel details:", error);
      setError("Failed to load hotel details");
    } finally {
      setIsLoading(false);
    }
  };

  const formatTime = (time: string) => {
    try {
      const [hours, minutes] = time.split(":");
      const hour = parseInt(hours);
      const ampm = hour >= 12 ? "PM" : "AM";
      const displayHour = hour % 12 || 12;
      return `${displayHour}:${minutes} ${ampm}`;
    } catch {
      return time;
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${
          i < rating ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
        }`}
      />
    ));
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="container mx-auto px-4 py-8">
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-accent"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !hotel) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              Hotel Not Found
            </h1>
            <p className="text-gray-600">
              The hotel you're looking for doesn't exist or has been removed.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />

      <main className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-6 relative">
          {hotel.is_dummy && (
            <div className="absolute top-0 right-0 z-10">
              <Badge className="bg-orange-100 text-orange-800 flex items-center gap-1">
                <TestTube className="w-3 h-3" />
                Display Only
              </Badge>
            </div>
          )}
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                {hotel.title}
              </h1>
              <div className="flex items-center text-gray-600 mb-2">
                <MapPin className="w-5 h-5 mr-2" />
                <span>{hotel.location}</span>
              </div>
              <div className="flex items-center gap-4">
                {hotel.avg_rating && hotel.avg_rating > 0 && (
                  <div className="flex items-center">
                    <Star className="w-4 h-4 fill-yellow-400 text-yellow-400 mr-1" />
                    <span className="font-medium">
                      {hotel.avg_rating.toFixed(1)}
                    </span>
                    <span className="text-gray-500 ml-1">
                      ({hotel.review_count} reviews)
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Images Gallery */}
        {hotel.images && hotel.images.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
            <div className="aspect-[4/3] rounded-lg overflow-hidden">
              <img
                src={hotel.images[0]}
                alt={hotel.title}
                className="w-full h-full object-cover"
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              {hotel.images.slice(1, 5).map((image, index) => (
                <div
                  key={index}
                  className="aspect-square rounded-lg overflow-hidden"
                >
                  <img
                    src={image}
                    alt={`${hotel.title} - ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Description */}
            <Card>
              <CardHeader>
                <CardTitle>About This Hotel</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 leading-relaxed">
                  {hotel.description}
                </p>
              </CardContent>
            </Card>

            {/* Check-in/Check-out */}
            <Card>
              <CardHeader>
                <CardTitle>Check-in & Check-out</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center gap-2">
                    <Clock className="w-5 h-5 text-gray-500" />
                    <div>
                      <p className="font-medium">Check-in</p>
                      <p className="text-gray-600">
                        {formatTime(hotel.check_in_time)}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="w-5 h-5 text-gray-500" />
                    <div>
                      <p className="font-medium">Check-out</p>
                      <p className="text-gray-600">
                        {formatTime(hotel.check_out_time)}
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Amenities */}
            {hotel.amenities && hotel.amenities.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Hotel Amenities</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    {hotel.amenities.map((amenity) => (
                      <div key={amenity} className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-accent rounded-full"></div>
                        <span className="text-sm">{amenity}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Room Types */}
            {hotel.room_types && hotel.room_types.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Available Room Types</CardTitle>
                  <CardDescription>
                    Choose from our selection of comfortable rooms
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {hotel.room_types.map((roomType) => (
                      <div
                        key={roomType.id}
                        className="border rounded-lg overflow-hidden"
                      >
                        {/* Room Images */}
                        {roomType.images && roomType.images.length > 0 && (
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-2 p-4 pb-0">
                            <div className="md:col-span-2">
                              <img
                                src={roomType.images[0]}
                                alt={roomType.name}
                                className="w-full h-48 object-cover rounded-lg"
                              />
                            </div>
                            <div className="grid grid-cols-2 md:grid-cols-1 gap-2">
                              {roomType.images
                                .slice(1, 3)
                                .map((image, index) => (
                                  <img
                                    key={index}
                                    src={image}
                                    alt={`${roomType.name} - ${index + 1}`}
                                    className="w-full h-24 md:h-[92px] object-cover rounded-lg"
                                  />
                                ))}
                            </div>
                          </div>
                        )}

                        <div className="p-4">
                          <div className="flex justify-between items-start mb-3">
                            <div>
                              <h4 className="font-semibold text-lg">
                                {roomType.name}
                              </h4>
                              {roomType.description && (
                                <p className="text-gray-600 text-sm mt-1">
                                  {roomType.description}
                                </p>
                              )}
                            </div>
                            <div className="text-right">
                              <div className="text-2xl font-bold text-accent">
                                ${roomType.base_price}
                              </div>
                              <div className="text-sm text-gray-500">
                                per night
                              </div>
                            </div>
                          </div>

                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-3">
                            <div className="flex items-center gap-2">
                              <Users className="w-4 h-4 text-gray-500" />
                              <span className="text-sm">
                                Max {roomType.max_occupancy} guests
                              </span>
                            </div>
                            {roomType.bed_configuration && (
                              <div className="flex items-center gap-2">
                                <Bed className="w-4 h-4 text-gray-500" />
                                <span className="text-sm">
                                  {roomType.bed_configuration}
                                </span>
                              </div>
                            )}
                            {roomType.room_size_sqm && (
                              <div className="text-sm text-gray-600">
                                {roomType.room_size_sqm} sqm
                              </div>
                            )}
                          </div>

                          {roomType.amenities &&
                            roomType.amenities.length > 0 && (
                              <div className="flex flex-wrap gap-1">
                                {roomType.amenities.map((amenity) => (
                                  <Badge
                                    key={amenity}
                                    variant="secondary"
                                    className="text-xs"
                                  >
                                    {amenity}
                                  </Badge>
                                ))}
                              </div>
                            )}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Reviews */}
            <Reviews hotelId={hotel.id} entityType="hotel" />
          </div>

          {/* Booking Widget */}
          <div className="lg:col-span-1">
            <div className="sticky top-8">
              <HotelBookingWidget hotel={hotel} />
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default HotelDetail;
