-- Add phone number support to profiles table
-- This migration adds phone_number column to support mobile authentication

-- Add phone_number column to profiles table
ALTER TABLE public.profiles 
ADD COLUMN IF NOT EXISTS phone_number TEXT;

-- Create index for phone number lookups
CREATE INDEX IF NOT EXISTS idx_profiles_phone_number ON public.profiles(phone_number);

-- Add constraint to ensure phone number format (basic validation)
-- This allows international phone numbers with optional + prefix
ALTER TABLE public.profiles 
ADD CONSTRAINT check_phone_number_format 
CHECK (phone_number IS NULL OR phone_number ~ '^\+?[0-9]{8,15}$');

-- Update the updated_at timestamp when phone_number is modified
CREATE OR REPLACE FUNCTION update_profiles_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger if it doesn't exist
DROP TRIGGER IF EXISTS profiles_updated_at_trigger ON public.profiles;
CREATE TRIGGER profiles_updated_at_trigger
  BEFORE UPDATE ON public.profiles
  FOR EACH ROW
  EXECUTE FUNCTION update_profiles_updated_at();
