import { useState } from "react";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import { useCarListings } from "@/hooks/useCarListings";
import CarCard from "@/components/cars/CarCard";
import CarFilters from "@/components/cars/CarFilters";
import { Database } from "@/integrations/supabase/types";
import { Button } from "@/components/ui/button";
import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Link } from "react-router-dom";

const CarsListingPage = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [filters, setFilters] = useState<{
    type?: Database["public"]["Enums"]["car_type"];
    location?: string;
    minPrice?: number;
    maxPrice?: number;
    seats?: number;
  }>({});

  const { data: cars = [], isLoading, error } = useCarListings(filters);

  const filteredCars = cars.filter(
    (car) =>
      car.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      car.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
      `${car.make} ${car.model}`
        .toLowerCase()
        .includes(searchTerm.toLowerCase())
  );

  return (
    <div className="min-h-screen bg-white">
      <Navbar />

      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-4">Car Rentals in Africa</h1>
        <p className="text-muted-foreground mb-8">
          Find the perfect car for your trip in Africa. From economy to luxury
          vehicles.
        </p>

        <div className="flex flex-col sm:flex-row gap-4 mb-8">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search cars by name, model or location..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          <CarFilters onFilterChange={setFilters} />
        </div>

        {isLoading ? (
          <div className="flex justify-center py-20">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent"></div>
          </div>
        ) : error ? (
          <div className="text-center py-10">
            <h3 className="text-lg font-semibold text-red-600">
              Error loading cars
            </h3>
            <p className="text-muted-foreground">
              {error ? (error as Error).message : "Failed to load cars"}
            </p>
            <Button
              variant="outline"
              onClick={() => window.location.reload()}
              className="mt-4"
            >
              Try Again
            </Button>
          </div>
        ) : filteredCars.length === 0 ? (
          <div className="text-center py-10">
            <h3 className="text-lg font-semibold">No cars found</h3>
            <p className="text-muted-foreground">
              Try changing your filters or search criteria
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredCars.map((car) => (
              <CarCard key={car.id} car={car} />
            ))}
          </div>
        )}
      </div>

      <Footer />
    </div>
  );
};

export default CarsListingPage;
