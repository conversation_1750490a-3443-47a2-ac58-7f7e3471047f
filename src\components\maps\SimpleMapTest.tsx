import React, { useRef, useEffect, useState } from 'react';
import { GoogleMapsProvider } from './GoogleMapsProvider';
import { isGoogleMapsLoaded, getDefaultMapOptions, DEFAULT_CENTER } from '@/lib/google-maps';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

/**
 * Simple test component to verify Google Maps is working
 * This component tests basic map functionality without complex features
 */
const SimpleMapTestComponent: React.FC = () => {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<google.maps.Map | null>(null);
  const markerRef = useRef<google.maps.Marker | null>(null);
  
  const [mapLoaded, setMapLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Initialize basic map
  const initializeMap = () => {
    if (!mapRef.current || mapInstanceRef.current) return;

    try {
      const mapOptions = {
        ...getDefaultMapOptions(),
        center: DEFAULT_CENTER,
        zoom: 10,
      };

      mapInstanceRef.current = new google.maps.Map(mapRef.current, mapOptions);
      setMapLoaded(true);
      setError(null);

      // Add click listener
      mapInstanceRef.current.addListener('click', (event: google.maps.MapMouseEvent) => {
        if (event.latLng) {
          addSimpleMarker(event.latLng.lat(), event.latLng.lng());
        }
      });

      console.log('Map initialized successfully');
    } catch (err: any) {
      console.error('Error initializing map:', err);
      setError(`Failed to initialize map: ${err.message}`);
    }
  };

  // Add a simple marker
  const addSimpleMarker = (lat: number, lng: number) => {
    if (!mapInstanceRef.current) return;

    try {
      // Remove existing marker
      if (markerRef.current) {
        markerRef.current.setMap(null);
      }

      // Add simple marker
      markerRef.current = new google.maps.Marker({
        position: { lat, lng },
        map: mapInstanceRef.current,
        title: `Location: ${lat.toFixed(4)}, ${lng.toFixed(4)}`,
      });

      console.log('Marker added at:', lat, lng);
    } catch (err: any) {
      console.error('Error adding marker:', err);
      setError(`Failed to add marker: ${err.message}`);
    }
  };

  // Test current location
  const testCurrentLocation = () => {
    if (!navigator.geolocation) {
      setError('Geolocation is not supported by this browser');
      return;
    }

    navigator.geolocation.getCurrentPosition(
      (position) => {
        const lat = position.coords.latitude;
        const lng = position.coords.longitude;
        
        if (mapInstanceRef.current) {
          mapInstanceRef.current.setCenter({ lat, lng });
          mapInstanceRef.current.setZoom(15);
          addSimpleMarker(lat, lng);
        }
        
        console.log('Current location:', lat, lng);
      },
      (error) => {
        console.error('Geolocation error:', error);
        setError(`Geolocation error: ${error.message}`);
      }
    );
  };

  // Initialize map when Google Maps is loaded
  useEffect(() => {
    if (isGoogleMapsLoaded()) {
      initializeMap();
    }
  }, []);

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>Simple Google Maps Test</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {error && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-700 font-medium">Error:</p>
            <p className="text-red-600 text-sm">{error}</p>
          </div>
        )}

        <div className="space-y-2">
          <p className="text-sm text-gray-600">
            Google Maps Loaded: {isGoogleMapsLoaded() ? '✅ Yes' : '❌ No'}
          </p>
          <p className="text-sm text-gray-600">
            Map Initialized: {mapLoaded ? '✅ Yes' : '❌ No'}
          </p>
        </div>

        <div className="flex gap-2">
          <Button onClick={testCurrentLocation} variant="outline">
            Test Current Location
          </Button>
          <Button 
            onClick={() => addSimpleMarker(DEFAULT_CENTER.lat, DEFAULT_CENTER.lng)} 
            variant="outline"
          >
            Add Test Marker
          </Button>
        </div>

        <div 
          ref={mapRef} 
          className="w-full h-96 border border-gray-300 rounded-lg bg-gray-100"
        />

        <div className="text-sm text-gray-600 space-y-1">
          <p><strong>Instructions:</strong></p>
          <ul className="list-disc list-inside space-y-1">
            <li>The map should load above</li>
            <li>Click anywhere on the map to add a marker</li>
            <li>Use "Test Current Location" to center on your location</li>
            <li>Use "Add Test Marker" to add a marker at the default location</li>
            <li>Check the browser console for detailed logs</li>
          </ul>
        </div>

        <div className="text-xs text-gray-500 space-y-1">
          <p><strong>Debug Info:</strong></p>
          <p>API Key: {import.meta.env.VITE_GOOGLE_MAPS_API_KEY ? '✅ Set' : '❌ Not Set'}</p>
          <p>Window.google: {typeof window !== 'undefined' && window.google ? '✅ Available' : '❌ Not Available'}</p>
          <p>Google Maps: {typeof window !== 'undefined' && window.google?.maps ? '✅ Available' : '❌ Not Available'}</p>
          <p>Places API: {typeof window !== 'undefined' && window.google?.maps?.places ? '✅ Available' : '❌ Not Available'}</p>
          <p>PlaceAutocompleteElement: {typeof window !== 'undefined' && window.google?.maps?.places?.PlaceAutocompleteElement ? '✅ Available' : '❌ Not Available'}</p>
        </div>
      </CardContent>
    </Card>
  );
};

export const SimpleMapTest: React.FC = () => {
  return (
    <GoogleMapsProvider>
      <SimpleMapTestComponent />
    </GoogleMapsProvider>
  );
};
