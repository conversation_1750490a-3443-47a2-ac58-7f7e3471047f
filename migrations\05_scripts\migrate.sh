#!/bin/bash

# Gesco Stay Supabase Migration Script
# This script orchestrates the complete migration process

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
MIGRATION_ROOT="$(dirname "$SCRIPT_DIR")"
OLD_PROJECT_ID="meakrzwthtkkumudxhzv"
NEW_PROJECT_ID=""
NEW_PROJECT_URL=""
NEW_ANON_KEY=""
NEW_SERVICE_ROLE_KEY=""

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_dependencies() {
    log_info "Checking dependencies..."
    
    # Check if supabase CLI is installed
    if ! command -v supabase &> /dev/null; then
        log_error "Supabase CLI is not installed. Please install it first."
        exit 1
    fi
    
    # Check if psql is installed
    if ! command -v psql &> /dev/null; then
        log_error "PostgreSQL client (psql) is not installed. Please install it first."
        exit 1
    fi
    
    log_success "All dependencies are available"
}

get_project_info() {
    log_info "Please provide the new project information:"
    
    read -p "New Project ID: " NEW_PROJECT_ID
    read -p "New Project URL (https://xxx.supabase.co): " NEW_PROJECT_URL
    read -p "New Anon Key: " NEW_ANON_KEY
    read -s -p "New Service Role Key: " NEW_SERVICE_ROLE_KEY
    echo
    
    if [[ -z "$NEW_PROJECT_ID" || -z "$NEW_PROJECT_URL" || -z "$NEW_ANON_KEY" || -z "$NEW_SERVICE_ROLE_KEY" ]]; then
        log_error "All project information is required"
        exit 1
    fi
    
    log_success "Project information collected"
}

backup_current_project() {
    log_info "Creating backup of current project..."
    
    # Create backup directory
    BACKUP_DIR="$MIGRATION_ROOT/backup_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # Export data from current project
    log_info "Exporting data from current project..."
    cd "$MIGRATION_ROOT/01_database/data"
    
    # Note: This would need to be run against the actual database
    log_warning "Please run the export_data.sql script against your current project manually"
    log_warning "Or use the Supabase CLI: supabase db dump --project-id $OLD_PROJECT_ID"
    
    log_success "Backup preparation completed"
}

create_database_schema() {
    log_info "Creating database schema in new project..."
    
    # Connect to new project and run schema scripts
    DB_URL="${NEW_PROJECT_URL/https:\/\//postgresql://postgres:}@db.${NEW_PROJECT_ID}.supabase.co:5432/postgres"
    
    log_info "Running table creation..."
    psql "$DB_URL" -f "$MIGRATION_ROOT/01_database/schema/01_tables.sql" || {
        log_error "Failed to create tables"
        exit 1
    }
    
    log_info "Running function creation..."
    psql "$DB_URL" -f "$MIGRATION_ROOT/01_database/schema/02_functions.sql" || {
        log_error "Failed to create functions"
        exit 1
    }
    
    log_info "Running trigger creation..."
    psql "$DB_URL" -f "$MIGRATION_ROOT/01_database/schema/03_triggers.sql" || {
        log_error "Failed to create triggers"
        exit 1
    }
    
    log_info "Running RLS policies creation..."
    psql "$DB_URL" -f "$MIGRATION_ROOT/01_database/schema/04_rls_policies.sql" || {
        log_error "Failed to create RLS policies"
        exit 1
    }
    
    log_info "Running indexes creation..."
    psql "$DB_URL" -f "$MIGRATION_ROOT/01_database/schema/05_indexes.sql" || {
        log_error "Failed to create indexes"
        exit 1
    }
    
    log_info "Running views creation..."
    psql "$DB_URL" -f "$MIGRATION_ROOT/01_database/schema/06_views.sql" || {
        log_error "Failed to create views"
        exit 1
    }
    
    log_success "Database schema created successfully"
}

setup_storage() {
    log_info "Setting up storage buckets and policies..."
    
    DB_URL="${NEW_PROJECT_URL/https:\/\//postgresql://postgres:}@db.${NEW_PROJECT_ID}.supabase.co:5432/postgres"
    
    log_info "Creating storage buckets..."
    psql "$DB_URL" -f "$MIGRATION_ROOT/03_storage/buckets.sql" || {
        log_error "Failed to create storage buckets"
        exit 1
    }
    
    log_info "Creating storage policies..."
    psql "$DB_URL" -f "$MIGRATION_ROOT/03_storage/policies.sql" || {
        log_error "Failed to create storage policies"
        exit 1
    }
    
    log_success "Storage setup completed"
}

deploy_edge_functions() {
    log_info "Deploying edge functions..."
    
    # Set up Supabase CLI for new project
    export SUPABASE_ACCESS_TOKEN="$NEW_SERVICE_ROLE_KEY"
    
    # Deploy each function
    FUNCTIONS_DIR="$MIGRATION_ROOT/02_edge_functions"
    
    for func_dir in "$FUNCTIONS_DIR"/*; do
        if [[ -d "$func_dir" ]]; then
            func_name=$(basename "$func_dir")
            log_info "Deploying function: $func_name"
            
            supabase functions deploy "$func_name" \
                --project-ref "$NEW_PROJECT_ID" \
                --source "$func_dir" || {
                log_error "Failed to deploy function: $func_name"
                exit 1
            }
        fi
    done
    
    log_success "Edge functions deployed successfully"
}

import_data() {
    log_info "Importing data to new project..."
    
    DB_URL="${NEW_PROJECT_URL/https:\/\//postgresql://postgres:}@db.${NEW_PROJECT_ID}.supabase.co:5432/postgres"
    
    log_warning "Please ensure you have exported data from the old project first"
    read -p "Have you exported the data? (y/N): " confirm
    
    if [[ $confirm != [yY] ]]; then
        log_warning "Skipping data import. You can run import_data.sql manually later."
        return
    fi
    
    cd "$MIGRATION_ROOT/01_database/data"
    psql "$DB_URL" -f "import_data.sql" || {
        log_error "Failed to import data"
        exit 1
    }
    
    log_success "Data import completed"
}

update_configuration() {
    log_info "Updating configuration files..."
    
    # Update config.toml
    sed -i.bak "s/project_id = \"gesco-stay-new\"/project_id = \"$NEW_PROJECT_ID\"/" \
        "$MIGRATION_ROOT/04_config/config.toml"
    
    # Create environment file
    cat > "$MIGRATION_ROOT/.env.new" << EOF
# New Project Environment Variables
VITE_SUPABASE_URL=$NEW_PROJECT_URL
VITE_SUPABASE_ANON_KEY=$NEW_ANON_KEY
VITE_SUPABASE_SERVICE_ROLE_KEY=$NEW_SERVICE_ROLE_KEY

# Copy these to your application's .env file
EOF
    
    log_success "Configuration files updated"
}

verify_migration() {
    log_info "Verifying migration..."
    
    # Test basic connectivity
    curl -s -H "apikey: $NEW_ANON_KEY" \
         -H "Authorization: Bearer $NEW_ANON_KEY" \
         "$NEW_PROJECT_URL/rest/v1/profiles" > /dev/null || {
        log_error "Failed to connect to new project API"
        exit 1
    }
    
    log_success "Basic connectivity test passed"
    
    # More verification steps can be added here
    log_info "Please run additional tests to verify all functionality"
}

main() {
    log_info "Starting Gesco Stay Supabase Migration"
    log_info "======================================"
    
    check_dependencies
    get_project_info
    
    log_warning "This will migrate your Supabase project. Make sure you have:"
    log_warning "1. Created a new Supabase project"
    log_warning "2. Backed up your current project"
    log_warning "3. Have all necessary credentials"
    
    read -p "Continue with migration? (y/N): " confirm
    if [[ $confirm != [yY] ]]; then
        log_info "Migration cancelled"
        exit 0
    fi
    
    backup_current_project
    create_database_schema
    setup_storage
    deploy_edge_functions
    import_data
    update_configuration
    verify_migration
    
    log_success "Migration completed successfully!"
    log_info "Next steps:"
    log_info "1. Update your application's environment variables"
    log_info "2. Test all functionality thoroughly"
    log_info "3. Update DNS/domain settings if needed"
    log_info "4. Monitor the new project for 24-48 hours"
    
    log_info "Environment variables saved to: $MIGRATION_ROOT/.env.new"
}

# Run main function
main "$@"
