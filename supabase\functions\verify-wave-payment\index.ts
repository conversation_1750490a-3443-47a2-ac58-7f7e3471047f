import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.45.0";

const SUPABASE_URL = Deno.env.get("SUPABASE_URL") || "";
const SUPABASE_SERVICE_ROLE_KEY =
  Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") || "";
const WAVE_API_KEY = Deno.env.get("WAVE_API_KEY") || "";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { checkoutId, bookingId, bookingType } = await req.json();

    // Create Supabase client with admin privileges
    const supabaseAdmin = createClient(
      SUPABASE_URL,
      SUPABASE_SERVICE_ROLE_KEY,
      {
        auth: { persistSession: false },
      }
    );

    // Determine the booking table based on booking type
    const bookingTable = bookingType === "car" ? "car_bookings" : "bookings";

    // Retrieve checkout session from Wave
    const response = await fetch(
      `https://api.wave.com/v1/checkout/sessions/${checkoutId}`,
      {
        method: "GET",
        headers: {
          Authorization: `Bearer ${WAVE_API_KEY}`,
        },
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(
        `Wave API error: ${errorData.message || response.statusText}`
      );
    }

    const checkoutSession = await response.json();

    // Check if payment is successful
    const paymentStatus =
      checkoutSession.payment_status === "succeeded" ? "completed" : "pending";
    const checkoutStatus = checkoutSession.checkout_status;

    // Update booking status
    await supabaseAdmin
      .from(bookingTable)
      .update({
        payment_status: paymentStatus,
        payment_id: checkoutSession.transaction_id,
        status: paymentStatus === "completed" ? "confirmed" : "pending",
      })
      .eq("id", bookingId);

    // Update payment log
    await supabaseAdmin
      .from("payment_logs")
      .update({
        status: paymentStatus,
        provider_response: checkoutSession,
      })
      .eq("transaction_id", checkoutId);

    // If payment was successful, send confirmation email (only if not already sent)
    if (paymentStatus === "completed") {
      // Check if email was already sent to prevent duplicates
      const { data: existingBooking } = await supabaseAdmin
        .from(bookingTable)
        .select("email_sent")
        .eq("id", bookingId)
        .single();

      const emailAlreadySent = existingBooking?.email_sent || false;

      if (!emailAlreadySent) {
        // Call email notification function
        try {
          const emailResponse = await fetch(
            `${SUPABASE_URL}/functions/v1/send-booking-confirmation`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${SUPABASE_SERVICE_ROLE_KEY}`,
              },
              body: JSON.stringify(
                bookingType === "car"
                  ? {
                      carBookingId: bookingId,
                      type: "confirmation",
                    }
                  : {
                      bookingId,
                      type: "confirmation",
                    }
              ),
            }
          );

          const emailData = await emailResponse.json();
          console.log("Email notification sent:", emailData);

          // Mark email as sent to prevent future duplicates
          await supabaseAdmin
            .from(bookingTable)
            .update({ email_sent: true })
            .eq("id", bookingId);
        } catch (emailError) {
          console.error("Error sending email notification:", emailError);
        }
      } else {
        console.log(
          "Email already sent for this booking, skipping duplicate email"
        );
      }
    }

    return new Response(
      JSON.stringify({
        success: true,
        paymentStatus,
        checkoutStatus,
        transactionId: checkoutSession.transaction_id,
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200,
      }
    );
  } catch (error) {
    console.error("Error verifying Wave payment:", error);
    return new Response(
      JSON.stringify({ success: false, error: error.message }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 500,
      }
    );
  }
});
