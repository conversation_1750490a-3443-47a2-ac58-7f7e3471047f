import Navbar from "@/components/layout/Navbar";
import { Users, Star, Shield, Globe, Music, Calendar, MapPin, Heart } from "lucide-react";

const CommunityPage = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      <div className="container mx-auto px-4 py-12">
        <div className="max-w-6xl mx-auto">
          {/* Header Section */}
          <div className="text-center mb-16">
            <div className="inline-block px-6 py-2 bg-accent/10 rounded-full text-accent font-semibold text-sm mb-4">
              Community & Resources
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              GescoStay Community
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              We believe in building more than just a booking platform — we're cultivating a vibrant 
              community of hosts, travelers, service partners, and local guides across The Gambia and West Africa.
            </p>
          </div>

          {/* Community Sections */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16">
            {/* For Hosts */}
            <div className="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-shadow">
              <div className="flex items-center justify-center w-16 h-16 bg-accent/10 rounded-full mb-6 mx-auto">
                <Users className="h-8 w-8 text-accent" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4 text-center">For Hosts</h3>
              <ul className="space-y-3 text-gray-600">
                <li className="flex items-start">
                  <Star className="h-5 w-5 text-accent mt-0.5 mr-3 flex-shrink-0" />
                  <span><strong>Training & Onboarding:</strong> Access support to list your property professionally and securely.</span>
                </li>
                <li className="flex items-start">
                  <Shield className="h-5 w-5 text-accent mt-0.5 mr-3 flex-shrink-0" />
                  <span><strong>Host Tools:</strong> Use our dashboard to manage bookings, track earnings, and communicate with guests.</span>
                </li>
                <li className="flex items-start">
                  <Star className="h-5 w-5 text-accent mt-0.5 mr-3 flex-shrink-0" />
                  <span><strong>Premium Host Badge:</strong> Earn trust with higher ratings, verified identity, and consistent performance.</span>
                </li>
              </ul>
            </div>

            {/* For Travelers */}
            <div className="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-shadow">
              <div className="flex items-center justify-center w-16 h-16 bg-secondary/10 rounded-full mb-6 mx-auto">
                <Globe className="h-8 w-8 text-secondary" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4 text-center">For Travelers</h3>
              <ul className="space-y-3 text-gray-600">
                <li className="flex items-start">
                  <MapPin className="h-5 w-5 text-secondary mt-0.5 mr-3 flex-shrink-0" />
                  <span><strong>Local Experiences:</strong> Discover unique stays, cultural tours, and authentic local homes.</span>
                </li>
                <li className="flex items-start">
                  <Shield className="h-5 w-5 text-secondary mt-0.5 mr-3 flex-shrink-0" />
                  <span><strong>24/7 Support:</strong> We're here to assist with bookings, safety concerns, and recommendations.</span>
                </li>
                <li className="flex items-start">
                  <Star className="h-5 w-5 text-secondary mt-0.5 mr-3 flex-shrink-0" />
                  <span><strong>Verified Listings:</strong> All stays go through quality checks and compliance.</span>
                </li>
              </ul>
            </div>

            {/* For Partners */}
            <div className="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-shadow">
              <div className="flex items-center justify-center w-16 h-16 bg-brown/10 rounded-full mb-6 mx-auto">
                <Heart className="h-8 w-8 text-brown" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4 text-center">For Partners</h3>
              <ul className="space-y-3 text-gray-600">
                <li className="flex items-start">
                  <Users className="h-5 w-5 text-brown mt-0.5 mr-3 flex-shrink-0" />
                  <span><strong>Business Collaborations:</strong> We work with cleaning services, tour operators, transportation companies, and local vendors to support guest experiences.</span>
                </li>
                <li className="flex items-start">
                  <Globe className="h-5 w-5 text-brown mt-0.5 mr-3 flex-shrink-0" />
                  <span><strong>Co-Marketing Opportunities:</strong> Promote your service or event to GescoStay users and expand your reach.</span>
                </li>
              </ul>
            </div>
          </div>

          {/* Nightlife & Tourism Section */}
          <div className="bg-gradient-to-r from-purple-600 to-purple-800 rounded-2xl p-8 md:p-12 text-white mb-16">
            <div className="max-w-4xl mx-auto text-center">
              <div className="flex items-center justify-center w-20 h-20 bg-white/10 rounded-full mb-6 mx-auto">
                <Music className="h-10 w-10 text-white" />
              </div>
              <h2 className="text-3xl md:text-4xl font-bold mb-6">Nightlife & Tourism Promotion</h2>
              <p className="text-xl mb-8 opacity-90">
                The Gambia is more than sun and sand — it's also a place of rhythm, lights, and unforgettable evenings.
              </p>
              <p className="text-lg mb-8 opacity-80">
                GescoStay promotes safe, exciting nightlife options for international tourists, diaspora returnees, and local explorers alike.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
                <div className="bg-white/10 rounded-lg p-6">
                  <Music className="h-8 w-8 mb-4 mx-auto" />
                  <h4 className="font-semibold mb-2">Clubs & Lounges</h4>
                  <p className="text-sm opacity-80">Exclusive listings of top nightlife venues.</p>
                </div>
                <div className="bg-white/10 rounded-lg p-6">
                  <Calendar className="h-8 w-8 mb-4 mx-auto" />
                  <h4 className="font-semibold mb-2">Live Music & Cultural Nights</h4>
                  <p className="text-sm opacity-80">Access to performances showcasing Gambian talent.</p>
                </div>
                <div className="bg-white/10 rounded-lg p-6">
                  <MapPin className="h-8 w-8 mb-4 mx-auto" />
                  <h4 className="font-semibold mb-2">Event-Ready Stays</h4>
                  <p className="text-sm opacity-80">Book nearby accommodation during major nightlife events or festivals.</p>
                </div>
                <div className="bg-white/10 rounded-lg p-6">
                  <Globe className="h-8 w-8 mb-4 mx-auto" />
                  <h4 className="font-semibold mb-2">Tour Packages</h4>
                  <p className="text-sm opacity-80">Curated nightlife tours guided by locals for a truly immersive experience.</p>
                </div>
              </div>
              
              <p className="text-lg mt-8 opacity-90">
                Whether you're seeking a peaceful stay or a night out to remember, GescoStay connects you with the full experience of modern West African travel.
              </p>
            </div>
          </div>

          {/* Call to Action */}
          <div className="bg-white rounded-xl shadow-lg p-8 text-center">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Let's build the future of hospitality, culture, and tourism — together.
            </h3>
            <p className="text-gray-600 mb-6">
              For partnerships, inquiries, or support, reach out to us:
            </p>
            <a 
              href="mailto:<EMAIL>" 
              className="inline-flex items-center px-6 py-3 bg-accent hover:bg-accent/90 text-white font-semibold rounded-lg transition-colors"
            >
              Contact Us
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CommunityPage;
