import { useState } from "react";
import { useNavigate } from "react-router-dom";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import { useAuth } from "@/components/layout/AuthProvider";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import FeatureSelector from "@/components/features/FeatureSelector";
import { LocationPicker } from "@/components/maps/LocationPicker";
import { LocationData } from "@/lib/google-maps";
import { Database } from "@/integrations/supabase/types";

type CarType = Database["public"]["Enums"]["car_type"];

const CreateCarListing = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();

  const [formData, setFormData] = useState({
    title: "",
    description: "",
    car_type: "" as CarType,
    make: "",
    model: "",
    year: new Date().getFullYear(),
    location: "",
    price_day: 0,
    price_week: 0,
    price_month: 0,
    seats: 5,
    transmission: "automatic",
    fuel_type: "petrol",
    features: [],
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState<LocationData | null>(null);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: Number(value),
    }));
  };

  const handleFeaturesChange = (features: string[]) => {
    setFormData((prev) => ({
      ...prev,
      features,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user) {
      toast({
        title: "Authentication required",
        description: "Please sign in to create a car listing",
        variant: "destructive",
      });
      navigate("/auth");
      return;
    }

    // Validation
    if (!formData.title) {
      toast({
        title: "Error",
        description: "Please enter a title",
        variant: "destructive",
      });
      return;
    }

    if (!formData.description) {
      toast({
        title: "Error",
        description: "Please enter a description",
        variant: "destructive",
      });
      return;
    }

    if (!formData.car_type) {
      toast({
        title: "Error",
        description: "Please select a car type",
        variant: "destructive",
      });
      return;
    }

    if (!formData.make || !formData.model) {
      toast({
        title: "Error",
        description: "Please enter make and model",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsSubmitting(true);

      const { error } = await supabase.from("cars").insert({
        ...formData,
        latitude: selectedLocation?.latitude || null,
        longitude: selectedLocation?.longitude || null,
        formatted_address: selectedLocation?.formatted_address || null,
        owner_id: user.id,
      });

      if (error) throw error;

      toast({
        title: "Success",
        description: "Your car has been listed successfully",
      });

      navigate("/cars");
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to create car listing",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-white">
      <Navbar />

      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-2">List Your Car</h1>
        <p className="text-muted-foreground mb-8">
          Share your car with travelers and earn extra income
        </p>

        <Card className="max-w-3xl mx-auto">
          <CardHeader>
            <CardTitle>Car Details</CardTitle>
            <CardDescription>
              Provide information about your car to attract renters
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-4">
                <div className="grid gap-2">
                  <Label htmlFor="title">Title</Label>
                  <Input
                    id="title"
                    name="title"
                    placeholder="E.g., Luxury Mercedes SUV for Rent"
                    value={formData.title}
                    onChange={handleChange}
                    required
                  />
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    name="description"
                    placeholder="Describe your car, its condition, special features..."
                    value={formData.description}
                    onChange={handleChange}
                    rows={5}
                    required
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="make">Make</Label>
                    <Input
                      id="make"
                      name="make"
                      placeholder="E.g., Toyota"
                      value={formData.make}
                      onChange={handleChange}
                      required
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="model">Model</Label>
                    <Input
                      id="model"
                      name="model"
                      placeholder="E.g., Corolla"
                      value={formData.model}
                      onChange={handleChange}
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="year">Year</Label>
                    <Input
                      id="year"
                      name="year"
                      type="number"
                      value={formData.year}
                      onChange={handleNumberChange}
                      required
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="car_type">Car Type</Label>
                    <Select
                      value={formData.car_type}
                      onValueChange={(value) =>
                        setFormData((prev) => ({
                          ...prev,
                          car_type: value as CarType,
                        }))
                      }
                    >
                      <SelectTrigger id="car_type">
                        <SelectValue placeholder="Select car type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="sedan">Sedan</SelectItem>
                        <SelectItem value="suv">SUV</SelectItem>
                        <SelectItem value="luxury">Luxury</SelectItem>
                        <SelectItem value="compact">Compact</SelectItem>
                        <SelectItem value="convertible">Convertible</SelectItem>
                        <SelectItem value="van">Van</SelectItem>
                        <SelectItem value="truck">Truck</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <Separator />

                <div className="grid gap-2">
                  <Label htmlFor="location">Location</Label>
                  <Input
                    id="location"
                    name="location"
                    placeholder="E.g., Banjul, Serrekunda"
                    value={formData.location}
                    onChange={handleChange}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label>Car Location on Map</Label>
                  <LocationPicker
                    onLocationSelect={(location) => {
                      setSelectedLocation(location);
                      // Also update the text field with the formatted address
                      setFormData((prev) => ({
                        ...prev,
                        location: location.formatted_address || location.latitude + ', ' + location.longitude,
                      }));
                    }}
                    initialLocation={selectedLocation || undefined}
                    height="400px"
                    showSearch={true}
                    showCurrentLocationButton={true}
                  />
                  {selectedLocation && (
                    <p className="text-sm text-gray-600">
                      Selected: {selectedLocation.formatted_address}
                    </p>
                  )}
                </div>

                <div className="grid grid-cols-3 gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="price_day">Price per Day ($)</Label>
                    <Input
                      id="price_day"
                      name="price_day"
                      type="number"
                      min="0"
                      value={formData.price_day}
                      onChange={handleNumberChange}
                      required
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="price_week">Price per Week ($)</Label>
                    <Input
                      id="price_week"
                      name="price_week"
                      type="number"
                      min="0"
                      value={formData.price_week}
                      onChange={handleNumberChange}
                      required
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="price_month">Price per Month ($)</Label>
                    <Input
                      id="price_month"
                      name="price_month"
                      type="number"
                      min="0"
                      value={formData.price_month}
                      onChange={handleNumberChange}
                      required
                    />
                  </div>
                </div>

                <Separator />

                <div className="grid grid-cols-3 gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="seats">Number of Seats</Label>
                    <Input
                      id="seats"
                      name="seats"
                      type="number"
                      min="1"
                      max="15"
                      value={formData.seats}
                      onChange={handleNumberChange}
                      required
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="transmission">Transmission</Label>
                    <Select
                      value={formData.transmission}
                      onValueChange={(value) =>
                        setFormData((prev) => ({
                          ...prev,
                          transmission: value,
                        }))
                      }
                    >
                      <SelectTrigger id="transmission">
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="automatic">Automatic</SelectItem>
                        <SelectItem value="manual">Manual</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="fuel_type">Fuel Type</Label>
                    <Select
                      value={formData.fuel_type}
                      onValueChange={(value) =>
                        setFormData((prev) => ({ ...prev, fuel_type: value }))
                      }
                    >
                      <SelectTrigger id="fuel_type">
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="petrol">Petrol</SelectItem>
                        <SelectItem value="diesel">Diesel</SelectItem>
                        <SelectItem value="electric">Electric</SelectItem>
                        <SelectItem value="hybrid">Hybrid</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

              </div>

              <Separator />

              <FeatureSelector
                type="car"
                selectedFeatures={formData.features}
                onFeaturesChange={handleFeaturesChange}
              />

              <Button type="submit" className="w-full bg-accent hover:bg-accent/90" disabled={isSubmitting}>
                {isSubmitting ? "Creating Listing..." : "Create Car Listing"}
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>

      <Footer />
    </div>
  );
};

export default CreateCarListing;
