
-- Function to get a host's payout requests with payment method details
CREATE OR REPLACE FUNCTION public.get_host_payout_requests(host_id uuid)
RETURNS TABLE (
  id uuid,
  host_id uuid,
  amount numeric,
  status text,
  notes text,
  admin_notes text,
  created_at timestamptz,
  updated_at timestamptz,
  payment_method_id uuid,
  processed_at timestamptz,
  processed_by uuid,
  payment_method jsonb
)
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT 
    pr.id,
    pr.host_id,
    pr.amount,
    pr.status,
    pr.notes,
    pr.admin_notes,
    pr.created_at,
    pr.updated_at,
    pr.payment_method_id,
    pr.processed_at,
    pr.processed_by,
    jsonb_build_object(
      'provider', hpm.provider,
      'account_id', hpm.account_id,
      'status', hpm.status
    ) as payment_method
  FROM 
    payout_requests pr
  LEFT JOIN 
    host_payment_methods hpm ON pr.payment_method_id = hpm.id
  WHERE 
    pr.host_id = get_host_payout_requests.host_id
  ORDER BY
    pr.created_at DESC;
$$;
