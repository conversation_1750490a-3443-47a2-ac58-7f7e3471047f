
import { AspectRatio } from "@/components/ui/aspect-ratio";

interface PropertyGalleryProps {
  images: string[];
  title: string;
}

const PropertyGallery = ({ images, title }: PropertyGalleryProps) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
      {images.length > 0 ? (
        <>
          <div className="aspect-[4/3] rounded-lg overflow-hidden">
            <img 
              src={images[0]} 
              alt={title}
              className="w-full h-full object-cover"
            />
          </div>
          <div className="grid grid-cols-2 gap-4">
            {images.slice(1, 5).map((image, index) => (
              <div key={index} className="aspect-square rounded-lg overflow-hidden">
                <img 
                  src={image} 
                  alt={`${title} - ${index + 1}`}
                  className="w-full h-full object-cover"
                />
              </div>
            ))}
          </div>
        </>
      ) : (
        <div className="aspect-[4/3] rounded-lg overflow-hidden bg-gray-200 flex items-center justify-center col-span-2">
          <img 
            src="/placeholder.svg" 
            alt="No image available"
            className="w-32 h-32 opacity-50"
          />
        </div>
      )}
    </div>
  );
};

export default PropertyGallery;
