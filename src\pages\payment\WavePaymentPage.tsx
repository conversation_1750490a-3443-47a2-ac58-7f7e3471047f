import { useEffect, useState } from "react";
import { useSearchParams, useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Wallet, Smartphone, QrCode, CheckCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";

const WavePaymentPage = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [paymentStatus, setPaymentStatus] = useState<'pending' | 'completed' | 'failed'>('pending');
  const [checkingPayment, setCheckingPayment] = useState(false);

  const checkoutId = searchParams.get('checkout_id');
  const bookingId = searchParams.get('booking_id');
  const amount = searchParams.get('amount');

  useEffect(() => {
    if (!checkoutId || !bookingId || !amount) {
      toast({
        title: "Invalid payment link",
        description: "Missing payment information. Please try again.",
        variant: "destructive",
      });
      navigate('/');
    }
  }, [checkoutId, bookingId, amount, navigate, toast]);

  const checkPaymentStatus = async () => {
    if (!bookingId) return;

    setCheckingPayment(true);
    try {
      const { data: booking, error } = await supabase
        .from('bookings')
        .select('payment_status, status')
        .eq('id', bookingId)
        .single();

      if (error) throw error;

      if (booking.payment_status === 'completed' && booking.status === 'confirmed') {
        setPaymentStatus('completed');
        toast({
          title: "Payment successful!",
          description: "Your booking has been confirmed.",
        });
        setTimeout(() => {
          navigate(`/payment-success?booking_id=${bookingId}&type=property`);
        }, 2000);
      } else if (booking.payment_status === 'failed') {
        setPaymentStatus('failed');
      }
    } catch (error) {
      console.error('Error checking payment status:', error);
    } finally {
      setCheckingPayment(false);
    }
  };

  const handleTryAgain = () => {
    navigate(-1); // Go back to the booking page
  };

  const handleOpenWaveApp = () => {
    // Try to open Wave app with the deep link
    const waveUrl = `wave://capture/https://pay.wave.com/c/${checkoutId}?a=${amount}&c=GMD&m=GESCO%20Gambia`;
    
    // Create a temporary link and click it
    const link = document.createElement('a');
    link.href = waveUrl;
    link.click();
    
    // Show instructions after attempting to open the app
    toast({
      title: "Opening Wave app...",
      description: "If the Wave app doesn't open, please install it from your app store.",
    });
  };

  if (paymentStatus === 'completed') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
            <CardTitle className="text-green-600">Payment Successful!</CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-gray-600 mb-4">
              Your payment has been processed successfully. You will be redirected shortly.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <Wallet className="h-16 w-16 text-blue-500 mx-auto mb-4" />
          <CardTitle>Complete Payment with Wave</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="text-center">
            <p className="text-lg font-semibold mb-2">Amount: {amount} GMD</p>
            <p className="text-sm text-gray-600">Booking ID: {bookingId}</p>
          </div>

          <div className="space-y-4">
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="font-semibold mb-2 flex items-center">
                <Smartphone className="h-5 w-5 mr-2" />
                Option 1: Use Wave Mobile App
              </h3>
              <p className="text-sm text-gray-600 mb-3">
                Open the Wave app on your phone to complete the payment.
              </p>
              <Button 
                onClick={handleOpenWaveApp}
                className="w-full"
                variant="default"
              >
                Open Wave App
              </Button>
            </div>

            <div className="bg-green-50 p-4 rounded-lg">
              <h3 className="font-semibold mb-2 flex items-center">
                <QrCode className="h-5 w-5 mr-2" />
                Option 2: Manual Payment
              </h3>
              <p className="text-sm text-gray-600 mb-3">
                Send {amount} GMD to Wave merchant account and use reference: {checkoutId}
              </p>
              <div className="text-xs text-gray-500 bg-white p-2 rounded border">
                Reference: {checkoutId}
              </div>
            </div>
          </div>

          <div className="space-y-3">
            <Button 
              onClick={checkPaymentStatus}
              disabled={checkingPayment}
              className="w-full"
              variant="outline"
            >
              {checkingPayment ? "Checking..." : "Check Payment Status"}
            </Button>
            
            <Button 
              onClick={handleTryAgain}
              className="w-full"
              variant="ghost"
            >
              Try Different Payment Method
            </Button>
          </div>

          <div className="text-xs text-gray-500 text-center">
            <p>Having trouble? Contact support for assistance.</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default WavePaymentPage;
