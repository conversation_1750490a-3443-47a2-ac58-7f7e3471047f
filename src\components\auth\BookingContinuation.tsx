import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/components/layout/AuthProvider';
import { getBookingIntent, clearBookingIntent } from '@/utils/bookingContinuation';
import { useToast } from '@/hooks/use-toast';

export const BookingContinuation = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();

  useEffect(() => {
    // Only run if user is authenticated
    if (!user) return;

    const bookingIntent = getBookingIntent();
    if (!bookingIntent) return;

    // Clear the intent first to prevent loops
    clearBookingIntent();

    // Show success message
    toast({
      title: "Authentication successful!",
      description: "Continuing with your booking...",
    });

    // Navigate back to the booking page
    if (bookingIntent.type === 'car') {
      navigate(`/cars/${bookingIntent.id}`);
    } else if (bookingIntent.type === 'property') {
      navigate(`/listings/${bookingIntent.id}`);
    } else if (bookingIntent.returnUrl) {
      navigate(bookingIntent.returnUrl);
    } else {
      // Fallback to home page
      navigate('/');
    }
  }, [user, navigate, toast]);

  return null; // This component doesn't render anything
};
