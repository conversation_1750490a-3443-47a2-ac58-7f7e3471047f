import { createContext, useContext, useEffect, useState } from "react";
import { Session, User } from "@supabase/supabase-js";
import { supabase } from "@/integrations/supabase/client";
import { useNavigate } from "react-router-dom";
import { Database } from "@/integrations/supabase/types";
import { toast } from "sonner";
import { BookingContinuation } from "../auth/BookingContinuation";

type UserRole = Database["public"]["Enums"]["user_role"];

interface AuthContextType {
  session: Session | null;
  user: User | null;
  signIn: (email: string, password: string) => Promise<void>;
  signInWithPhone: (phone: string, password: string) => Promise<void>;
  signUp: (
    email: string,
    password: string,
    firstName: string,
    lastName: string,
    phoneNumber?: string
  ) => Promise<void>;
  signUpWithPhone: (
    phone: string,
    password: string,
    firstName: string,
    lastName: string,
    email?: string
  ) => Promise<void>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  updateProfile: (data: {
    firstName?: string;
    lastName?: string;
    role?: UserRole;
    phoneNumber?: string;
    email?: string;
  }) => Promise<void>;
  getUserRole: () => Promise<UserRole | null>;
  getUserRoles: () => Promise<string[]>;
  hasRole: (role: string) => Promise<boolean>;
  isPhoneVerified: () => boolean;
  isEmailVerified: () => boolean;
  isVerified: () => boolean;
  loading: boolean;
  switchRole: (newRole: UserRole) => Promise<void>;
  becomeHost: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType>({
  session: null,
  user: null,
  signIn: async () => {},
  signInWithPhone: async () => {},
  signUp: async () => {},
  signUpWithPhone: async () => {},
  signOut: async () => {},
  resetPassword: async () => {},
  updateProfile: async () => {},
  getUserRole: async () => null,
  getUserRoles: async () => [],
  hasRole: async () => false,
  isPhoneVerified: () => false,
  isEmailVerified: () => false,
  isVerified: () => false,
  loading: true,
  switchRole: async () => {},
  becomeHost: async () => {},
});

export const useAuth = () => {
  return useContext(AuthContext);
};

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [userProfile, setUserProfile] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [cachedRole, setCachedRole] = useState<string | null>(null);
  const [cachedRoles, setCachedRoles] = useState<string[] | null>(null);
  const [rolesCacheTime, setRolesCacheTime] = useState<number>(0);
  const navigate = useNavigate();

  // Fetch user profile data
  const fetchUserProfile = async (userId: string) => {
    try {
      const { data: profile, error } = await supabase
        .from("profiles")
        .select("primary_login_method")
        .eq("id", userId)
        .single();

      if (!error && profile) {
        setUserProfile(profile);
      }
    } catch (error) {
      console.error("Error fetching user profile:", error);
    }
  };

  const ensureProfileExists = async (userId: string, currentUser?: User) => {
    try {
      // Add timeout to prevent hanging
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error("Profile check timeout")), 3000);
      });

      // Check if profile exists with timeout
      const profileCheckPromise = supabase
        .from("profiles")
        .select("id")
        .eq("id", userId)
        .single();

      const { data: existingProfile, error: checkError } = (await Promise.race([
        profileCheckPromise,
        timeoutPromise,
      ])) as any;

      // If there's an error but it's not "not found", skip profile creation
      if (checkError && !checkError.message?.includes("No rows returned")) {
        return;
      }

      if (!existingProfile) {
        const userToUse = currentUser || user;
        if (userToUse) {
          // Create profile from user metadata with timeout
          const profileCreatePromise = supabase.rpc("handle_new_user_manual", {
            user_id: userId,
            first_name: userToUse.user_metadata?.first_name || "",
            last_name: userToUse.user_metadata?.last_name || "",
            email: userToUse.email || userToUse.user_metadata?.email || "",
            phone_number:
              userToUse.phone || userToUse.user_metadata?.phone_number || "",
          });

          const { error: profileError } = (await Promise.race([
            profileCreatePromise,
            timeoutPromise,
          ])) as any;

          if (profileError) {
            console.error("Error creating missing profile:", profileError);
          }
        }
      }
    } catch (error) {
      // Don't throw errors, just silently skip to prevent blocking the UI
      return;
    }
  };

  useEffect(() => {
    let mounted = true;

    // Set up auth state listener
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((event, session) => {
      if (!mounted) return;

      setSession(session);
      setUser(session?.user ?? null);
      setLoading(false);

      // Clear role cache when user changes
      setCachedRole(null);
      setCachedRoles(null);
      setRolesCacheTime(0);

      // Fetch user profile and ensure profile exists for authenticated users (non-blocking)
      if (
        session?.user &&
        (event === "SIGNED_IN" || event === "TOKEN_REFRESHED")
      ) {
        // Fetch user profile data
        fetchUserProfile(session.user.id).catch((error) => {
          console.error("Failed to fetch user profile:", error);
        });

        // Run profile creation in background without blocking UI
        ensureProfileExists(session.user.id, session.user).catch((error) => {
          console.error("Background profile creation failed:", error);
        });
      } else {
        // Clear profile data when user logs out
        setUserProfile(null);
      }
    });

    // Check for existing session with error handling
    supabase.auth
      .getSession()
      .then(({ data: { session } }) => {
        if (!mounted) return;

        setSession(session);
        setUser(session?.user ?? null);
        setLoading(false);

        // Ensure profile exists for existing session (non-blocking)
        if (session?.user) {
          ensureProfileExists(session.user.id, session.user).catch((error) => {
            console.error("Background profile creation failed:", error);
          });
        }
      })
      .catch((error) => {
        console.error("Error getting session:", error);
        if (mounted) {
          setLoading(false); // Ensure loading is set to false even on error
        }
      });

    // Fallback timeout to ensure loading is never stuck
    const fallbackTimeout = setTimeout(() => {
      if (mounted) {
        setLoading(false);
      }
    }, 5000); // 5 second timeout

    return () => {
      mounted = false;
      subscription.unsubscribe();
      clearTimeout(fallbackTimeout);
    };
  }, []);

  const signIn = async (email: string, password: string) => {
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) {
      // Provide more specific error messages
      if (error.message.includes("Invalid login credentials")) {
        throw new Error(
          "Invalid email or password. Please check your credentials and try again."
        );
      } else if (error.message.includes("Email not confirmed")) {
        // For unverified users, provide a clear path to verification
        throw new Error("EMAIL_NOT_VERIFIED");
      } else if (error.message.includes("Too many requests")) {
        throw new Error(
          "Too many login attempts. Please wait a moment before trying again."
        );
      } else {
        throw new Error(error.message || "An error occurred during sign in.");
      }
    }

    // Check if user is verified after successful login
    if (
      data?.user &&
      !data.user.email_confirmed_at &&
      !data.user.phone_confirmed_at
    ) {
      // User logged in but not verified - redirect to verification
      throw new Error("EMAIL_NOT_VERIFIED");
    }

    // After sign in, check user role to redirect accordingly
    const userRole = await getUserRole();
    if (userRole === "host") {
      navigate("/host");
    } else if (userRole === "admin") {
      navigate("/admin/dashboard");
    } else {
      navigate("/");
    }
  };

  const signInWithPhone = async (phone: string, password: string) => {
    const { data, error } = await supabase.auth.signInWithPassword({
      phone,
      password,
    });

    if (error) {
      // Provide more specific error messages
      if (error.message.includes("Invalid login credentials")) {
        throw new Error(
          "Invalid phone number or password. Please check your credentials and try again."
        );
      } else if (error.message.includes("Phone not confirmed")) {
        // For unverified users, provide a clear path to verification
        throw new Error("PHONE_NOT_VERIFIED");
      } else if (error.message.includes("Too many requests")) {
        throw new Error(
          "Too many login attempts. Please wait a moment before trying again."
        );
      } else {
        throw new Error(error.message || "An error occurred during sign in.");
      }
    }

    // Check if user is verified after successful login
    if (
      data?.user &&
      !data.user.phone_confirmed_at &&
      !data.user.email_confirmed_at
    ) {
      // User logged in but not verified - redirect to verification
      throw new Error("PHONE_NOT_VERIFIED");
    }

    // After sign in, check user role to redirect accordingly
    const userRole = await getUserRole();
    if (userRole === "host") {
      navigate("/host");
    } else if (userRole === "admin") {
      navigate("/admin/dashboard");
    } else {
      navigate("/");
    }
  };

  const signUp = async (
    email: string,
    password: string,
    firstName: string,
    lastName: string,
    phoneNumber?: string
  ) => {
    // First check if email or phone already exists
    try {
      if (email) {
        const { data: emailExists } = await supabase.rpc("check_email_exists", {
          email_to_check: email,
        });
        if (emailExists) {
          throw new Error(
            "An account with this email address already exists. Please use a different email or try signing in."
          );
        }
      }

      if (phoneNumber) {
        const { data: phoneExists } = await supabase.rpc("check_phone_exists", {
          phone_to_check: phoneNumber,
        });
        if (phoneExists) {
          throw new Error(
            "An account with this phone number already exists. Please use a different phone number or try signing in."
          );
        }
      }
    } catch (validationError: any) {
      if (
        validationError.message.includes("email address already exists") ||
        validationError.message.includes("phone number already exists")
      ) {
        throw validationError;
      }
      // If it's not our validation error, continue with signup
    }

    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          first_name: firstName,
          last_name: lastName,
          phone_number: phoneNumber,
        },
      },
    });

    if (error) {
      // Provide more specific error messages
      if (error.message.includes("User already registered")) {
        throw new Error(
          "An account with this email already exists. Please sign in instead."
        );
      } else if (error.message.includes("Password should be at least")) {
        throw new Error("Password should be at least 6 characters long.");
      } else if (error.message.includes("Unable to validate email address")) {
        throw new Error("Please enter a valid email address.");
      } else if (error.message.includes("Signup is disabled")) {
        throw new Error(
          "Account registration is currently disabled. Please contact support."
        );
      } else {
        throw new Error(error.message || "An error occurred during sign up.");
      }
    }

    // Create profile manually since triggers don't work on auth.users
    if (data?.user) {
      try {
        const { error: profileError } = await supabase.rpc(
          "handle_new_user_manual",
          {
            user_id: data.user.id,
            first_name: firstName,
            last_name: lastName,
            email: email,
            phone_number: phoneNumber || "",
            primary_login_method: "email", // Email was chosen as primary
          }
        );

        if (profileError) {
          console.error("Error creating profile:", profileError);
          // Handle profile creation errors
          if (profileError.message?.includes("EMAIL_ALREADY_EXISTS")) {
            throw new Error(
              "An account with this email address already exists. Please try signing in instead."
            );
          }
          if (profileError.message?.includes("PHONE_ALREADY_EXISTS")) {
            throw new Error(
              "An account with this phone number already exists. Please try signing in instead."
            );
          }
          // Don't throw other profile errors as user is already created
        }
      } catch (profileError: any) {
        console.error("Failed to create user profile:", profileError);
        if (
          profileError.message?.includes("EMAIL_ALREADY_EXISTS") ||
          profileError.message?.includes("PHONE_ALREADY_EXISTS")
        ) {
          throw profileError;
        }
        // Continue with registration flow for other profile creation failures
      }

      // Use Supabase's built-in email confirmation via AWS SES SMTP
      // The user will receive an email with Supabase's generated token
      console.log(
        "Email signup completed, user will receive confirmation email via AWS SES SMTP"
      );
    }

    return data;
  };

  const signUpWithPhone = async (
    phone: string,
    password: string,
    firstName: string,
    lastName: string,
    email?: string
  ) => {
    // First check if email or phone already exists
    try {
      if (phone) {
        const { data: phoneExists } = await supabase.rpc("check_phone_exists", {
          phone_to_check: phone,
        });
        if (phoneExists) {
          throw new Error(
            "An account with this phone number already exists. Please use a different phone number or try signing in."
          );
        }
      }

      if (email) {
        const { data: emailExists } = await supabase.rpc("check_email_exists", {
          email_to_check: email,
        });
        if (emailExists) {
          throw new Error(
            "An account with this email address already exists. Please use a different email or try signing in."
          );
        }
      }
    } catch (validationError: any) {
      if (
        validationError.message.includes("email address already exists") ||
        validationError.message.includes("phone number already exists")
      ) {
        throw validationError;
      }
      // If it's not our validation error, continue with signup
    }

    const { data, error } = await supabase.auth.signUp({
      phone,
      password,
      options: {
        data: {
          first_name: firstName,
          last_name: lastName,
          phone_number: phone,
          email: email,
        },
      },
    });

    if (error) {
      console.error("Phone signup error:", error);

      // Provide more specific error messages
      if (error.message.includes("User already registered")) {
        throw new Error(
          "An account with this phone number already exists. Please sign in instead."
        );
      } else if (error.message.includes("Password should be at least")) {
        throw new Error("Password should be at least 6 characters long.");
      } else if (error.message.includes("Unable to validate phone")) {
        throw new Error("Please enter a valid phone number.");
      } else if (error.message.includes("Signup is disabled")) {
        throw new Error(
          "Account registration is currently disabled. Please contact support."
        );
      } else if (error.message.includes("Error sending confirmation OTP")) {
        throw new Error(
          "Unable to send verification code to your phone number. Please check your number and try again."
        );
      } else if (error.message.includes("Authenticate More Information")) {
        throw new Error(
          "Unable to send verification code. Please check your phone number and try again."
        );
      } else if (error.message.includes("twilio.com/docs/errors")) {
        throw new Error(
          "Unable to send verification code to your phone number. Please verify your number is correct and try again."
        );
      } else {
        // For any other errors, provide a generic user-friendly message
        throw new Error(
          "Unable to create account with this phone number. Please check your number and try again."
        );
      }
    }

    // Create profile manually since triggers don't work on auth.users
    if (data?.user) {
      try {
        const { error: profileError } = await supabase.rpc(
          "handle_new_user_manual",
          {
            user_id: data.user.id,
            first_name: firstName,
            last_name: lastName,
            email: email || "",
            phone_number: phone,
            primary_login_method: "phone", // Phone was chosen as primary
          }
        );

        if (profileError) {
          console.error("Error creating profile:", profileError);
          // Handle profile creation errors
          if (profileError.message?.includes("EMAIL_ALREADY_EXISTS")) {
            throw new Error(
              "An account with this email address already exists. Please try signing in instead."
            );
          }
          if (profileError.message?.includes("PHONE_ALREADY_EXISTS")) {
            throw new Error(
              "An account with this phone number already exists. Please try signing in instead."
            );
          }
          // Don't throw other profile errors as user is already created
        }
      } catch (profileError: any) {
        console.error("Failed to create user profile:", profileError);
        if (
          profileError.message?.includes("EMAIL_ALREADY_EXISTS") ||
          profileError.message?.includes("PHONE_ALREADY_EXISTS")
        ) {
          throw profileError;
        }
        // Continue with registration flow for other profile creation failures
      }
    }

    // Return the data so we can check if user needs verification
    return data;
  };

  const signOut = async () => {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
    navigate("/");
  };

  const resetPassword = async (email: string) => {
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/auth/reset-password`,
    });
    if (error) throw error;
  };

  const updateProfile = async (data: {
    firstName?: string;
    lastName?: string;
    role?: UserRole;
    phoneNumber?: string;
    email?: string;
  }) => {
    if (!user) throw new Error("No user logged in");

    try {
      const updates = {
        id: user.id,
        first_name: data.firstName,
        last_name: data.lastName,
        role: data.role,
        phone_number: data.phoneNumber,
        email: data.email,
        updated_at: new Date().toISOString(),
      };

      // Filter out undefined values
      Object.keys(updates).forEach((key) => {
        if (updates[key as keyof typeof updates] === undefined) {
          delete updates[key as keyof typeof updates];
        }
      });

      // Use the session for authentication
      const { error } = await supabase
        .from("profiles")
        .upsert(updates)
        .select();

      if (error) throw error;
    } catch (error) {
      console.error("Error updating profile:", error);
      throw error;
    }
  };

  const getUserRole = async (): Promise<UserRole | null> => {
    if (!user) return null;

    // Check cache first (cache for 30 seconds)
    const now = Date.now();
    if (cachedRole && cachedRoles && now - rolesCacheTime < 30000) {
      return cachedRole as UserRole;
    }

    try {
      // Add timeout to prevent hanging (increased to 5 seconds)
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error("getUserRole timeout")), 5000);
      });

      const rolePromise = supabase
        .from("profiles")
        .select("role, roles")
        .eq("id", user.id)
        .single();

      const { data, error } = await Promise.race([rolePromise, timeoutPromise]);

      if (error) {
        if (error.message?.includes("No rows returned")) {
          console.warn("Profile not found for user, returning default role");
          return "guest"; // Default role if profile doesn't exist
        }
        throw error;
      }

      // Cache the results
      const role = data.role || "guest";
      const roles = data.roles || ["guest"];
      setCachedRole(role);
      setCachedRoles(roles);
      setRolesCacheTime(now);

      return role;
    } catch (error) {
      // Only log timeout errors as warnings, not errors
      if (error instanceof Error && error.message.includes("timeout")) {
        console.warn(
          "getUserRole timeout - using default role:",
          error.message
        );
      } else {
        console.error("Error fetching user role:", error);
      }
      return "guest"; // Return default role on error
    }
  };

  const getUserRoles = async (): Promise<string[]> => {
    if (!user) return ["guest"];

    // Check cache first (cache for 30 seconds)
    const now = Date.now();
    if (cachedRole && cachedRoles && now - rolesCacheTime < 30000) {
      return cachedRoles;
    }

    try {
      // Add timeout to prevent hanging (increased to 5 seconds)
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error("getUserRoles timeout")), 5000);
      });

      const rolesPromise = supabase
        .from("profiles")
        .select("role, roles")
        .eq("id", user.id)
        .single();

      const { data, error } = await Promise.race([
        rolesPromise,
        timeoutPromise,
      ]);

      if (error) {
        if (error.message?.includes("No rows returned")) {
          console.warn("Profile not found for user, returning default roles");
          return ["guest"]; // Default roles if profile doesn't exist
        }
        throw error;
      }

      // Cache the results
      const role = data.role || "guest";
      const roles = data?.roles || ["guest"];
      setCachedRole(role);
      setCachedRoles(roles);
      setRolesCacheTime(now);

      return roles;
    } catch (error) {
      // Only log timeout errors as warnings, not errors
      if (error instanceof Error && error.message.includes("timeout")) {
        console.warn(
          "getUserRoles timeout - using default roles:",
          error.message
        );
      } else {
        console.error("Error fetching user roles:", error);
      }
      return ["guest"];
    }
  };

  const hasRole = async (role: string): Promise<boolean> => {
    const roles = await getUserRoles();
    return roles.includes(role);
  };

  const switchRole = async (newRole: UserRole) => {
    if (!user) throw new Error("No user logged in");

    try {
      // Update the role
      const { error } = await supabase
        .from("profiles")
        .update({
          role: newRole,
          updated_at: new Date().toISOString(),
        })
        .eq("id", user.id);

      if (error) {
        console.error("Error switching role:", error);
        throw error;
      }

      // Add a small delay to ensure database is updated before navigation
      setTimeout(() => {
        // Redirect based on the new role
        if (newRole === "host") {
          navigate("/host");
        } else if (newRole === "guest") {
          navigate("/");
        }
      }, 500);
    } catch (error) {
      console.error("Error switching role:", error);
      throw error;
    }
  };

  const becomeHost = async () => {
    if (!user) throw new Error("No user logged in");

    try {
      // Get current roles
      const currentRoles = await getUserRoles();

      // Add host role if not already present
      if (!currentRoles.includes("host")) {
        const newRoles = [...currentRoles, "host"];

        const { error } = await supabase
          .from("profiles")
          .update({
            role: "host", // Keep the primary role as host for compatibility
            roles: newRoles,
            updated_at: new Date().toISOString(),
          })
          .eq("id", user.id);

        if (error) {
          console.error("Error becoming host:", error);
          throw error;
        }

        // Clear role cache after successful update
        setCachedRole(null);
        setCachedRoles(null);
        setRolesCacheTime(0);

        toast.success("You are now a host!");

        // Verify the role update was successful before navigating
        let retryCount = 0;
        const maxRetries = 5;

        const checkAndNavigate = async () => {
          const updatedRoles = await getUserRoles();
          if (updatedRoles.includes("host") || retryCount >= maxRetries) {
            navigate("/host");
          } else {
            retryCount++;
            setTimeout(checkAndNavigate, 500);
          }
        };

        setTimeout(checkAndNavigate, 500);
      } else {
        // User is already a host, navigate immediately
        navigate("/host");
        toast.success("Switched to Host mode");
      }
    } catch (error) {
      console.error("Error becoming host:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      toast.error(`Failed to become host: ${errorMessage}`);
      throw error;
    }
  };

  const isPhoneVerified = (): boolean => {
    if (!user) return false;
    return !!user.phone_confirmed_at;
  };

  const isEmailVerified = (): boolean => {
    if (!user) return false;
    return !!user.email_confirmed_at;
  };

  const isVerified = (): boolean => {
    if (!user) return false;

    // Check verification based on primary login method
    if (userProfile?.primary_login_method === "phone") {
      return !!user.phone_confirmed_at;
    } else if (userProfile?.primary_login_method === "email") {
      return !!user.email_confirmed_at;
    }

    // Fallback: if no primary method set, check if either is verified
    return !!(user.phone_confirmed_at || user.email_confirmed_at);
  };

  return (
    <AuthContext.Provider
      value={{
        session,
        user,
        signIn,
        signInWithPhone,
        signUp,
        signUpWithPhone,
        signOut,
        resetPassword,
        updateProfile,
        getUserRole,
        getUserRoles,
        hasRole,
        isPhoneVerified,
        isEmailVerified,
        isVerified,
        loading,
        switchRole,
        becomeHost,
      }}
    >
      {children}
      <BookingContinuation />
    </AuthContext.Provider>
  );
};
