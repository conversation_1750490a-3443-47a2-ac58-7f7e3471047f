import { useEffect, useState } from "react";
import { useSearchParams, useNavigate, Link } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import Navbar from "@/components/layout/Navbar";
import {
  XCircle,
  ArrowLeft,
  RefreshCw,
  Home,
  Car,
  Building,
} from "lucide-react";
import { supabase } from "@/integrations/supabase/client";

const PaymentCancelled = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [bookingDetails, setBookingDetails] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  const bookingId = searchParams.get("booking_id");
  const isCarBooking =
    searchParams.get("car_booking") === "true" ||
    searchParams.get("type") === "car" ||
    window.location.pathname.includes("car-booking");

  const isHotelBooking =
    searchParams.get("hotel_booking") === "true" ||
    searchParams.get("type") === "hotel" ||
    window.location.pathname.includes("hotel-booking");

  // Debug logging
  console.log("PaymentCancelled params:", {
    bookingId,
    isCarBooking,
    isHotelBooking,
    carBookingParam: searchParams.get("car_booking"),
    hotelBookingParam: searchParams.get("hotel_booking"),
    typeParam: searchParams.get("type"),
    pathname: window.location.pathname,
    allParams: Object.fromEntries(searchParams.entries()),
  });

  useEffect(() => {
    const fetchBookingDetails = async () => {
      if (!bookingId) {
        setLoading(false);
        return;
      }

      try {
        if (isCarBooking) {
          console.log("Fetching car booking details for ID:", bookingId);
          const { data, error } = await supabase
            .from("car_bookings")
            .select(
              `
              *,
              cars(make, model, year, location),
              profiles(first_name, last_name)
            `
            )
            .eq("id", bookingId)
            .single();

          console.log("Car booking query result:", { data, error });
          if (error) {
            console.error("Car booking query error:", error);
          } else if (data) {
            setBookingDetails(data);
          }
        } else if (isHotelBooking) {
          console.log("Fetching hotel booking details for ID:", bookingId);
          const { data, error } = await supabase
            .from("hotel_bookings")
            .select(
              `
              *,
              hotels(name, location, address),
              profiles(first_name, last_name)
            `
            )
            .eq("id", bookingId)
            .single();

          console.log("Hotel booking query result:", { data, error });
          if (error) {
            console.error("Hotel booking query error:", error);
          } else if (data) {
            setBookingDetails(data);
          }
        } else {
          console.log("Fetching property booking details for ID:", bookingId);
          // First get the booking with properties data
          const { data: booking, error: bookingError } = await supabase
            .from("bookings")
            .select(
              `
              *,
              properties(title, location, address)
            `
            )
            .eq("id", bookingId)
            .single();

          console.log("Property booking query result:", {
            data: booking,
            error: bookingError,
          });
          if (bookingError) {
            console.error("Property booking query error:", bookingError);
          } else if (booking) {
            // Then get the user profile separately
            const { data: profile, error: profileError } = await supabase
              .from("profiles")
              .select("first_name, last_name")
              .eq("id", booking.user_id)
              .single();

            if (profileError) {
              console.warn("Could not fetch user profile:", profileError);
            }

            setBookingDetails({
              ...booking,
              profiles: profile,
            });
          }
        }
      } catch (error) {
        console.error("Error fetching booking details:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchBookingDetails();
  }, [bookingId, isCarBooking]);

  const handleRetryPayment = () => {
    console.log("Retry payment clicked:", {
      isCarBooking,
      isHotelBooking,
      bookingDetails,
    });

    if (isCarBooking && bookingDetails?.car_id) {
      console.log("Navigating to car:", bookingDetails.car_id);
      navigate(`/cars/${bookingDetails.car_id}`);
    } else if (isHotelBooking && bookingDetails?.hotel_id) {
      console.log("Navigating to hotel:", bookingDetails.hotel_id);
      navigate(`/hotels/${bookingDetails.hotel_id}`);
    } else if (
      !isCarBooking &&
      !isHotelBooking &&
      bookingDetails?.property_id
    ) {
      console.log("Navigating to property:", bookingDetails.property_id);
      navigate(`/listings/${bookingDetails.property_id}`);
    } else {
      // Fallback: if we don't have booking details, redirect based on booking type
      console.log(
        "Fallback navigation, isCarBooking:",
        isCarBooking,
        "isHotelBooking:",
        isHotelBooking
      );
      if (isCarBooking) {
        navigate("/cars");
      } else if (isHotelBooking) {
        navigate("/hotels");
      } else {
        navigate("/listings");
      }
    }
  };

  const handleBrowse = () => {
    console.log(
      "Browse clicked, isCarBooking:",
      isCarBooking,
      "isHotelBooking:",
      isHotelBooking
    );
    if (isCarBooking) {
      navigate("/cars");
    } else if (isHotelBooking) {
      navigate("/hotels");
    } else {
      navigate("/listings");
    }
  };

  const getBookingTitle = () => {
    if (isCarBooking && bookingDetails?.cars) {
      return `${bookingDetails.cars.make} ${bookingDetails.cars.model} ${bookingDetails.cars.year}`;
    } else if (isHotelBooking && bookingDetails?.hotels) {
      return bookingDetails.hotels.name;
    } else if (bookingDetails?.properties) {
      return bookingDetails.properties.title;
    }
    return isCarBooking
      ? "Car Rental"
      : isHotelBooking
      ? "Hotel Booking"
      : "Property Booking";
  };

  const getBookingIcon = () => {
    if (isCarBooking) {
      return <Car className="h-6 w-6" />;
    } else if (isHotelBooking) {
      return <Building className="h-6 w-6" />;
    } else {
      return <Building className="h-6 w-6" />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />

      <div className="container mx-auto px-4 py-12">
        <div className="max-w-2xl mx-auto">
          <Card className="shadow-lg">
            <CardContent className="p-8">
              {/* Header */}
              <div className="text-center mb-8">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-red-100 rounded-full mb-4">
                  <XCircle className="h-8 w-8 text-red-600" />
                </div>
                <h1 className="text-2xl font-bold text-gray-900 mb-2">
                  Payment Cancelled
                </h1>
                <p className="text-gray-600">
                  Your payment was cancelled and no charges were made to your
                  account.
                </p>
              </div>

              {/* Booking Details */}
              {!loading && bookingDetails && bookingId && (
                <div className="bg-gray-50 rounded-lg p-6 mb-6">
                  <div className="flex items-center mb-4">
                    <div className="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-lg mr-3">
                      {getBookingIcon()}
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">
                        {getBookingTitle()}
                      </h3>
                      <p className="text-sm text-gray-600">
                        Booking ID: {bookingId}
                      </p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    {isCarBooking ? (
                      <>
                        <div>
                          <span className="font-medium text-gray-700">
                            Pickup Date:
                          </span>
                          <p className="text-gray-600">
                            {new Date(
                              bookingDetails.start_date
                            ).toLocaleDateString()}
                          </p>
                        </div>
                        <div>
                          <span className="font-medium text-gray-700">
                            Return Date:
                          </span>
                          <p className="text-gray-600">
                            {new Date(
                              bookingDetails.end_date
                            ).toLocaleDateString()}
                          </p>
                        </div>
                        <div>
                          <span className="font-medium text-gray-700">
                            Location:
                          </span>
                          <p className="text-gray-600">
                            {bookingDetails.cars?.location || "TBD"}
                          </p>
                        </div>
                      </>
                    ) : isHotelBooking ? (
                      <>
                        <div>
                          <span className="font-medium text-gray-700">
                            Check-in:
                          </span>
                          <p className="text-gray-600">
                            {new Date(
                              bookingDetails.check_in_date
                            ).toLocaleDateString()}
                          </p>
                        </div>
                        <div>
                          <span className="font-medium text-gray-700">
                            Check-out:
                          </span>
                          <p className="text-gray-600">
                            {new Date(
                              bookingDetails.check_out_date
                            ).toLocaleDateString()}
                          </p>
                        </div>
                        <div>
                          <span className="font-medium text-gray-700">
                            Guests:
                          </span>
                          <p className="text-gray-600">
                            {bookingDetails.guests}
                          </p>
                        </div>
                        <div>
                          <span className="font-medium text-gray-700">
                            Location:
                          </span>
                          <p className="text-gray-600">
                            {bookingDetails.hotels?.location || "TBD"}
                          </p>
                        </div>
                      </>
                    ) : (
                      <>
                        <div>
                          <span className="font-medium text-gray-700">
                            Check-in:
                          </span>
                          <p className="text-gray-600">
                            {new Date(
                              bookingDetails.check_in
                            ).toLocaleDateString()}
                          </p>
                        </div>
                        <div>
                          <span className="font-medium text-gray-700">
                            Check-out:
                          </span>
                          <p className="text-gray-600">
                            {new Date(
                              bookingDetails.check_out
                            ).toLocaleDateString()}
                          </p>
                        </div>
                        <div>
                          <span className="font-medium text-gray-700">
                            Guests:
                          </span>
                          <p className="text-gray-600">
                            {bookingDetails.guests}
                          </p>
                        </div>
                      </>
                    )}
                    <div>
                      <span className="font-medium text-gray-700">
                        Total Amount:
                      </span>
                      <p className="text-gray-600 font-semibold">
                        ${bookingDetails.total_price}
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Show message when no booking details */}
              {!loading && !bookingDetails && bookingId && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                  <p className="text-yellow-800 text-sm">
                    <strong>Booking ID:</strong> {bookingId}
                  </p>
                  <p className="text-yellow-700 text-sm mt-1">
                    We couldn't load the booking details, but your payment was
                    still cancelled successfully.
                  </p>
                </div>
              )}

              {/* Show message when no booking ID */}
              {!loading && !bookingId && (
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6">
                  <p className="text-gray-700 text-sm">
                    Your payment was cancelled successfully. No charges were
                    made to your account.
                  </p>
                </div>
              )}

              {/* What happens next */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                <h4 className="font-semibold text-blue-900 mb-2">
                  What happens next?
                </h4>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• Your booking has not been confirmed</li>
                  <li>• No payment has been processed</li>
                  <li>• You can try booking again at any time</li>
                  <li>
                    • The{" "}
                    {isCarBooking
                      ? "car"
                      : isHotelBooking
                      ? "hotel"
                      : "property"}{" "}
                    is still available for booking
                  </li>
                </ul>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-3">
                <Button
                  onClick={handleRetryPayment}
                  className="flex-1 bg-blue-600 hover:bg-blue-700 text-white"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Try Booking Again
                </Button>

                <Button
                  variant="outline"
                  onClick={handleBrowse}
                  className="flex-1"
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Browse{" "}
                  {isCarBooking
                    ? "Cars"
                    : isHotelBooking
                    ? "Hotels"
                    : "Properties"}
                </Button>

                <Button
                  variant="outline"
                  onClick={() => navigate("/")}
                  className="flex-1"
                >
                  <Home className="h-4 w-4 mr-2" />
                  Go Home
                </Button>
              </div>

              {/* Support */}
              <div className="text-center mt-6 pt-6 border-t border-gray-200">
                <p className="text-sm text-gray-600">
                  Need help? Contact our support team at{" "}
                  <a
                    href="mailto:<EMAIL>"
                    className="text-blue-600 hover:text-blue-700 font-medium"
                  >
                    <EMAIL>
                  </a>
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default PaymentCancelled;
