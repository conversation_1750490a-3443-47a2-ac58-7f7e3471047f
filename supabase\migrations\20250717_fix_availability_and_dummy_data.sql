-- Fix availability checking and add dummy data support
-- This migration addresses several issues:
-- 1. Availability should only consider confirmed bookings, not pending ones
-- 2. Add dummy data flagging for test listings
-- 3. Mark some existing listings as dummy data for testing

-- Update car availability function to only consider confirmed bookings
CREATE OR REPLACE FUNCTION check_car_availability(
  car_id UUID,
  start_date DATE,
  end_date DATE
)
RETURNS BOOLEAN AS $$
declare
  overlap_count integer;
begin
  select count(*) into overlap_count
  from car_bookings cb
  where cb.car_id = check_car_availability.car_id
    and cb.status = 'confirmed'
    and (
      (cb.start_date, cb.end_date) overlaps (check_car_availability.start_date, check_car_availability.end_date)
    );
  return overlap_count = 0;
end;
$$ LANGUAGE plpgsql;

-- Update property availability function to only consider confirmed bookings
CREATE OR REPLACE FUNCTION check_property_availability(
  property_id UUID,
  check_in_date DATE,
  check_out_date DATE
)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN NOT EXISTS (
    SELECT 1 FROM bookings
    WHERE bookings.property_id = check_property_availability.property_id
    AND status = 'confirmed'
    AND (
      (check_in_date, check_out_date) OVERLAPS (check_in, check_out)
    )
  );
END;
$$ LANGUAGE plpgsql;

-- Update room availability function to only consider confirmed bookings
CREATE OR REPLACE FUNCTION check_room_availability(
  p_room_type_id UUID,
  p_check_in DATE,
  p_check_out DATE,
  p_rooms_needed INTEGER DEFAULT 1
)
RETURNS INTEGER AS $$
DECLARE
  total_rooms INTEGER;
  booked_rooms INTEGER;
  available_rooms INTEGER;
BEGIN
  -- Get total rooms for this room type
  SELECT COUNT(*) INTO total_rooms
  FROM public.room_inventory ri
  WHERE ri.room_type_id = p_room_type_id 
    AND ri.status = 'available';
  
  -- Get booked rooms for the date range - only confirmed bookings
  SELECT COALESCE(SUM(hb.rooms_count), 0) INTO booked_rooms
  FROM public.hotel_bookings hb
  WHERE hb.room_type_id = p_room_type_id
    AND hb.status = 'confirmed'
    AND (
      (hb.check_in <= p_check_in AND hb.check_out > p_check_in) OR
      (hb.check_in < p_check_out AND hb.check_out >= p_check_out) OR
      (hb.check_in >= p_check_in AND hb.check_out <= p_check_out)
    );
  
  available_rooms := total_rooms - booked_rooms;
  
  RETURN GREATEST(0, available_rooms);
END;
$$ LANGUAGE plpgsql;

-- Add is_dummy column to all listing tables (if not already exists)
ALTER TABLE public.properties 
ADD COLUMN IF NOT EXISTS is_dummy BOOLEAN DEFAULT false;

ALTER TABLE public.cars 
ADD COLUMN IF NOT EXISTS is_dummy BOOLEAN DEFAULT false;

ALTER TABLE public.hotels 
ADD COLUMN IF NOT EXISTS is_dummy BOOLEAN DEFAULT false;

-- Add indexes for better query performance when filtering dummy data
CREATE INDEX IF NOT EXISTS idx_properties_is_dummy ON public.properties(is_dummy);
CREATE INDEX IF NOT EXISTS idx_cars_is_dummy ON public.cars(is_dummy);
CREATE INDEX IF NOT EXISTS idx_hotels_is_dummy ON public.hotels(is_dummy);

-- Add comments to document the purpose
COMMENT ON COLUMN public.properties.is_dummy IS 'Flag to indicate if this is test/demo data';
COMMENT ON COLUMN public.cars.is_dummy IS 'Flag to indicate if this is test/demo data';
COMMENT ON COLUMN public.hotels.is_dummy IS 'Flag to indicate if this is test/demo data';

-- Mark some existing listings as dummy data for testing
-- This helps distinguish between real listings and test data
UPDATE public.properties SET is_dummy = true 
WHERE id IN (SELECT id FROM public.properties ORDER BY created_at LIMIT 3);

UPDATE public.cars SET is_dummy = true 
WHERE id IN (SELECT id FROM public.cars ORDER BY created_at LIMIT 2);

UPDATE public.hotels SET is_dummy = true 
WHERE id IN (SELECT id FROM public.hotels ORDER BY created_at LIMIT 2);
