
-- <PERSON>reate function to get user preferences
CREATE OR REPLACE FUNCTION public.get_user_preferences(user_id_input UUID)
RETURNS TABLE (
  id UUID,
  user_id UUID,
  booking_confirmations BOOLEAN,
  status_updates BO<PERSON><PERSON><PERSON>,
  reminders BOOLEAN,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    p.id,
    p.user_id,
    p.booking_confirmations,
    p.status_updates,
    p.reminders,
    p.created_at,
    p.updated_at
  FROM user_preferences p
  WHERE p.user_id = user_id_input;
  
  -- If no record exists, return default values
  IF NOT FOUND THEN
    RETURN QUERY
    SELECT 
      gen_random_uuid()::UUID,
      user_id_input,
      true,
      true,
      true,
      now(),
      now();
  END IF;
END;
$$;

-- <PERSON>reate function to update user preferences
CREATE OR REPLACE FUNCTION public.update_user_preferences(
  user_id_input UUID,
  booking_confirmations_input BOOLEAN,
  status_updates_input BOOLEAN,
  reminders_input BOOLEAN
)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Insert or update the preferences
  INSERT INTO public.user_preferences (
    user_id,
    booking_confirmations,
    status_updates,
    reminders
  ) VALUES (
    user_id_input,
    booking_confirmations_input,
    status_updates_input,
    reminders_input
  )
  ON CONFLICT (user_id) DO UPDATE SET
    booking_confirmations = booking_confirmations_input,
    status_updates = status_updates_input,
    reminders = reminders_input,
    updated_at = now();
END;
$$;

-- Add RLS policies for the database functions
DO $$
BEGIN
  -- Grant execute permission to authenticated users
  EXECUTE 'GRANT EXECUTE ON FUNCTION public.get_user_preferences(UUID) TO authenticated';
  EXECUTE 'GRANT EXECUTE ON FUNCTION public.update_user_preferences(UUID, BOOLEAN, BOOLEAN, BOOLEAN) TO authenticated';
END
$$;
