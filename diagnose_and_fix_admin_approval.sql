-- Comprehensive Diagnosis and Fix for Admin Approval Issue
-- Run this entire script in Supabase SQL Editor

-- ============================================================================
-- STEP 1: DIAGNOSTIC CHECKS
-- ============================================================================

-- Check 1: Verify admin user exists and has correct role
SELECT '=== ADMIN USER CHECK ===' as step;
SELECT id, email, role, roles FROM profiles WHERE email = '<EMAIL>';

-- Check 2: Test is_admin function
SELECT '=== IS_ADMIN FUNCTION TEST ===' as step;
SELECT email, role, is_admin(id) as admin_check FROM profiles WHERE email = '<EMAIL>';

-- Check 3: Check current RLS policies
SELECT '=== CURRENT RLS POLICIES ===' as step;
SELECT tablename, policyname, cmd, permissive FROM pg_policies 
WHERE tablename IN ('properties', 'cars', 'hotels') 
ORDER BY tablename, policyname;

-- Check 4: Sample data from each table
SELECT '=== SAMPLE PROPERTIES ===' as step;
SELECT id, title, status, owner_id, updated_at FROM properties LIMIT 3;

SELECT '=== SAMPLE CARS ===' as step;
SELECT id, title, status, owner_id, updated_at FROM cars LIMIT 3;

SELECT '=== SAMPLE HOTELS ===' as step;
SELECT id, title, status, owner_id, updated_at FROM hotels LIMIT 3;

-- ============================================================================
-- STEP 2: FIX THE ISSUES
-- ============================================================================

-- Fix 1: Ensure admin user has correct role
UPDATE profiles 
SET role = 'admin', roles = ARRAY['admin', 'host']
WHERE email = '<EMAIL>';

-- Fix 2: Recreate is_admin function to ensure it works
CREATE OR REPLACE FUNCTION is_admin(uid UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = uid AND role = 'admin'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Fix 3: Drop ALL existing policies and recreate them properly
-- Properties policies
DROP POLICY IF EXISTS "Anyone can view properties" ON public.properties;
DROP POLICY IF EXISTS "Users can create their own properties" ON public.properties;
DROP POLICY IF EXISTS "Users can update their own properties" ON public.properties;
DROP POLICY IF EXISTS "Users can delete their own properties" ON public.properties;
DROP POLICY IF EXISTS "Admins can view all properties" ON public.properties;
DROP POLICY IF EXISTS "Admins can update all properties" ON public.properties;
DROP POLICY IF EXISTS "admin_can_update_properties" ON public.properties;
DROP POLICY IF EXISTS "admin_can_view_properties" ON public.properties;
DROP POLICY IF EXISTS "owner_can_update_properties" ON public.properties;

-- Cars policies
DROP POLICY IF EXISTS "Anyone can view cars" ON public.cars;
DROP POLICY IF EXISTS "Car owners can create their cars" ON public.cars;
DROP POLICY IF EXISTS "Car owners can update their cars" ON public.cars;
DROP POLICY IF EXISTS "Car owners can delete their cars" ON public.cars;
DROP POLICY IF EXISTS "Admins can view all cars" ON public.cars;
DROP POLICY IF EXISTS "Admins can update all cars" ON public.cars;
DROP POLICY IF EXISTS "admin_can_update_cars" ON public.cars;
DROP POLICY IF EXISTS "admin_can_view_cars" ON public.cars;
DROP POLICY IF EXISTS "owner_can_update_cars" ON public.cars;

-- Hotels policies
DROP POLICY IF EXISTS "Anyone can view hotels" ON public.hotels;
DROP POLICY IF EXISTS "Hotel owners can create their hotels" ON public.hotels;
DROP POLICY IF EXISTS "Hotel owners can update their hotels" ON public.hotels;
DROP POLICY IF EXISTS "Hotel owners can delete their hotels" ON public.hotels;
DROP POLICY IF EXISTS "Admins can view all hotels" ON public.hotels;
DROP POLICY IF EXISTS "Admins can update all hotels" ON public.hotels;
DROP POLICY IF EXISTS "admin_can_update_hotels" ON public.hotels;
DROP POLICY IF EXISTS "admin_can_view_hotels" ON public.hotels;
DROP POLICY IF EXISTS "owner_can_update_hotels" ON public.hotels;

-- Fix 4: Create new simplified policies that definitely work
-- Properties
CREATE POLICY "properties_select_all" ON public.properties FOR SELECT USING (true);
CREATE POLICY "properties_insert_owner" ON public.properties FOR INSERT WITH CHECK (auth.uid() = owner_id);
CREATE POLICY "properties_update_admin_or_owner" ON public.properties 
  FOR UPDATE USING (
    (SELECT role FROM profiles WHERE id = auth.uid()) = 'admin' OR 
    auth.uid() = owner_id
  );
CREATE POLICY "properties_delete_owner" ON public.properties FOR DELETE USING (auth.uid() = owner_id);

-- Cars
CREATE POLICY "cars_select_all" ON public.cars FOR SELECT USING (true);
CREATE POLICY "cars_insert_owner" ON public.cars FOR INSERT WITH CHECK (auth.uid() = owner_id);
CREATE POLICY "cars_update_admin_or_owner" ON public.cars 
  FOR UPDATE USING (
    (SELECT role FROM profiles WHERE id = auth.uid()) = 'admin' OR 
    auth.uid() = owner_id
  );
CREATE POLICY "cars_delete_owner" ON public.cars FOR DELETE USING (auth.uid() = owner_id);

-- Hotels
CREATE POLICY "hotels_select_all" ON public.hotels FOR SELECT USING (true);
CREATE POLICY "hotels_insert_owner" ON public.hotels FOR INSERT WITH CHECK (auth.uid() = owner_id);
CREATE POLICY "hotels_update_admin_or_owner" ON public.hotels 
  FOR UPDATE USING (
    (SELECT role FROM profiles WHERE id = auth.uid()) = 'admin' OR 
    auth.uid() = owner_id
  );
CREATE POLICY "hotels_delete_owner" ON public.hotels FOR DELETE USING (auth.uid() = owner_id);

-- Fix 5: Grant explicit permissions
GRANT ALL ON public.properties TO authenticated;
GRANT ALL ON public.cars TO authenticated;
GRANT ALL ON public.hotels TO authenticated;

-- ============================================================================
-- STEP 3: TEST THE FIX
-- ============================================================================

-- Test 1: Verify policies are created
SELECT '=== NEW POLICIES CREATED ===' as step;
SELECT tablename, policyname, cmd FROM pg_policies 
WHERE tablename IN ('properties', 'cars', 'hotels') 
ORDER BY tablename, policyname;

-- Test 2: Test admin function again
SELECT '=== ADMIN FUNCTION RETEST ===' as step;
SELECT email, role, is_admin(id) as admin_check FROM profiles WHERE email = '<EMAIL>';

-- Test 3: Try to update a property as admin (replace with actual property ID)
DO $$
DECLARE
    admin_id UUID;
    test_property_id UUID;
    original_status TEXT;
    new_status TEXT;
BEGIN
    -- Get admin user ID
    SELECT id INTO admin_id FROM profiles WHERE email = '<EMAIL>';
    
    -- Get a test property
    SELECT id, status INTO test_property_id, original_status FROM properties LIMIT 1;
    
    IF test_property_id IS NOT NULL THEN
        -- Set new status
        new_status := CASE WHEN original_status = 'pending' THEN 'approved' ELSE 'pending' END;
        
        -- Perform the update
        UPDATE properties 
        SET status = new_status, updated_at = NOW()
        WHERE id = test_property_id;
        
        RAISE NOTICE 'Test update: Property % changed from % to %', test_property_id, original_status, new_status;
        
        -- Verify the update
        SELECT status INTO new_status FROM properties WHERE id = test_property_id;
        RAISE NOTICE 'Verification: Property % now has status %', test_property_id, new_status;
    ELSE
        RAISE NOTICE 'No properties found for testing';
    END IF;
END $$;

-- Final verification
SELECT '=== FINAL VERIFICATION ===' as step;
SELECT 'Properties with approved status' as check_type, COUNT(*) as count FROM properties WHERE status = 'approved';
SELECT 'Cars with approved status' as check_type, COUNT(*) as count FROM cars WHERE status = 'approved';
SELECT 'Hotels with approved status' as check_type, COUNT(*) as count FROM hotels WHERE status = 'approved';
