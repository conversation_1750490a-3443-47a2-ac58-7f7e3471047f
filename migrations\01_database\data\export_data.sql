-- Gesco Stay Data Export Script
-- This script exports all data from the current Supabase project
-- Run this script against the current project to export data

-- Export profiles
\copy (SELECT * FROM public.profiles ORDER BY created_at) TO 'profiles.csv' WITH CSV HEADER;

-- Export properties
\copy (SELECT * FROM public.properties ORDER BY created_at) TO 'properties.csv' WITH CSV HEADER;

-- Export cars
\copy (SELECT * FROM public.cars ORDER BY created_at) TO 'cars.csv' WITH CSV HEADER;

-- Export hotels
\copy (SELECT * FROM public.hotels ORDER BY created_at) TO 'hotels.csv' WITH CSV HEADER;

-- Export room types
\copy (SELECT * FROM public.room_types ORDER BY created_at) TO 'room_types.csv' WITH CSV HEADER;

-- Export room inventory
\copy (SELECT * FROM public.room_inventory ORDER BY created_at) TO 'room_inventory.csv' WITH CSV HEADER;

-- Export seasonal pricing
\copy (SELECT * FROM public.seasonal_pricing ORDER BY created_at) TO 'seasonal_pricing.csv' WITH CSV HEADER;

-- Export bookings
\copy (SELECT * FROM public.bookings ORDER BY created_at) TO 'bookings.csv' WITH CSV HEADER;

-- Export car bookings
\copy (SELECT * FROM public.car_bookings ORDER BY created_at) TO 'car_bookings.csv' WITH CSV HEADER;

-- Export hotel bookings
\copy (SELECT * FROM public.hotel_bookings ORDER BY created_at) TO 'hotel_bookings.csv' WITH CSV HEADER;

-- Export room assignments
\copy (SELECT * FROM public.room_assignments ORDER BY assigned_at) TO 'room_assignments.csv' WITH CSV HEADER;

-- Export reviews
\copy (SELECT * FROM public.reviews ORDER BY created_at) TO 'reviews.csv' WITH CSV HEADER;

-- Export car insurance options
\copy (SELECT * FROM public.car_insurance_options ORDER BY created_at) TO 'car_insurance_options.csv' WITH CSV HEADER;

-- Export conversations
\copy (SELECT * FROM public.conversations ORDER BY created_at) TO 'conversations.csv' WITH CSV HEADER;

-- Export messages
\copy (SELECT * FROM public.messages ORDER BY created_at) TO 'messages.csv' WITH CSV HEADER;

-- Export host payment methods
\copy (SELECT * FROM public.host_payment_methods ORDER BY created_at) TO 'host_payment_methods.csv' WITH CSV HEADER;

-- Export payout requests
\copy (SELECT * FROM public.payout_requests ORDER BY created_at) TO 'payout_requests.csv' WITH CSV HEADER;

-- Export platform earnings
\copy (SELECT * FROM public.platform_earnings ORDER BY created_at) TO 'platform_earnings.csv' WITH CSV HEADER;

-- Export payment logs
\copy (SELECT * FROM public.payment_logs ORDER BY created_at) TO 'payment_logs.csv' WITH CSV HEADER;

-- Export admin users (be careful with sensitive data)
\copy (SELECT id, email, first_name, last_name, role, is_active, created_by, created_at, updated_at, last_login_at FROM public.admin_users ORDER BY created_at) TO 'admin_users.csv' WITH CSV HEADER;

-- Export admin sessions (current sessions only)
\copy (SELECT id, admin_user_id, expires_at, ip_address, user_agent, created_at FROM public.admin_sessions WHERE expires_at > now() ORDER BY created_at) TO 'admin_sessions.csv' WITH CSV HEADER;

-- Export checkout reminders sent
\copy (SELECT * FROM public.checkout_reminders_sent ORDER BY sent_at) TO 'checkout_reminders_sent.csv' WITH CSV HEADER;

-- Alternative: Export as SQL INSERT statements
-- Uncomment the following section if you prefer SQL INSERT statements

/*
-- Generate INSERT statements for all tables
SELECT 'INSERT INTO public.profiles VALUES ' || string_agg('(' || quote_literal(id) || ',' || 
  COALESCE(quote_literal(first_name), 'NULL') || ',' || 
  COALESCE(quote_literal(last_name), 'NULL') || ',' || 
  COALESCE(quote_literal(avatar_url), 'NULL') || ',' || 
  quote_literal(role) || ',' || 
  COALESCE(quote_literal(phone_number), 'NULL') || ',' || 
  COALESCE(quote_literal(email), 'NULL') || ',' || 
  COALESCE(quote_literal(roles::text), 'NULL') || ',' || 
  quote_literal(created_at) || ',' || 
  quote_literal(updated_at) || ')', ',') || ';'
FROM public.profiles;

-- Add similar statements for other tables as needed
*/

-- Data validation queries
-- Run these to verify data integrity before migration

SELECT 'profiles' as table_name, COUNT(*) as record_count FROM public.profiles
UNION ALL
SELECT 'properties', COUNT(*) FROM public.properties
UNION ALL
SELECT 'cars', COUNT(*) FROM public.cars
UNION ALL
SELECT 'hotels', COUNT(*) FROM public.hotels
UNION ALL
SELECT 'room_types', COUNT(*) FROM public.room_types
UNION ALL
SELECT 'room_inventory', COUNT(*) FROM public.room_inventory
UNION ALL
SELECT 'seasonal_pricing', COUNT(*) FROM public.seasonal_pricing
UNION ALL
SELECT 'bookings', COUNT(*) FROM public.bookings
UNION ALL
SELECT 'car_bookings', COUNT(*) FROM public.car_bookings
UNION ALL
SELECT 'hotel_bookings', COUNT(*) FROM public.hotel_bookings
UNION ALL
SELECT 'room_assignments', COUNT(*) FROM public.room_assignments
UNION ALL
SELECT 'reviews', COUNT(*) FROM public.reviews
UNION ALL
SELECT 'car_insurance_options', COUNT(*) FROM public.car_insurance_options
UNION ALL
SELECT 'conversations', COUNT(*) FROM public.conversations
UNION ALL
SELECT 'messages', COUNT(*) FROM public.messages
UNION ALL
SELECT 'host_payment_methods', COUNT(*) FROM public.host_payment_methods
UNION ALL
SELECT 'payout_requests', COUNT(*) FROM public.payout_requests
UNION ALL
SELECT 'platform_earnings', COUNT(*) FROM public.platform_earnings
UNION ALL
SELECT 'payment_logs', COUNT(*) FROM public.payment_logs
UNION ALL
SELECT 'admin_users', COUNT(*) FROM public.admin_users
UNION ALL
SELECT 'admin_sessions', COUNT(*) FROM public.admin_sessions
UNION ALL
SELECT 'checkout_reminders_sent', COUNT(*) FROM public.checkout_reminders_sent
ORDER BY table_name;

-- Check for orphaned records
SELECT 'Orphaned bookings' as issue, COUNT(*) as count
FROM public.bookings b
LEFT JOIN public.properties p ON b.property_id = p.id
WHERE b.property_id IS NOT NULL AND p.id IS NULL

UNION ALL

SELECT 'Orphaned car bookings', COUNT(*)
FROM public.car_bookings cb
LEFT JOIN public.cars c ON cb.car_id = c.id
WHERE c.id IS NULL

UNION ALL

SELECT 'Orphaned hotel bookings', COUNT(*)
FROM public.hotel_bookings hb
LEFT JOIN public.hotels h ON hb.hotel_id = h.id
WHERE h.id IS NULL

UNION ALL

SELECT 'Orphaned reviews', COUNT(*)
FROM public.reviews r
LEFT JOIN public.properties p ON r.property_id = p.id
LEFT JOIN public.cars c ON r.car_id = c.id
LEFT JOIN public.hotels h ON r.hotel_id = h.id
WHERE p.id IS NULL AND c.id IS NULL AND h.id IS NULL;

-- Export summary
SELECT 
  'Data export completed at: ' || now()::text as summary,
  'Total tables exported: 23' as details;
