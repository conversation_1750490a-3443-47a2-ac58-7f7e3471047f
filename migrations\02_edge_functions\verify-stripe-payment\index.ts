import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import Stripe from "https://esm.sh/stripe@14.21.0";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.45.0";

const SUPABASE_URL = Deno.env.get("SUPABASE_URL") || "";
const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") || "";
const STRIPE_SECRET_KEY = Deno.env.get("STRIPE_SECRET_KEY") || "";

const stripe = new Stripe(STRIPE_SECRET_KEY, {
  apiVersion: "2023-10-16",
});

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { sessionId, bookingId, carBookingId } = await req.json();
    const isCarBooking = !!carBookingId;

    // Create Supabase client with admin privileges
    const supabaseAdmin = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY, { 
      auth: { persistSession: false } 
    });

    // Retrieve session from Stripe
    const session = await stripe.checkout.sessions.retrieve(sessionId);

    // Check if payment is successful
    const paymentStatus = session.payment_status === "paid" ? "completed" : "pending";

    // Update booking status based on type
    if (isCarBooking) {
      await supabaseAdmin.from("car_bookings").update({
        status: paymentStatus === "completed" ? "confirmed" : "pending"
      }).eq("id", carBookingId);
    } else {
      await supabaseAdmin.from("bookings").update({
        payment_status: paymentStatus,
        payment_id: session.payment_intent as string,
        status: paymentStatus === "completed" ? "confirmed" : "pending"
      }).eq("id", bookingId);
    }

    // Update payment log
    await supabaseAdmin.from("payment_logs").update({
      status: paymentStatus,
      provider_response: session,
    }).eq("transaction_id", sessionId);

    // If payment was successful, record platform fee
    if (paymentStatus === "completed") {
      try {
        // Get booking amount
        let bookingAmount = 0;
        let bookingType = "property";
        
        if (isCarBooking) {
          const { data: carBooking } = await supabaseAdmin
            .from("car_bookings")
            .select("total_price")
            .eq("id", carBookingId)
            .single();
            
          if (carBooking) {
            bookingAmount = carBooking.total_price;
            bookingType = "car";
          }
        } else {
          const { data: propertyBooking } = await supabaseAdmin
            .from("bookings")
            .select("total_price")
            .eq("id", bookingId)
            .single();
            
          if (propertyBooking) {
            bookingAmount = propertyBooking.total_price;
          }
        }
        
        // Calculate platform fee (15%)
        const platformFee = bookingAmount * 0.15;
        const hostPayout = bookingAmount * 0.85;
        
        // Record platform earnings
        await supabaseAdmin.from("platform_earnings").insert({
          booking_id: isCarBooking ? null : bookingId,
          car_booking_id: isCarBooking ? carBookingId : null,
          booking_type: bookingType,
          total_booking_amount: bookingAmount,
          platform_fee: platformFee,
          host_payout_amount: hostPayout
        });
      
        // Call email notification function
        try {
          const emailResponse = await fetch(`${SUPABASE_URL}/functions/v1/send-booking-confirmation`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "Authorization": `Bearer ${SUPABASE_SERVICE_ROLE_KEY}`,
            },
            body: JSON.stringify({ bookingId: isCarBooking ? carBookingId : bookingId, type: isCarBooking ? "car" : "property" }),
          });
          
          const emailData = await emailResponse.json();
          console.log("Email notification sent:", emailData);
        } catch (emailError) {
          console.error("Error sending email notification:", emailError);
        }
      } catch (platformError) {
        console.error("Error recording platform fee:", platformError);
      }
    }

    return new Response(
      JSON.stringify({ 
        success: true, 
        paymentStatus,
        paymentId: session.payment_intent,
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200,
      }
    );
  } catch (error) {
    console.error("Error verifying Stripe payment:", error);
    return new Response(
      JSON.stringify({ success: false, error: error.message }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 500,
      }
    );
  }
});
