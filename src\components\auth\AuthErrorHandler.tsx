import { AlertCircle, Shield, Phone, Mail } from "lucide-react";
import { Button } from "@/components/ui/button";

interface AuthErrorHandlerProps {
  error: string;
  phoneNumber?: string;
  onRetry?: () => void;
  onGoToVerification?: () => void;
  onGoToSignup?: () => void;
}

const AuthErrorHandler = ({
  error,
  phoneNumber,
  onRetry,
  onGoToVerification,
  onGoToSignup,
}: AuthErrorHandlerProps) => {
  const getErrorContent = () => {
    switch (error) {
      case "PHONE_NOT_VERIFIED":
        return {
          icon: <Shield className="h-8 w-8 text-orange-500" />,
          title: "Account Not Verified",
          description:
            "Your phone number needs to be verified before you can sign in.",
          bgColor: "bg-orange-50",
          borderColor: "border-orange-200",
          textColor: "text-orange-800",
          actions: (
            <div className="space-y-2">
              {onGoToVerification && (
                <Button
                  onClick={onGoToVerification}
                  className="w-full bg-orange-500 hover:bg-orange-600 text-white"
                >
                  <Shield className="mr-2 h-4 w-4" />
                  Verify Phone Number
                </Button>
              )}
              {onRetry && (
                <Button onClick={onRetry} variant="outline" className="w-full">
                  Try Again
                </Button>
              )}
            </div>
          ),
        };

      case "EMAIL_NOT_VERIFIED":
        return {
          icon: <Shield className="h-8 w-8 text-orange-500" />,
          title: "Email Not Verified",
          description:
            "Your email address needs to be verified before you can sign in.",
          bgColor: "bg-orange-50",
          borderColor: "border-orange-200",
          textColor: "text-orange-800",
          actions: (
            <div className="space-y-2">
              {onGoToVerification && (
                <Button
                  onClick={onGoToVerification}
                  className="w-full bg-orange-500 hover:bg-orange-600 text-white"
                >
                  <Mail className="mr-2 h-4 w-4" />
                  Verify Email Address
                </Button>
              )}
              {onRetry && (
                <Button onClick={onRetry} variant="outline" className="w-full">
                  Try Again
                </Button>
              )}
            </div>
          ),
        };

      case "Invalid phone number or password. Please check your credentials and try again.":
        return {
          icon: <AlertCircle className="h-8 w-8 text-red-500" />,
          title: "Invalid Credentials",
          description: "The phone number or password you entered is incorrect.",
          bgColor: "bg-red-50",
          borderColor: "border-red-200",
          textColor: "text-red-800",
          actions: (
            <div className="space-y-2">
              {onRetry && (
                <Button
                  onClick={onRetry}
                  className="w-full bg-red-500 hover:bg-red-600 text-white"
                >
                  Try Again
                </Button>
              )}
              {onGoToSignup && (
                <Button
                  onClick={onGoToSignup}
                  variant="outline"
                  className="w-full"
                >
                  Create New Account
                </Button>
              )}
            </div>
          ),
        };

      case "EMAIL_ALREADY_EXISTS":
        return {
          icon: <AlertCircle className="h-8 w-8 text-blue-500" />,
          title: "Email Already Registered",
          description: "An account with this email address already exists.",
          bgColor: "bg-blue-50",
          borderColor: "border-blue-200",
          textColor: "text-blue-800",
          actions: (
            <div className="space-y-2">
              {onRetry && (
                <Button
                  onClick={onRetry}
                  className="w-full bg-blue-500 hover:bg-blue-600 text-white"
                >
                  Try Different Email
                </Button>
              )}
              <Button
                onClick={() => (window.location.href = "/auth?mode=login")}
                variant="outline"
                className="w-full"
              >
                Sign In Instead
              </Button>
            </div>
          ),
        };

      case "PHONE_ALREADY_EXISTS":
        return {
          icon: <AlertCircle className="h-8 w-8 text-blue-500" />,
          title: "Phone Number Already Registered",
          description: "An account with this phone number already exists.",
          bgColor: "bg-blue-50",
          borderColor: "border-blue-200",
          textColor: "text-blue-800",
          actions: (
            <div className="space-y-2">
              {onRetry && (
                <Button
                  onClick={onRetry}
                  className="w-full bg-blue-500 hover:bg-blue-600 text-white"
                >
                  Try Different Number
                </Button>
              )}
              <Button
                onClick={() => (window.location.href = "/auth?mode=login")}
                variant="outline"
                className="w-full"
              >
                Sign In Instead
              </Button>
            </div>
          ),
        };

      case "Too many login attempts. Please wait a moment before trying again.":
        return {
          icon: <AlertCircle className="h-8 w-8 text-yellow-500" />,
          title: "Too Many Attempts",
          description:
            "Please wait a few minutes before trying to sign in again.",
          bgColor: "bg-yellow-50",
          borderColor: "border-yellow-200",
          textColor: "text-yellow-800",
          actions: (
            <div className="text-center">
              <p className="text-sm text-yellow-700">
                This is a security measure to protect your account.
              </p>
            </div>
          ),
        };

      default:
        // Handle error messages that contain our specific error codes
        if (error.includes("email address already exists")) {
          return {
            icon: <AlertCircle className="h-8 w-8 text-blue-500" />,
            title: "Email Already Registered",
            description: "An account with this email address already exists.",
            bgColor: "bg-blue-50",
            borderColor: "border-blue-200",
            textColor: "text-blue-800",
            actions: (
              <div className="space-y-2">
                {onRetry && (
                  <Button
                    onClick={onRetry}
                    className="w-full bg-blue-500 hover:bg-blue-600 text-white"
                  >
                    Try Different Email
                  </Button>
                )}
                <Button
                  onClick={() => (window.location.href = "/auth?mode=login")}
                  variant="outline"
                  className="w-full"
                >
                  Sign In Instead
                </Button>
              </div>
            ),
          };
        }

        if (error.includes("phone number already exists")) {
          return {
            icon: <AlertCircle className="h-8 w-8 text-blue-500" />,
            title: "Phone Number Already Registered",
            description: "An account with this phone number already exists.",
            bgColor: "bg-blue-50",
            borderColor: "border-blue-200",
            textColor: "text-blue-800",
            actions: (
              <div className="space-y-2">
                {onRetry && (
                  <Button
                    onClick={onRetry}
                    className="w-full bg-blue-500 hover:bg-blue-600 text-white"
                  >
                    Try Different Number
                  </Button>
                )}
                <Button
                  onClick={() => (window.location.href = "/auth?mode=login")}
                  variant="outline"
                  className="w-full"
                >
                  Sign In Instead
                </Button>
              </div>
            ),
          };
        }

        return {
          icon: <AlertCircle className="h-8 w-8 text-gray-500" />,
          title: "Authentication Error",
          description: error,
          bgColor: "bg-gray-50",
          borderColor: "border-gray-200",
          textColor: "text-gray-800",
          actions: onRetry && (
            <Button onClick={onRetry} className="w-full">
              Try Again
            </Button>
          ),
        };
    }
  };

  const errorContent = getErrorContent();

  return (
    <div
      className={`${errorContent.bgColor} ${errorContent.borderColor} border rounded-lg p-4 mb-4`}
    >
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0">{errorContent.icon}</div>
        <div className="flex-1">
          <h3 className={`text-sm font-medium ${errorContent.textColor} mb-1`}>
            {errorContent.title}
          </h3>
          <p className={`text-sm ${errorContent.textColor} mb-3`}>
            {errorContent.description}
          </p>
          {phoneNumber && error === "PHONE_NOT_VERIFIED" && (
            <div className="flex items-center gap-2 mb-3">
              <Phone className="h-4 w-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">
                {phoneNumber}
              </span>
            </div>
          )}
          {errorContent.actions}
        </div>
      </div>
    </div>
  );
};

export default AuthErrorHandler;
