# Supabase Configuration for Gesco Stay
# Update project_id to match your new project

# A string used to distinguish different Supabase projects on the same host. Defaults to the working
# directory name when running `supabase init`.
project_id = "gesco-stay-new"  # UPDATE THIS TO YOUR NEW PROJECT ID

[api]
# Port to use for the API URL.
port = 54321
# Schemas to expose in your API. Tables, views and stored procedures in this schema will get API
# endpoints. public and storage are always included.
schemas = ["public", "storage", "graphql_public"]
# Extra schemas to add to the search_path of every request. public is always included.
extra_search_path = ["public", "extensions"]
# The maximum number of rows returns from a view, table, or stored procedure. Limits payload size
# for accidental or malicious requests.
max_rows = 1000

[db]
# Port to use for the local database URL.
port = 54322
# The database major version to use. This has to be the same as your remote database's. Run `SHOW
# server_version;` on the remote database to check.
major_version = 15

[studio]
# Port to use for Supabase Studio.
port = 54323

# Email testing server. Emails sent with the local dev setup are not actually sent - rather, they
# are monitored, and you can view the emails that would have been sent from the web interface.
[inbucket]
# Port to use for the email testing server web interface.
port = 54324
smtp_port = 54325
pop3_port = 54326

[storage]
# The maximum file size allowed (e.g. "5MB", "500KB").
file_size_limit = "50MiB"

[auth]
# The base URL of your website. Used as an allow-list for redirects and for constructing URLs used
# in emails.
site_url = "http://localhost:3000"  # UPDATE FOR PRODUCTION
# A list of *exact* URLs that auth providers are permitted to redirect to post authentication.
additional_redirect_urls = ["https://localhost:3000", "https://yourdomain.com"]  # UPDATE FOR PRODUCTION
# How long tokens are valid for, in seconds. Defaults to 3600 (1 hour), maximum 604,800 (1 week).
jwt_expiry = 3600
# Allow/disallow new user signups to your project.
enable_signup = true
# If enabled, a user will be required to confirm any email change on both the old, and new email
# addresses. If disabled, only the new email is required to confirm.
double_confirm_changes = true
# If enabled, users need to confirm their email address before signing in.
enable_confirmations = false

# Email templates configuration
[auth.email]
# Templates for your emails
enable_signup = true
enable_confirmations = true
template_create_user = "<h2>Welcome to GescosStay</h2><p>Thank you for signing up!</p>"
template_password_reset = "<h2>Reset Your Password</h2><p>Follow this link to reset your password:</p><p><a href=\"{{ .ConfirmationURL }}\">Reset Password</a></p>"
template_magic_link = "<h2>Magic Link Login</h2><p>Follow this link to login:</p><p><a href=\"{{ .ConfirmationURL }}\">Login</a></p>"
template_email_change = "<h2>Change Your Email</h2><p>Follow this link to confirm your email change:</p><p><a href=\"{{ .ConfirmationURL }}\">Confirm Email Change</a></p>"
template_verify_email = "<h2>Verify Your Email</h2><p>Follow this link to verify your email:</p><p><a href=\"{{ .ConfirmationURL }}\">Verify Email</a></p>"

# Configure Edge Functions 
[functions]
[functions.send-booking-confirmation]
verify_jwt = true
[functions.send-booking-reminders]
verify_jwt = false
[functions.verify-stripe-payment]
verify_jwt = true
[functions.process-stripe-payment]
verify_jwt = true
[functions.process-wave-payment]
verify_jwt = true
[functions.verify-wave-payment]
verify_jwt = true

[analytics]
enabled = false
port = 54327
vector_port = 54328

# Additional configuration for production
[experimental]
# Enable experimental features if needed
# webhooks = true
