-- =====================================================
-- SUPABASE VERIFICATION QUERIES
-- Run these queries to verify platform fee updates
-- =====================================================

-- 1. Check if the calculate_platform_fee function exists and what it returns
SELECT 
  'Testing platform fee calculation' as test_description,
  public.calculate_platform_fee(100.00) as platform_fee_result,
  'Should be 15.00' as expected_result;

-- 2. Check if the calculate_host_payout function exists
SELECT 
  'Testing host payout calculation' as test_description,
  public.calculate_host_payout(100.00) as host_payout_result,
  'Should be 85.00' as expected_result;

-- 3. Check existing platform_earnings records to see current fee structure
SELECT 
  COUNT(*) as total_records,
  AVG(platform_fee / total_booking_amount * 100) as avg_fee_percentage,
  MIN(platform_fee / total_booking_amount * 100) as min_fee_percentage,
  MAX(platform_fee / total_booking_amount * 100) as max_fee_percentage
FROM platform_earnings
WHERE total_booking_amount > 0;

-- 4. Check for any records that might still be using 10% fee
SELECT 
  id,
  total_booking_amount,
  platform_fee,
  host_payout_amount,
  ROUND(platform_fee / total_booking_amount * 100, 2) as fee_percentage,
  created_at
FROM platform_earnings
WHERE 
  total_booking_amount > 0 
  AND ABS(platform_fee / total_booking_amount - 0.10) < 0.001  -- Records with ~10% fee
ORDER BY created_at DESC
LIMIT 10;

-- 5. Check for records with correct 15% fee
SELECT 
  id,
  total_booking_amount,
  platform_fee,
  host_payout_amount,
  ROUND(platform_fee / total_booking_amount * 100, 2) as fee_percentage,
  created_at
FROM platform_earnings
WHERE 
  total_booking_amount > 0 
  AND ABS(platform_fee / total_booking_amount - 0.15) < 0.001  -- Records with ~15% fee
ORDER BY created_at DESC
LIMIT 10;

-- 6. List all custom functions related to platform fees
SELECT 
  routine_name,
  routine_type,
  routine_definition
FROM information_schema.routines 
WHERE 
  routine_schema = 'public' 
  AND routine_name LIKE '%platform%'
ORDER BY routine_name;

-- 7. Check if there are any settings tables that might store platform fee configuration
SELECT table_name 
FROM information_schema.tables 
WHERE 
  table_schema = 'public' 
  AND (
    table_name LIKE '%setting%' 
    OR table_name LIKE '%config%' 
    OR table_name LIKE '%platform%'
  )
ORDER BY table_name;

-- 8. Sample calculation test with different amounts
SELECT 
  amount,
  public.calculate_platform_fee(amount) as platform_fee,
  public.calculate_host_payout(amount) as host_payout,
  ROUND(public.calculate_platform_fee(amount) / amount * 100, 2) as fee_percentage
FROM (
  VALUES (50.00), (100.00), (250.00), (500.00), (1000.00)
) AS test_amounts(amount);

-- =====================================================
-- EXPECTED RESULTS:
-- - Platform fee function should return 15% of input amount
-- - Host payout function should return 85% of input amount  
-- - Fee percentage should be 15.00 for all test amounts
-- - Any existing records should ideally show 15% fee percentage
-- =====================================================
