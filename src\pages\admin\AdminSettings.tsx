import { useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { toast } from "sonner";

const AdminSettings = () => {
  const [loading, setLoading] = useState(false);
  const [emailSettings, setEmailSettings] = useState({
    enableBookingConfirmations: true,
    enablePaymentReceipts: true,
    enableReminderEmails: true,
    adminEmail: "<EMAIL>",
  });

  const [platformSettings, setPlatformSettings] = useState({
    autoApproveProperties: false,
    autoApproveCars: false,
    requireVerification: true,
    platformFee: "15",
  });

  const saveEmailSettings = async () => {
    setLoading(true);
    try {
      // In a real implementation, this would save to a settings table
      // For now we'll just simulate success
      await new Promise((resolve) => setTimeout(resolve, 500));
      toast.success("Email settings saved successfully");
    } catch (error: any) {
      toast.error("Failed to save email settings");
    } finally {
      setLoading(false);
    }
  };

  const savePlatformSettings = async () => {
    setLoading(true);
    try {
      // In a real implementation, this would save to a settings table
      // For now we'll just simulate success
      await new Promise((resolve) => setTimeout(resolve, 500));
      toast.success("Platform settings saved successfully");
    } catch (error: any) {
      toast.error("Failed to save platform settings");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-6">Admin Settings</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Email Notifications */}
        <Card>
          <CardHeader>
            <CardTitle>Email Notifications</CardTitle>
            <CardDescription>
              Configure the platform's email notification settings
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="booking-confirmations">
                  Booking Confirmations
                </Label>
                <p className="text-sm text-gray-500">
                  Send email confirmations for new bookings
                </p>
              </div>
              <Switch
                id="booking-confirmations"
                checked={emailSettings.enableBookingConfirmations}
                onCheckedChange={(checked) =>
                  setEmailSettings({
                    ...emailSettings,
                    enableBookingConfirmations: checked,
                  })
                }
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="payment-receipts">Payment Receipts</Label>
                <p className="text-sm text-gray-500">
                  Send receipts after successful payments
                </p>
              </div>
              <Switch
                id="payment-receipts"
                checked={emailSettings.enablePaymentReceipts}
                onCheckedChange={(checked) =>
                  setEmailSettings({
                    ...emailSettings,
                    enablePaymentReceipts: checked,
                  })
                }
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="reminder-emails">Reminder Emails</Label>
                <p className="text-sm text-gray-500">
                  Send reminders before check-in/rental dates
                </p>
              </div>
              <Switch
                id="reminder-emails"
                checked={emailSettings.enableReminderEmails}
                onCheckedChange={(checked) =>
                  setEmailSettings({
                    ...emailSettings,
                    enableReminderEmails: checked,
                  })
                }
              />
            </div>

            <div className="pt-2">
              <Label htmlFor="admin-email">Admin Notification Email</Label>
              <Input
                id="admin-email"
                type="email"
                value={emailSettings.adminEmail}
                onChange={(e) =>
                  setEmailSettings({
                    ...emailSettings,
                    adminEmail: e.target.value,
                  })
                }
                className="mt-1"
              />
              <p className="text-sm text-gray-500 mt-1">
                Admin notifications will be sent to this email
              </p>
            </div>
          </CardContent>
          <CardFooter>
            <Button
              onClick={saveEmailSettings}
              disabled={loading}
              className="ml-auto"
            >
              {loading ? "Saving..." : "Save Email Settings"}
            </Button>
          </CardFooter>
        </Card>

        {/* Platform Settings */}
        <Card>
          <CardHeader>
            <CardTitle>Platform Settings</CardTitle>
            <CardDescription>
              Configure general platform settings and behavior
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="auto-approve-properties">
                  Auto-Approve Properties
                </Label>
                <p className="text-sm text-gray-500">
                  Automatically approve new property listings
                </p>
              </div>
              <Switch
                id="auto-approve-properties"
                checked={platformSettings.autoApproveProperties}
                onCheckedChange={(checked) =>
                  setPlatformSettings({
                    ...platformSettings,
                    autoApproveProperties: checked,
                  })
                }
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="auto-approve-cars">Auto-Approve Cars</Label>
                <p className="text-sm text-gray-500">
                  Automatically approve new car listings
                </p>
              </div>
              <Switch
                id="auto-approve-cars"
                checked={platformSettings.autoApproveCars}
                onCheckedChange={(checked) =>
                  setPlatformSettings({
                    ...platformSettings,
                    autoApproveCars: checked,
                  })
                }
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="require-verification">
                  Require Verification
                </Label>
                <p className="text-sm text-gray-500">
                  Require email verification for new accounts
                </p>
              </div>
              <Switch
                id="require-verification"
                checked={platformSettings.requireVerification}
                onCheckedChange={(checked) =>
                  setPlatformSettings({
                    ...platformSettings,
                    requireVerification: checked,
                  })
                }
              />
            </div>

            <div className="pt-2">
              <Label htmlFor="platform-fee">Platform Fee (%)</Label>
              <Input
                id="platform-fee"
                type="number"
                min="0"
                max="100"
                value={platformSettings.platformFee}
                onChange={(e) =>
                  setPlatformSettings({
                    ...platformSettings,
                    platformFee: e.target.value,
                  })
                }
                className="mt-1"
              />
              <p className="text-sm text-gray-500 mt-1">
                Commission percentage on bookings
              </p>
            </div>
          </CardContent>
          <CardFooter>
            <Button
              onClick={savePlatformSettings}
              disabled={loading}
              className="ml-auto"
            >
              {loading ? "Saving..." : "Save Platform Settings"}
            </Button>
          </CardFooter>
        </Card>
      </div>

      {/* Database Management */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Database Management</CardTitle>
          <CardDescription>
            Perform database maintenance operations
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Button variant="outline">Generate Analytics Reports</Button>
            <Button variant="outline">Update Platform Statistics</Button>
            <Button variant="outline">Clean Outdated Records</Button>
            <Button variant="outline">Reset Demo Data</Button>
          </div>
        </CardContent>
        <CardFooter className="text-sm text-gray-500">
          <p>
            These operations may take several minutes to complete and can affect
            system performance.
          </p>
        </CardFooter>
      </Card>
    </div>
  );
};

export default AdminSettings;
