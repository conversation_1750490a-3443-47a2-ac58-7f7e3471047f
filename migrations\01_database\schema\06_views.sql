-- Gesco Stay Database Schema - Views
-- This file contains all database views for the Gesco Stay application

-- Admin auth view for simplified admin user access
CREATE OR REPLACE VIEW public.admin_auth_view AS
SELECT 
  au.id,
  au.email,
  au.first_name,
  au.last_name,
  au.role,
  au.is_active,
  au.created_at,
  au.last_login_at
FROM public.admin_users au;

-- Booking analytics view
CREATE OR REPLACE VIEW public.booking_analytics AS
SELECT 
  DATE_TRUNC('month', created_at) as month,
  COUNT(*) as total_bookings,
  SUM(total_price) as total_revenue,
  COUNT(*) FILTER (WHERE status = 'confirmed') as confirmed_bookings,
  COUNT(*) FILTER (WHERE status = 'cancelled') as cancelled_bookings
FROM public.bookings
GROUP BY DATE_TRUNC('month', created_at)
ORDER BY month DESC;

-- Car booking analytics view
CREATE OR REPLACE VIEW public.car_booking_analytics AS
SELECT 
  DATE_TRUNC('month', created_at) as month,
  COUNT(*) as total_bookings,
  SUM(total_price) as total_revenue,
  COUNT(*) FILTER (WHERE status = 'confirmed') as confirmed_bookings,
  COUNT(*) FILTER (WHERE status = 'cancelled') as cancelled_bookings
FROM public.car_bookings
GROUP BY DATE_TRUNC('month', created_at)
ORDER BY month DESC;

-- Platform fee summary view
CREATE OR REPLACE VIEW public.platform_fee_summary AS
SELECT 
  booking_type,
  COUNT(*) as total_bookings,
  SUM(total_booking_amount) as total_booking_value,
  SUM(platform_fee) as total_platform_fees,
  SUM(host_payout_amount) as total_host_payouts,
  AVG(platform_fee / total_booking_amount * 100) as avg_fee_percentage
FROM public.platform_earnings
GROUP BY booking_type;

-- Popular properties view
CREATE OR REPLACE VIEW public.popular_properties AS
SELECT 
  p.id,
  p.title,
  p.location,
  COUNT(b.id) as booking_count,
  AVG(r.rating) as avg_rating,
  SUM(b.total_price) as total_revenue
FROM public.properties p
LEFT JOIN public.bookings b ON p.id = b.property_id AND b.status = 'confirmed'
LEFT JOIN public.reviews r ON p.id = r.property_id
WHERE p.status = 'approved'
GROUP BY p.id, p.title, p.location
HAVING COUNT(b.id) > 0
ORDER BY booking_count DESC, avg_rating DESC;

-- Popular cars view
CREATE OR REPLACE VIEW public.popular_cars AS
SELECT 
  c.id,
  c.title,
  c.make,
  c.model,
  c.location,
  COUNT(cb.id) as booking_count,
  AVG(r.rating) as avg_rating,
  SUM(cb.total_price) as total_revenue
FROM public.cars c
LEFT JOIN public.car_bookings cb ON c.id = cb.car_id AND cb.status = 'confirmed'
LEFT JOIN public.reviews r ON c.id = r.car_id
WHERE c.status = 'approved'
GROUP BY c.id, c.title, c.make, c.model, c.location
HAVING COUNT(cb.id) > 0
ORDER BY booking_count DESC, avg_rating DESC;

-- Popular hotels view
CREATE OR REPLACE VIEW public.popular_hotels AS
SELECT
  h.*,
  AVG(r.rating) as avg_rating,
  COUNT(r.id) as review_count,
  COUNT(hb.id) as booking_count
FROM public.hotels h
LEFT JOIN public.reviews r ON h.id = r.hotel_id
LEFT JOIN public.hotel_bookings hb ON h.id = hb.hotel_id AND hb.status = 'confirmed'
WHERE h.status = 'approved'
GROUP BY h.id, h.title, h.description, h.location, h.formatted_address,
         h.latitude, h.longitude, h.owner_id, h.images, h.amenities,
         h.policies, h.check_in_time, h.check_out_time, h.status,
         h.is_dummy, h.created_at, h.updated_at
HAVING COUNT(hb.id) > 0
ORDER BY booking_count DESC, avg_rating DESC;

-- Comments for documentation
COMMENT ON VIEW public.admin_auth_view IS 'Simplified view for admin user authentication and display';
COMMENT ON VIEW public.booking_analytics IS 'Monthly booking statistics for properties';
COMMENT ON VIEW public.car_booking_analytics IS 'Monthly booking statistics for cars';
COMMENT ON VIEW public.platform_fee_summary IS 'Summary of platform fees by booking type';
COMMENT ON VIEW public.popular_properties IS 'Most popular properties based on bookings and ratings';
COMMENT ON VIEW public.popular_cars IS 'Most popular cars based on bookings and ratings';
COMMENT ON VIEW public.popular_hotels IS 'Most popular hotels based on bookings and ratings';
