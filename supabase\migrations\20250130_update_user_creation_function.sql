-- Update handle_new_user_manual function to include primary_login_method

-- Drop and recreate the function with the new parameter
DROP FUNCTION IF EXISTS public.handle_new_user_manual(UUID, TEXT, TEXT, TEXT, TEXT);

CREATE OR REPLACE FUNCTION public.handle_new_user_manual(
  user_id UUID,
  first_name TEXT DEFAULT '',
  last_name TEXT DEFAULT '',
  email TEXT DEFAULT '',
  phone_number TEXT DEFAULT '',
  primary_login_method TEXT DEFAULT 'email'
)
RETURNS UUID AS $$
BEGIN
  -- Validate primary_login_method
  IF primary_login_method NOT IN ('email', 'phone') THEN
    primary_login_method := 'email';
  END IF;

  INSERT INTO public.profiles (
    id,
    first_name,
    last_name,
    phone_number,
    email,
    primary_login_method
  )
  VALUES (
    user_id,
    COALESCE(first_name, ''),
    COALESCE(last_name, ''),
    COALESCE(phone_number, ''),
    COALESCE(email, ''),
    primary_login_method
  )
  ON CONFLICT (id) DO UPDATE SET
    first_name = COALESCE(EXCLUDED.first_name, profiles.first_name),
    last_name = COALESCE(EXCLUDED.last_name, profiles.last_name),
    phone_number = COALESCE(EXCLUDED.phone_number, profiles.phone_number),
    email = COALESCE(EXCLUDED.email, profiles.email),
    primary_login_method = COALESCE(EXCLUDED.primary_login_method, profiles.primary_login_method),
    updated_at = now();

  RETURN user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
