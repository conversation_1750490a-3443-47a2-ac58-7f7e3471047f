# Google Maps Integration Setup Guide

This guide explains how to set up and use the Google Maps integration in the Gesco Stay application.

## Prerequisites

1. **Google Cloud Platform Account**: You need a Google Cloud Platform account to access the Google Maps APIs.
2. **Google Maps API Key**: You need to create and configure a Google Maps API key.

## Step 1: Create Google Maps API Key

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the following APIs:
   - **Maps JavaScript API** (for displaying maps)
   - **Places API** (for location search and autocomplete)
   - **Geocoding API** (for converting addresses to coordinates)
   - **Advanced Markers API** (for modern marker functionality)

4. Create credentials:
   - Go to "Credentials" in the left sidebar
   - Click "Create Credentials" → "API Key"
   - Copy the generated API key

## Step 2: Configure API Key Restrictions (Recommended)

For security, restrict your API key:

1. In the Google Cloud Console, go to "Credentials"
2. Click on your API key to edit it
3. Under "Application restrictions":
   - Choose "HTTP referrers (web sites)"
   - Add your domain(s): `localhost:*`, `your-domain.com/*`
4. Under "API restrictions":
   - Choose "Restrict key"
   - Select: Maps JavaScript API, Places API, Geocoding API, Advanced Markers API

## Step 3: Environment Configuration

1. Open your `.env` file in the project root
2. Replace `YOUR_GOOGLE_MAPS_API_KEY_HERE` with your actual API key:

```env
VITE_GOOGLE_MAPS_API_KEY=your_actual_api_key_here
```

## Step 4: Test the Integration

1. Start the development server:
```bash
npm run dev
```

2. Test the following features:

### Property Creation
- Navigate to `/listings/create`
- Scroll down to the "Property Location on Map" section
- Try the following:
  - Search for a location using the search box
  - Click "Current" to get your current location
  - Click on the map to place a marker
  - Drag the marker to adjust the location

### Car Creation
- Navigate to `/cars/create`
- Scroll down to the "Car Location on Map" section
- Test the same features as above

### Location Display
- Create a property or car with a location
- View the detail page
- Verify that the map displays with:
  - Correct marker position
  - "Get Directions" button (opens Google Maps)
  - "View on Maps" button (opens Google Maps)

## Features Included

### LocationPicker Component
- **Interactive Map**: Click to place markers
- **Search Functionality**: Search for addresses and places
- **Current Location**: Get user's current GPS location
- **Drag & Drop**: Drag markers to fine-tune position
- **Reverse Geocoding**: Convert coordinates to addresses

### MapDisplay Component
- **Location Display**: Show property/car locations
- **Navigation Links**: Direct links to Google Maps for directions
- **Info Windows**: Popup with location details
- **Custom Markers**: Styled markers for better visibility

### Database Integration
- **Coordinates Storage**: Latitude and longitude for precise positioning
- **Address Storage**: Formatted addresses from Google Places
- **Backward Compatibility**: Existing text location field preserved

## Troubleshooting

### Common Issues

1. **"Google Maps API Key Required" Error**
   - Check that your API key is correctly set in the `.env` file
   - Ensure the environment variable name is exactly `VITE_GOOGLE_MAPS_API_KEY`
   - Restart the development server after changing environment variables

2. **"Failed to load Google Maps" Error**
   - Verify that the Maps JavaScript API is enabled in Google Cloud Console
   - Check that your API key has the correct restrictions
   - Ensure your domain is allowed in the API key restrictions

3. **Search Not Working**
   - Verify that the Places API is enabled
   - Check that your API key has access to the Places API

4. **Current Location Not Working**
   - Ensure you're using HTTPS (required for geolocation)
   - Check browser permissions for location access
   - Note: `localhost` is allowed for development

### API Quotas and Billing

- Google Maps APIs have usage quotas and may require billing setup
- Monitor your usage in the Google Cloud Console
- Consider implementing usage limits in production

## Security Best Practices

1. **Restrict API Keys**: Always restrict API keys to specific domains and APIs
2. **Environment Variables**: Never commit API keys to version control
3. **Server-Side Validation**: Validate location data on the server side
4. **Rate Limiting**: Implement rate limiting for API calls

## Production Deployment

Before deploying to production:

1. Update API key restrictions to include your production domain
2. Set up billing in Google Cloud Platform
3. Monitor API usage and costs
4. Consider implementing caching for geocoding results
5. Add error handling for API failures

## Support

For issues with the Google Maps integration:
1. Check the browser console for error messages
2. Verify API key configuration
3. Test with a fresh API key if needed
4. Consult the [Google Maps JavaScript API documentation](https://developers.google.com/maps/documentation/javascript)
