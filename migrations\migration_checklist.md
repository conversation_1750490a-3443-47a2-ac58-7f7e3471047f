# Supabase Migration Checklist

## Pre-Migration Preparation

- [ ] **Backup Current Project**
  - [ ] Export all data using `05_scripts/export_current.sh`
  - [ ] Download all edge functions source code
  - [ ] Save current configuration files
  - [ ] Document all environment variables

- [ ] **Review Migration Files**
  - [ ] Check all SQL files in `01_database/schema/`
  - [ ] Verify edge functions in `02_edge_functions/`
  - [ ] Review storage configuration in `03_storage/`
  - [ ] Update configuration files in `04_config/`

- [ ] **Prepare New Organization**
  - [ ] Ensure target organization exists
  - [ ] Verify billing and permissions
  - [ ] Prepare domain/DNS changes if needed

## Migration Steps

### Step 1: Create New Project
- [ ] Create new Supabase project in target organization
- [ ] Note down new project details:
  - [ ] Project ID: `_________________`
  - [ ] Project URL: `_________________`
  - [ ] Anon Key: `_________________`
  - [ ] Service Role Key: `_________________`
- [ ] Choose same region as current project (ap-southeast-1)

### Step 2: Database Schema Migration
- [X] Run `01_database/schema/01_tables.sql`
- [X] Run `01_database/schema/02_functions.sql`
- [ ] Run `01_database/schema/03_triggers.sql`
- [ ] Run `01_database/schema/04_rls_policies.sql`
- [ ] Run `01_database/schema/05_indexes.sql`
- [ ] Run `01_database/schema/06_views.sql`
- [ ] Verify all tables, functions, and policies are created

### Step 3: Data Migration
- [ ] Run data export from current project
- [ ] Import data to new project using `01_database/data/import_data.sql`
- [ ] Verify data integrity and counts
- [ ] Test sample queries

### Step 4: Storage Setup
- [ ] Create storage buckets using `03_storage/buckets.sql`
- [ ] Apply storage policies using `03_storage/policies.sql`
- [ ] Migrate existing files (if any)
- [ ] Test file upload/download

### Step 5: Edge Functions Deployment
- [ ] Deploy `send-booking-confirmation` function
- [ ] Deploy `send-booking-reminders` function
- [ ] Deploy `process-stripe-payment` function
- [ ] Deploy `verify-stripe-payment` function
- [ ] Deploy `process-wave-payment` function
- [ ] Deploy `verify-wave-payment` function
- [ ] Test all functions with sample data

### Step 6: Configuration
- [ ] Update `config.toml` with new project settings
- [ ] Set all environment variables (see `04_config/environment_variables.md`)
- [ ] Configure auth settings using `04_config/auth_settings.sql`
- [ ] Update CORS settings if needed

### Step 7: Application Updates
- [ ] Update frontend environment variables:
  - [ ] `VITE_SUPABASE_URL`
  - [ ] `VITE_SUPABASE_ANON_KEY`
- [ ] Update any hardcoded references to old project
- [ ] Update CI/CD pipelines
- [ ] Update monitoring and logging

### Step 8: Testing
- [ ] **Authentication Testing**
  - [ ] User registration
  - [ ] User login
  - [ ] Password reset
  - [ ] Profile updates

- [ ] **Core Functionality Testing**
  - [ ] Property listings
  - [ ] Car listings
  - [ ] Hotel listings
  - [ ] Booking creation
  - [ ] Payment processing
  - [ ] Messaging system

- [ ] **Admin Functionality Testing**
  - [ ] Admin login
  - [ ] User management
  - [ ] Booking management
  - [ ] Payout requests

- [ ] **Edge Functions Testing**
  - [ ] Booking confirmations
  - [ ] Payment processing
  - [ ] Reminder emails

### Step 9: Go-Live
- [ ] Update DNS records (if applicable)
- [ ] Switch production traffic to new project
- [ ] Monitor for errors and performance
- [ ] Verify all integrations work correctly

### Step 10: Post-Migration
- [ ] Monitor system for 24-48 hours
- [ ] Verify all scheduled functions work
- [ ] Check payment processing
- [ ] Confirm email notifications
- [ ] Update documentation
- [ ] Archive old project (after confirmation)

## Rollback Plan

In case of issues:
- [ ] Keep old project running during migration
- [ ] Have DNS/traffic switch ready to revert
- [ ] Maintain data sync during transition period
- [ ] Document rollback procedures

## Important Notes

- **Test thoroughly** in a staging environment first
- **Coordinate with team** for minimal downtime
- **Have support contacts** ready for both organizations
- **Monitor closely** for the first 48 hours after migration
