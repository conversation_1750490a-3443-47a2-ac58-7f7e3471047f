
import { CreditCard, BanknoteIcon } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

export interface PaymentMethod {
  id: string;
  host_id: string;
  provider: string;
  account_id: string;
  status: string;
  is_default: boolean;
  created_at: string;
  updated_at: string;
}

interface PaymentMethodsListProps {
  paymentMethods: PaymentMethod[];
  onAddPaymentMethod: () => void;
}

const PaymentMethodsList = ({ paymentMethods, onAddPaymentMethod }: PaymentMethodsListProps) => {
  if (paymentMethods.length === 0) {
    return (
      <div className="border rounded-md p-8 border-dashed text-center">
        <div className="flex flex-col items-center justify-center">
          <CreditCard className="h-8 w-8 mb-2 text-muted-foreground" />
          <h3 className="font-medium">No Payment Methods</h3>
          <p className="text-sm text-muted-foreground text-center mt-1 max-w-md">
            Add your Stripe or Wave account to receive payouts from bookings.
          </p>
          <Button 
            className="mt-4" 
            onClick={onAddPaymentMethod}
          >
            Add Payment Method
          </Button>
        </div>
      </div>
    );
  }
  
  return (
    <>
      {paymentMethods.map((method) => (
        <div key={method.id} className="border rounded-md p-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              {method.provider === "stripe" ? (
                <CreditCard className="h-6 w-6 mr-2" />
              ) : (
                <BanknoteIcon className="h-6 w-6 mr-2" />
              )}
              <div>
                <h3 className="font-medium">
                  {method.provider === "stripe" ? "Stripe" : "Wave"} Account
                  {method.is_default && <span className="ml-2 text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded">Default</span>}
                </h3>
                <p className="text-sm text-muted-foreground">
                  {method.account_id}
                  {" • "}
                  <span className={
                    method.status === "verified" ? "text-green-600" : 
                    method.status === "pending_verification" ? "text-amber-600" : 
                    "text-red-600"
                  }>
                    {method.status === "verified" ? "Verified" : 
                     method.status === "pending_verification" ? "Pending Verification" : 
                     "Verification Failed"}
                  </span>
                </p>
              </div>
            </div>
            <Button variant="ghost" size="sm">Edit</Button>
          </div>
        </div>
      ))}
    </>
  );
};

export default PaymentMethodsList;
