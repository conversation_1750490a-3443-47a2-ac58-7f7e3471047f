-- Gesco Stay Database Schema - Row Level Security Policies
-- This file contains all RLS policies for the Gesco Stay application

-- Enable RLS on all tables
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.properties ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.cars ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.hotels ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.bookings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.car_bookings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.hotel_bookings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.car_insurance_options ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.host_payment_methods ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.payout_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.platform_earnings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.payment_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.admin_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.admin_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.checkout_reminders_sent ENABLE ROW LEVEL SECURITY;

-- Profiles policies
CREATE POLICY "Users can view their own profile" ON public.profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON public.profiles
  FOR UPDATE USING (auth.uid() = id);

-- Properties policies
CREATE POLICY "Anyone can view properties" ON public.properties
  FOR SELECT USING (true);

CREATE POLICY "Users can create their own properties" ON public.properties
  FOR INSERT WITH CHECK (auth.uid() = owner_id);

CREATE POLICY "Users can update their own properties" ON public.properties
  FOR UPDATE USING (auth.uid() = owner_id);

CREATE POLICY "Users can delete their own properties" ON public.properties
  FOR DELETE USING (auth.uid() = owner_id);

CREATE POLICY "Admins can view all properties" ON public.properties
  FOR SELECT USING (is_admin(auth.uid()) OR (owner_id = auth.uid()));

CREATE POLICY "Admins can update all properties" ON public.properties
  FOR UPDATE USING (is_admin(auth.uid()) OR (owner_id = auth.uid()));

-- Cars policies
CREATE POLICY "Anyone can view cars" ON public.cars
  FOR SELECT USING (true);

CREATE POLICY "Car owners can create their cars" ON public.cars
  FOR INSERT WITH CHECK (auth.uid() = owner_id);

CREATE POLICY "Car owners can update their cars" ON public.cars
  FOR UPDATE USING (auth.uid() = owner_id);

CREATE POLICY "Car owners can delete their cars" ON public.cars
  FOR DELETE USING (auth.uid() = owner_id);

CREATE POLICY "Admins can view all cars" ON public.cars
  FOR SELECT USING (is_admin(auth.uid()) OR (owner_id = auth.uid()));

CREATE POLICY "Admins can update all cars" ON public.cars
  FOR UPDATE USING (is_admin(auth.uid()) OR (owner_id = auth.uid()));

-- Hotels policies (similar to properties and cars)
CREATE POLICY "Anyone can view hotels" ON public.hotels
  FOR SELECT USING (true);

CREATE POLICY "Hotel owners can create their hotels" ON public.hotels
  FOR INSERT WITH CHECK (auth.uid() = owner_id);

CREATE POLICY "Hotel owners can update their hotels" ON public.hotels
  FOR UPDATE USING (auth.uid() = owner_id);

CREATE POLICY "Hotel owners can delete their hotels" ON public.hotels
  FOR DELETE USING (auth.uid() = owner_id);

-- Bookings policies
CREATE POLICY "Users can view their own bookings" ON public.bookings
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create bookings" ON public.bookings
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own bookings" ON public.bookings
  FOR UPDATE USING (auth.uid() = user_id);

-- Car bookings policies
CREATE POLICY "Authenticated users can create bookings" ON public.car_bookings
  FOR INSERT TO authenticated WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Car owners can view bookings for their cars" ON public.car_bookings
  FOR SELECT TO authenticated USING (
    (auth.uid() IN (SELECT cars.owner_id FROM cars WHERE cars.id = car_bookings.car_id)) OR 
    (auth.uid() = user_id)
  );

CREATE POLICY "Users can update their own bookings" ON public.car_bookings
  FOR UPDATE TO authenticated USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own bookings" ON public.car_bookings
  FOR DELETE TO authenticated USING (auth.uid() = user_id);

-- Hotel bookings policies (similar to car bookings)
CREATE POLICY "Users can view their own hotel bookings" ON public.hotel_bookings
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create hotel bookings" ON public.hotel_bookings
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own hotel bookings" ON public.hotel_bookings
  FOR UPDATE USING (auth.uid() = user_id);

-- Reviews policies
CREATE POLICY "Anyone can read reviews" ON public.reviews
  FOR SELECT USING (true);

CREATE POLICY "Users can create their own reviews" ON public.reviews
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own reviews" ON public.reviews
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own reviews" ON public.reviews
  FOR DELETE USING (auth.uid() = user_id);

-- Car insurance options policies
CREATE POLICY "Anyone can view insurance options" ON public.car_insurance_options
  FOR SELECT USING (true);

CREATE POLICY "Only admins can modify insurance options" ON public.car_insurance_options
  FOR ALL TO authenticated USING (
    auth.uid() IN (SELECT profiles.id FROM profiles WHERE profiles.role = 'admin'::user_role)
  ) WITH CHECK (
    auth.uid() IN (SELECT profiles.id FROM profiles WHERE profiles.role = 'admin'::user_role)
  );

-- Conversations policies
CREATE POLICY "conversations_select_policy" ON public.conversations
  FOR SELECT USING ((auth.uid() = participant_1_id) OR (auth.uid() = participant_2_id));

CREATE POLICY "conversations_insert_policy" ON public.conversations
  FOR INSERT WITH CHECK ((auth.uid() = participant_1_id) OR (auth.uid() = participant_2_id));

CREATE POLICY "conversations_update_policy" ON public.conversations
  FOR UPDATE USING ((auth.uid() = participant_1_id) OR (auth.uid() = participant_2_id));

-- Messages policies
CREATE POLICY "messages_select_policy" ON public.messages
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM conversations
      WHERE conversations.id = messages.conversation_id
      AND ((conversations.participant_1_id = auth.uid()) OR (conversations.participant_2_id = auth.uid()))
    )
  );

CREATE POLICY "messages_insert_policy" ON public.messages
  FOR INSERT WITH CHECK (
    (auth.uid() = sender_id) AND 
    (EXISTS (
      SELECT 1 FROM conversations
      WHERE conversations.id = messages.conversation_id
      AND ((conversations.participant_1_id = auth.uid()) OR (conversations.participant_2_id = auth.uid()))
    ))
  );

CREATE POLICY "messages_update_policy" ON public.messages
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM conversations
      WHERE conversations.id = messages.conversation_id
      AND ((conversations.participant_1_id = auth.uid()) OR (conversations.participant_2_id = auth.uid()))
    )
  );

-- Host payment methods policies
CREATE POLICY "Hosts can view their own payment methods" ON public.host_payment_methods
  FOR SELECT USING (auth.uid() = host_id);

CREATE POLICY "Hosts can insert their own payment methods" ON public.host_payment_methods
  FOR INSERT WITH CHECK (auth.uid() = host_id);

CREATE POLICY "Hosts can update their own payment methods" ON public.host_payment_methods
  FOR UPDATE USING (auth.uid() = host_id);

CREATE POLICY "Hosts can delete their own payment methods" ON public.host_payment_methods
  FOR DELETE USING (auth.uid() = host_id);

CREATE POLICY "Admins can view all payment methods" ON public.host_payment_methods
  FOR SELECT USING (
    (SELECT profiles.role FROM profiles WHERE profiles.id = auth.uid()) = 'admin'::user_role
  );

CREATE POLICY "Admins can update all payment methods" ON public.host_payment_methods
  FOR UPDATE USING (
    (SELECT profiles.role FROM profiles WHERE profiles.id = auth.uid()) = 'admin'::user_role
  );

-- Payout requests policies
CREATE POLICY "Hosts can view their own payout requests" ON public.payout_requests
  FOR SELECT USING (auth.uid() = host_id);

CREATE POLICY "Hosts can insert their own payout requests" ON public.payout_requests
  FOR INSERT WITH CHECK (auth.uid() = host_id);

CREATE POLICY "Admins can view all payout requests" ON public.payout_requests
  FOR SELECT USING (
    (SELECT profiles.role FROM profiles WHERE profiles.id = auth.uid()) = 'admin'::user_role
  );

CREATE POLICY "Admins can update all payout requests" ON public.payout_requests
  FOR UPDATE USING (
    (SELECT profiles.role FROM profiles WHERE profiles.id = auth.uid()) = 'admin'::user_role
  );

-- Platform earnings policies
CREATE POLICY "Admin can view platform earnings" ON public.platform_earnings
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid() AND profiles.role = 'admin'::user_role
    )
  );

CREATE POLICY "Only system can insert platform earnings" ON public.platform_earnings
  FOR INSERT WITH CHECK (false);

CREATE POLICY "Only system can update platform earnings" ON public.platform_earnings
  FOR UPDATE USING (false);

CREATE POLICY "Only system can delete platform earnings" ON public.platform_earnings
  FOR DELETE USING (false);

-- Payment logs policies
CREATE POLICY "Users can view their own payment logs" ON public.payment_logs
  FOR SELECT USING (
    booking_id IN (SELECT bookings.id FROM bookings WHERE bookings.user_id = auth.uid())
  );

CREATE POLICY "Backend can insert payment logs" ON public.payment_logs
  FOR INSERT WITH CHECK (true);

-- Admin users policies
CREATE POLICY "Service role can manage admin users" ON public.admin_users
  FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "admin_users_select_policy" ON public.admin_users
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM admin_sessions
      WHERE admin_sessions.admin_user_id = auth.uid()
      AND admin_sessions.expires_at > now()
    )
  );

CREATE POLICY "admin_users_insert_policy" ON public.admin_users
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM admin_users au
      JOIN admin_sessions s ON au.id = s.admin_user_id
      WHERE s.admin_user_id = auth.uid()
      AND au.role = 'super_admin'
      AND s.expires_at > now()
    )
  );

CREATE POLICY "admin_users_update_policy" ON public.admin_users
  FOR UPDATE USING (
    (id = auth.uid()) OR 
    (EXISTS (
      SELECT 1 FROM admin_users au
      JOIN admin_sessions s ON au.id = s.admin_user_id
      WHERE s.admin_user_id = auth.uid()
      AND au.role = 'super_admin'
      AND s.expires_at > now()
    ))
  );

-- Admin sessions policies
CREATE POLICY "Service role can manage admin sessions" ON public.admin_sessions
  FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "admin_sessions_select_policy" ON public.admin_sessions
  FOR SELECT USING (admin_user_id = auth.uid());

CREATE POLICY "admin_sessions_insert_policy" ON public.admin_sessions
  FOR INSERT WITH CHECK (admin_user_id = auth.uid());

CREATE POLICY "admin_sessions_update_policy" ON public.admin_sessions
  FOR UPDATE USING (admin_user_id = auth.uid());

CREATE POLICY "admin_sessions_delete_policy" ON public.admin_sessions
  FOR DELETE USING (admin_user_id = auth.uid());

-- Checkout reminders policies
CREATE POLICY "Service role can manage checkout reminders" ON public.checkout_reminders_sent
  FOR ALL USING (auth.role() = 'service_role');
