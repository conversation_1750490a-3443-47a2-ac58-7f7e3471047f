
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Gesco Stay Color Palette - Light Theme */
    --background: 45 25% 96%; /* Very light warm tan */
    --foreground: 25 25% 18%; /* Dark brown #72452E */

    --card: 0 0% 100%;
    --card-foreground: 25 25% 18%; /* Dark brown */

    --popover: 0 0% 100%;
    --popover-foreground: 25 25% 18%; /* Dark brown */

    --primary: 45 25% 60%; /* Warm tan #C2BD70 */
    --primary-foreground: 25 25% 18%; /* Dark brown */

    --secondary: 184 20% 52%; /* Muted teal #68A19B */
    --secondary-foreground: 0 0% 100%;

    --muted: 45 15% 92%;
    --muted-foreground: 25 15% 45%; /* Taupe brown #8B7266 */

    --accent: 22 65% 62%; /* Soft orange #E0895A */
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 45 15% 88%;
    --input: 45 15% 88%;
    --ring: 25 25% 18%; /* Dark brown */

    --radius: 0.75rem;

    --sidebar-background: 45 25% 98%;
    --sidebar-foreground: 25 15% 45%; /* Taupe brown */
    --sidebar-primary: 25 25% 18%; /* Dark brown */
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 45 15% 95%;
    --sidebar-accent-foreground: 25 25% 18%;
    --sidebar-border: 45 15% 88%;
    --sidebar-ring: 22 65% 62%; /* Soft orange */
  }

  .dark {
    /* Gesco Stay Color Palette - Dark Theme */
    --background: 200 15% 15%; /* Charcoal gray #576160 */
    --foreground: 45 25% 90%; /* Light warm tan */

    --card: 200 15% 18%;
    --card-foreground: 45 25% 90%;

    --popover: 200 15% 18%;
    --popover-foreground: 45 25% 90%;

    --primary: 45 25% 60%; /* Warm tan #C2BD70 */
    --primary-foreground: 25 25% 18%; /* Dark brown */

    --secondary: 184 20% 52%; /* Muted teal #68A19B */
    --secondary-foreground: 0 0% 100%;

    --muted: 200 15% 22%;
    --muted-foreground: 45 15% 70%;

    --accent: 22 65% 62%; /* Soft orange #E0895A */
    --accent-foreground: 0 0% 100%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 200 15% 25%;
    --input: 200 15% 25%;
    --ring: 45 25% 60%; /* Warm tan */
    --sidebar-background: 200 15% 12%;
    --sidebar-foreground: 45 15% 80%;
    --sidebar-primary: 45 25% 60%; /* Warm tan */
    --sidebar-primary-foreground: 25 25% 18%;
    --sidebar-accent: 200 15% 20%;
    --sidebar-accent-foreground: 45 15% 80%;
    --sidebar-border: 200 15% 25%;
    --sidebar-ring: 22 65% 62%; /* Soft orange */
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-inter;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-playfair font-medium;
  }
}

.property-card-transition {
  transition: transform 0.3s ease;
}

.property-card-transition:hover {
  transform: translateY(-5px);
}

/* Enhanced animations for landing page */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.8s ease-out;
}

/* Smooth section transitions */
section {
  position: relative;
}

/* Hero section specific styles */
.hero-gradient {
  background: linear-gradient(135deg, hsl(200 15% 15% / 0.7) 0%, hsl(200 15% 15% / 0.5) 50%, hsl(200 15% 15% / 0.8) 100%);
}

/* Text truncation utilities */
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
