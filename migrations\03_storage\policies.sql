-- Gesco Stay Storage Configuration - Policies
-- This file creates all storage policies for the Gesco Stay application

-- Enable RLS on storage.objects
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- Property Images Policies
CREATE POLICY "Anyone can view property images" ON storage.objects
  FOR SELECT USING (bucket_id = 'property-images');

CREATE POLICY "Property owners can upload images" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'property-images' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Property owners can update their images" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'property-images' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Property owners can delete their images" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'property-images' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

-- Car Images Policies
CREATE POLICY "Anyone can view car images" ON storage.objects
  FOR SELECT USING (bucket_id = 'car-images');

CREATE POLICY "Car owners can upload images" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'car-images' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Car owners can update their images" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'car-images' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Car owners can delete their images" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'car-images' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

-- Hotel Images Policies
CREATE POLICY "Anyone can view hotel images" ON storage.objects
  FOR SELECT USING (bucket_id = 'hotel-images');

CREATE POLICY "Hotel owners can upload images" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'hotel-images' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Hotel owners can update their images" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'hotel-images' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Hotel owners can delete their images" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'hotel-images' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

-- Profile Avatars Policies
CREATE POLICY "Anyone can view profile avatars" ON storage.objects
  FOR SELECT USING (bucket_id = 'profile-avatars');

CREATE POLICY "Users can upload their own avatar" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'profile-avatars' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can update their own avatar" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'profile-avatars' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can delete their own avatar" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'profile-avatars' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

-- Documents Policies (private bucket)
CREATE POLICY "Users can view their own documents" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'documents' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can upload their own documents" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'documents' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can update their own documents" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'documents' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can delete their own documents" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'documents' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

-- Admin policies for all buckets
CREATE POLICY "Admins can manage all storage objects" ON storage.objects
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Service role policies (for edge functions and backend operations)
CREATE POLICY "Service role can manage all objects" ON storage.objects
  FOR ALL USING (auth.role() = 'service_role');

-- Comments for documentation
COMMENT ON POLICY "Anyone can view property images" ON storage.objects IS 'Public read access for property images';
COMMENT ON POLICY "Property owners can upload images" ON storage.objects IS 'Property owners can upload images to their folder';
COMMENT ON POLICY "Users can view their own documents" ON storage.objects IS 'Private document access restricted to owner';
COMMENT ON POLICY "Admins can manage all storage objects" ON storage.objects IS 'Admin users have full access to all storage objects';

-- Verify policies were created
SELECT 
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual
FROM pg_policies 
WHERE schemaname = 'storage' AND tablename = 'objects'
ORDER BY policyname;
