-- Gesco Stay Database Schema - Additional Indexes
-- This file contains additional database indexes for performance optimization
-- Note: Basic indexes are already created in 01_tables.sql

-- Additional performance indexes

-- Profiles indexes
CREATE INDEX IF NOT EXISTS idx_profiles_role ON public.profiles(role);
CREATE INDEX IF NOT EXISTS idx_profiles_email ON public.profiles(email);
CREATE INDEX IF NOT EXISTS idx_profiles_phone_number ON public.profiles(phone_number);

-- Properties advanced indexes
CREATE INDEX IF NOT EXISTS idx_properties_price ON public.properties(price);
CREATE INDEX IF NOT EXISTS idx_properties_beds_baths ON public.properties(beds, baths);
CREATE INDEX IF NOT EXISTS idx_properties_created_at ON public.properties(created_at);
CREATE INDEX IF NOT EXISTS idx_properties_location_gin ON public.properties USING gin(to_tsvector('english', location));
CREATE INDEX IF NOT EXISTS idx_properties_title_gin ON public.properties USING gin(to_tsvector('english', title));
CREATE INDEX IF NOT EXISTS idx_properties_features_gin ON public.properties USING gin(features);

-- Cars advanced indexes
CREATE INDEX IF NOT EXISTS idx_cars_make_model ON public.cars(make, model);
CREATE INDEX IF NOT EXISTS idx_cars_year ON public.cars(year);
CREATE INDEX IF NOT EXISTS idx_cars_car_type ON public.cars(car_type);
CREATE INDEX IF NOT EXISTS idx_cars_price_day ON public.cars(price_day);
CREATE INDEX IF NOT EXISTS idx_cars_seats ON public.cars(seats);
CREATE INDEX IF NOT EXISTS idx_cars_transmission ON public.cars(transmission);
CREATE INDEX IF NOT EXISTS idx_cars_fuel_type ON public.cars(fuel_type);
CREATE INDEX IF NOT EXISTS idx_cars_created_at ON public.cars(created_at);
CREATE INDEX IF NOT EXISTS idx_cars_title_gin ON public.cars USING gin(to_tsvector('english', title));
CREATE INDEX IF NOT EXISTS idx_cars_features_gin ON public.cars USING gin(features);

-- Hotels advanced indexes
CREATE INDEX IF NOT EXISTS idx_hotels_created_at ON public.hotels(created_at);
CREATE INDEX IF NOT EXISTS idx_hotels_title_gin ON public.hotels USING gin(to_tsvector('english', title));
CREATE INDEX IF NOT EXISTS idx_hotels_amenities_gin ON public.hotels USING gin(amenities);

-- Room types indexes
CREATE INDEX IF NOT EXISTS idx_room_types_hotel_id ON public.room_types(hotel_id);
CREATE INDEX IF NOT EXISTS idx_room_types_max_occupancy ON public.room_types(max_occupancy);
CREATE INDEX IF NOT EXISTS idx_room_types_base_price ON public.room_types(base_price);

-- Room inventory indexes
CREATE INDEX IF NOT EXISTS idx_room_inventory_room_type_id ON public.room_inventory(room_type_id);
CREATE INDEX IF NOT EXISTS idx_room_inventory_status ON public.room_inventory(status);
CREATE INDEX IF NOT EXISTS idx_room_inventory_floor_number ON public.room_inventory(floor_number);

-- Seasonal pricing indexes
CREATE INDEX IF NOT EXISTS idx_seasonal_pricing_room_type_id ON public.seasonal_pricing(room_type_id);
CREATE INDEX IF NOT EXISTS idx_seasonal_pricing_dates ON public.seasonal_pricing(start_date, end_date);

-- Bookings advanced indexes
CREATE INDEX IF NOT EXISTS idx_bookings_payment_status ON public.bookings(payment_status);
CREATE INDEX IF NOT EXISTS idx_bookings_payment_method ON public.bookings(payment_method);
CREATE INDEX IF NOT EXISTS idx_bookings_created_at ON public.bookings(created_at);
CREATE INDEX IF NOT EXISTS idx_bookings_total_price ON public.bookings(total_price);

-- Car bookings advanced indexes
CREATE INDEX IF NOT EXISTS idx_car_bookings_duration_type ON public.car_bookings(duration_type);
CREATE INDEX IF NOT EXISTS idx_car_bookings_payment_status ON public.car_bookings(payment_status);
CREATE INDEX IF NOT EXISTS idx_car_bookings_payment_method ON public.car_bookings(payment_method);
CREATE INDEX IF NOT EXISTS idx_car_bookings_created_at ON public.car_bookings(created_at);
CREATE INDEX IF NOT EXISTS idx_car_bookings_total_price ON public.car_bookings(total_price);

-- Hotel bookings advanced indexes
CREATE INDEX IF NOT EXISTS idx_hotel_bookings_adults_children ON public.hotel_bookings(adults, children);
CREATE INDEX IF NOT EXISTS idx_hotel_bookings_rooms_count ON public.hotel_bookings(rooms_count);
CREATE INDEX IF NOT EXISTS idx_hotel_bookings_payment_status ON public.hotel_bookings(payment_status);
CREATE INDEX IF NOT EXISTS idx_hotel_bookings_payment_method ON public.hotel_bookings(payment_method);
CREATE INDEX IF NOT EXISTS idx_hotel_bookings_created_at ON public.hotel_bookings(created_at);
CREATE INDEX IF NOT EXISTS idx_hotel_bookings_total_price ON public.hotel_bookings(total_price);

-- Room assignments indexes
CREATE INDEX IF NOT EXISTS idx_room_assignments_hotel_booking_id ON public.room_assignments(hotel_booking_id);
CREATE INDEX IF NOT EXISTS idx_room_assignments_room_inventory_id ON public.room_assignments(room_inventory_id);
CREATE INDEX IF NOT EXISTS idx_room_assignments_assigned_at ON public.room_assignments(assigned_at);

-- Reviews indexes
CREATE INDEX IF NOT EXISTS idx_reviews_property_id ON public.reviews(property_id);
CREATE INDEX IF NOT EXISTS idx_reviews_car_id ON public.reviews(car_id);
CREATE INDEX IF NOT EXISTS idx_reviews_hotel_id ON public.reviews(hotel_id);
CREATE INDEX IF NOT EXISTS idx_reviews_user_id ON public.reviews(user_id);
CREATE INDEX IF NOT EXISTS idx_reviews_rating ON public.reviews(rating);
CREATE INDEX IF NOT EXISTS idx_reviews_created_at ON public.reviews(created_at);

-- Car insurance options indexes
CREATE INDEX IF NOT EXISTS idx_car_insurance_price_day ON public.car_insurance_options(price_day);
CREATE INDEX IF NOT EXISTS idx_car_insurance_created_at ON public.car_insurance_options(created_at);

-- Conversations advanced indexes
CREATE INDEX IF NOT EXISTS idx_conversations_property_id ON public.conversations(property_id);
CREATE INDEX IF NOT EXISTS idx_conversations_car_id ON public.conversations(car_id);
CREATE INDEX IF NOT EXISTS idx_conversations_hotel_id ON public.conversations(hotel_id);
CREATE INDEX IF NOT EXISTS idx_conversations_last_message_at ON public.conversations(last_message_at);
CREATE INDEX IF NOT EXISTS idx_conversations_created_at ON public.conversations(created_at);

-- Messages advanced indexes
CREATE INDEX IF NOT EXISTS idx_messages_created_at ON public.messages(created_at);
CREATE INDEX IF NOT EXISTS idx_messages_read_at ON public.messages(read_at);
CREATE INDEX IF NOT EXISTS idx_messages_content_gin ON public.messages USING gin(to_tsvector('english', content));

-- Host payment methods indexes
CREATE INDEX IF NOT EXISTS idx_host_payment_methods_host_id ON public.host_payment_methods(host_id);
CREATE INDEX IF NOT EXISTS idx_host_payment_methods_provider ON public.host_payment_methods(provider);
CREATE INDEX IF NOT EXISTS idx_host_payment_methods_status ON public.host_payment_methods(status);
CREATE INDEX IF NOT EXISTS idx_host_payment_methods_is_default ON public.host_payment_methods(is_default);

-- Payout requests indexes
CREATE INDEX IF NOT EXISTS idx_payout_requests_host_id ON public.payout_requests(host_id);
CREATE INDEX IF NOT EXISTS idx_payout_requests_status ON public.payout_requests(status);
CREATE INDEX IF NOT EXISTS idx_payout_requests_amount ON public.payout_requests(amount);
CREATE INDEX IF NOT EXISTS idx_payout_requests_created_at ON public.payout_requests(created_at);
CREATE INDEX IF NOT EXISTS idx_payout_requests_processed_at ON public.payout_requests(processed_at);
CREATE INDEX IF NOT EXISTS idx_payout_requests_processed_by ON public.payout_requests(processed_by);

-- Platform earnings indexes
CREATE INDEX IF NOT EXISTS idx_platform_earnings_booking_id ON public.platform_earnings(booking_id);
CREATE INDEX IF NOT EXISTS idx_platform_earnings_car_booking_id ON public.platform_earnings(car_booking_id);
CREATE INDEX IF NOT EXISTS idx_platform_earnings_hotel_booking_id ON public.platform_earnings(hotel_booking_id);
CREATE INDEX IF NOT EXISTS idx_platform_earnings_booking_type ON public.platform_earnings(booking_type);
CREATE INDEX IF NOT EXISTS idx_platform_earnings_created_at ON public.platform_earnings(created_at);
CREATE INDEX IF NOT EXISTS idx_platform_earnings_platform_fee ON public.platform_earnings(platform_fee);

-- Payment logs indexes
CREATE INDEX IF NOT EXISTS idx_payment_logs_booking_id ON public.payment_logs(booking_id);
CREATE INDEX IF NOT EXISTS idx_payment_logs_car_booking_id ON public.payment_logs(car_booking_id);
CREATE INDEX IF NOT EXISTS idx_payment_logs_booking_type ON public.payment_logs(booking_type);
CREATE INDEX IF NOT EXISTS idx_payment_logs_payment_method ON public.payment_logs(payment_method);
CREATE INDEX IF NOT EXISTS idx_payment_logs_status ON public.payment_logs(status);
CREATE INDEX IF NOT EXISTS idx_payment_logs_created_at ON public.payment_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_payment_logs_transaction_id ON public.payment_logs(transaction_id);

-- Admin users indexes
CREATE INDEX IF NOT EXISTS idx_admin_users_email ON public.admin_users(email);
CREATE INDEX IF NOT EXISTS idx_admin_users_role ON public.admin_users(role);
CREATE INDEX IF NOT EXISTS idx_admin_users_is_active ON public.admin_users(is_active);
CREATE INDEX IF NOT EXISTS idx_admin_users_created_at ON public.admin_users(created_at);
CREATE INDEX IF NOT EXISTS idx_admin_users_last_login_at ON public.admin_users(last_login_at);

-- Admin sessions indexes
CREATE INDEX IF NOT EXISTS idx_admin_sessions_admin_user_id ON public.admin_sessions(admin_user_id);
CREATE INDEX IF NOT EXISTS idx_admin_sessions_session_token ON public.admin_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_admin_sessions_expires_at ON public.admin_sessions(expires_at);
CREATE INDEX IF NOT EXISTS idx_admin_sessions_created_at ON public.admin_sessions(created_at);

-- Checkout reminders indexes
CREATE INDEX IF NOT EXISTS idx_checkout_reminders_booking_id ON public.checkout_reminders_sent(booking_id);
CREATE INDEX IF NOT EXISTS idx_checkout_reminders_reminder_type ON public.checkout_reminders_sent(reminder_type);
CREATE INDEX IF NOT EXISTS idx_checkout_reminders_sent_at ON public.checkout_reminders_sent(sent_at);

-- Composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_properties_status_location ON public.properties(status, location);
CREATE INDEX IF NOT EXISTS idx_cars_status_location ON public.cars(status, location);
CREATE INDEX IF NOT EXISTS idx_hotels_status_location ON public.hotels(status, location);

CREATE INDEX IF NOT EXISTS idx_bookings_user_status ON public.bookings(user_id, status);
CREATE INDEX IF NOT EXISTS idx_car_bookings_user_status ON public.car_bookings(user_id, status);
CREATE INDEX IF NOT EXISTS idx_hotel_bookings_user_status ON public.hotel_bookings(user_id, status);

CREATE INDEX IF NOT EXISTS idx_bookings_property_dates ON public.bookings(property_id, check_in, check_out);
CREATE INDEX IF NOT EXISTS idx_hotel_bookings_room_dates ON public.hotel_bookings(room_type_id, check_in, check_out);

-- Partial indexes for better performance on filtered queries
CREATE INDEX IF NOT EXISTS idx_properties_approved ON public.properties(id) WHERE status = 'approved';
CREATE INDEX IF NOT EXISTS idx_cars_approved ON public.cars(id) WHERE status = 'approved';
CREATE INDEX IF NOT EXISTS idx_hotels_approved ON public.hotels(id) WHERE status = 'approved';

CREATE INDEX IF NOT EXISTS idx_bookings_confirmed ON public.bookings(property_id, check_in, check_out) WHERE status = 'confirmed';
CREATE INDEX IF NOT EXISTS idx_car_bookings_confirmed ON public.car_bookings(car_id, start_date, end_date) WHERE status = 'confirmed';
CREATE INDEX IF NOT EXISTS idx_hotel_bookings_confirmed ON public.hotel_bookings(room_type_id, check_in, check_out) WHERE status = 'confirmed';

CREATE INDEX IF NOT EXISTS idx_messages_unread ON public.messages(conversation_id, sender_id) WHERE read_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_admin_sessions_active ON public.admin_sessions(admin_user_id) WHERE expires_at > now();

-- Comments for documentation
COMMENT ON INDEX idx_properties_location_gin IS 'Full-text search index for property locations';
COMMENT ON INDEX idx_properties_title_gin IS 'Full-text search index for property titles';
COMMENT ON INDEX idx_cars_title_gin IS 'Full-text search index for car titles';
COMMENT ON INDEX idx_hotels_title_gin IS 'Full-text search index for hotel titles';
COMMENT ON INDEX idx_messages_content_gin IS 'Full-text search index for message content';
COMMENT ON INDEX idx_bookings_confirmed IS 'Partial index for confirmed bookings to optimize availability checks';
COMMENT ON INDEX idx_car_bookings_confirmed IS 'Partial index for confirmed car bookings to optimize availability checks';
COMMENT ON INDEX idx_hotel_bookings_confirmed IS 'Partial index for confirmed hotel bookings to optimize availability checks';
