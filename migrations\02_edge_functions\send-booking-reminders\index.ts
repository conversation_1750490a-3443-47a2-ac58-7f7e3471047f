import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.45.0";

const SUPABASE_URL = Deno.env.get("SUPABASE_URL") || "";
const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") || "";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Create Supabase client with admin privileges
    const supabaseAdmin = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY, { 
      auth: { persistSession: false } 
    });
    
    // Get current date and time
    const now = new Date();

    // Calculate 2 hours from now for checkout reminders
    const twoHoursFromNow = new Date(now.getTime() + (2 * 60 * 60 * 1000));
    const checkoutReminderDate = twoHoursFromNow.toISOString().split('T')[0];

    // Find bookings with checkout dates today that need 2-hour reminders
    const { data: bookingsNeedingReminders, error } = await supabaseAdmin
      .from("bookings")
      .select(`
        id,
        check_out,
        profiles:user_id(email, first_name),
        properties(title, location)
      `)
      .eq("status", "confirmed")
      .eq("check_out", checkoutReminderDate);
    
    if (error) throw error;
    
    console.log(`Found ${bookingsNeedingReminders?.length || 0} bookings that need checkout reminders`);

    // Send checkout reminders for each booking
    const reminderPromises = bookingsNeedingReminders?.map(async (booking) => {
      try {
        // Check if reminder already sent
        const { data: existingReminder } = await supabaseAdmin
          .from("checkout_reminders_sent")
          .select("id")
          .eq("booking_id", booking.id)
          .eq("reminder_type", "checkout_2h")
          .single();

        if (existingReminder) {
          console.log(`Checkout reminder already sent for booking ${booking.id}`);
          return;
        }

        // Send checkout reminder email
        const emailResponse = await fetch(`${SUPABASE_URL}/functions/v1/send-booking-confirmation`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${SUPABASE_SERVICE_ROLE_KEY}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            bookingId: booking.id,
            type: 'checkout_reminder'
          })
        });

        if (!emailResponse.ok) {
          throw new Error(`Failed to send checkout reminder email: ${emailResponse.statusText}`);
        }

        // Mark reminder as sent
        await supabaseAdmin
          .from("checkout_reminders_sent")
          .insert({
            booking_id: booking.id,
            reminder_type: "checkout_2h"
          });

        console.log(`Checkout reminder sent for booking ${booking.id}`);
      } catch (error) {
        console.error(`Error sending checkout reminder for booking ${booking.id}:`, error);
      }
    }) || [];

    await Promise.all(reminderPromises);

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: `Processed ${bookingsNeedingReminders?.length || 0} checkout reminders` 
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200,
      }
    );
  } catch (error) {
    console.error("Error in send-booking-reminders:", error);
    return new Response(
      JSON.stringify({ success: false, error: error.message }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 500,
      }
    );
  }
});
