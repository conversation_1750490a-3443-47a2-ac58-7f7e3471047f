-- Quick Fix for Admin Approval Issue
-- Run this in Supabase SQL Editor

-- The issue is likely that admin policies are being overridden by owner policies
-- We need to ensure admin policies take precedence

-- 1. Drop and recreate properties policies with correct precedence
DROP POLICY IF EXISTS "Users can update their own properties" ON public.properties;
DROP POLICY IF EXISTS "Admins can update all properties" ON public.properties;
DROP POLICY IF EXISTS "Ad<PERSON> can view all properties" ON public.properties;

-- Create admin policies first (they take precedence)
CREATE POLICY "admin_can_update_properties" ON public.properties
  FOR UPDATE TO authenticated 
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

CREATE POLICY "admin_can_view_properties" ON public.properties
  FOR SELECT TO authenticated 
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role = 'admin'
    ) OR owner_id = auth.uid()
  );

-- Then create owner policies
CREATE POLICY "owner_can_update_properties" ON public.properties
  FOR UPDATE TO authenticated 
  USING (auth.uid() = owner_id);

-- 2. Do the same for cars
DROP POLICY IF EXISTS "Car owners can update their cars" ON public.cars;
DROP POLICY IF EXISTS "Admins can update all cars" ON public.cars;
DROP POLICY IF EXISTS "Admins can view all cars" ON public.cars;

CREATE POLICY "admin_can_update_cars" ON public.cars
  FOR UPDATE TO authenticated 
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

CREATE POLICY "admin_can_view_cars" ON public.cars
  FOR SELECT TO authenticated 
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role = 'admin'
    ) OR owner_id = auth.uid()
  );

CREATE POLICY "owner_can_update_cars" ON public.cars
  FOR UPDATE TO authenticated 
  USING (auth.uid() = owner_id);

-- 3. Add hotels policies (these were missing)
DROP POLICY IF EXISTS "Hotel owners can update their hotels" ON public.hotels;
DROP POLICY IF EXISTS "Admins can update all hotels" ON public.hotels;
DROP POLICY IF EXISTS "Admins can view all hotels" ON public.hotels;

CREATE POLICY "admin_can_update_hotels" ON public.hotels
  FOR UPDATE TO authenticated 
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

CREATE POLICY "admin_can_view_hotels" ON public.hotels
  FOR SELECT TO authenticated 
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role = 'admin'
    ) OR owner_id = auth.uid()
  );

CREATE POLICY "owner_can_update_hotels" ON public.hotels
  FOR UPDATE TO authenticated 
  USING (auth.uid() = owner_id);

-- 4. Test the fix by updating the problematic property
UPDATE properties 
SET status = 'approved', updated_at = NOW() 
WHERE id = 'a75a72e9-ea8b-498b-a893-aaddf95fb8bc';

-- 5. Verify the update
SELECT id, title, status, updated_at 
FROM properties 
WHERE id = 'a75a72e9-ea8b-498b-a893-aaddf95fb8bc';
