import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "sonner";
import { Mail, Eye, Send } from "lucide-react";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";

const EmailPreview = () => {
  const [selectedTemplate, setSelectedTemplate] = useState("booking_confirmation");
  const [previewOpen, setPreviewOpen] = useState(false);
  
  // Mock email templates
  const emailTemplates = {
    booking_confirmation: {
      subject: "Your booking is confirmed! 🎉",
      content: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; color: #333;">
          <h2 style="color: #4f46e5;">Booking Confirmation</h2>
          <p>Dear {customer_name},</p>
          <p>We're excited to confirm your booking at <strong>{property_name}</strong>!</p>
          
          <div style="background-color: #f9fafb; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <h3 style="margin-top: 0;">Booking Details:</h3>
            <p><strong>Check-in:</strong> {check_in_date}</p>
            <p><strong>Check-out:</strong> {check_out_date}</p>
            <p><strong>Total:</strong> ${'{total_price}'}</p>
          </div>
          
          <p>We look forward to welcoming you soon!</p>
          <p>Best regards,<br>The Gesco Team</p>
        </div>
      `
    },
    booking_reminder: {
      subject: "Your stay is coming up soon! 🗓️",
      content: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; color: #333;">
          <h2 style="color: #4f46e5;">Your Stay is Almost Here!</h2>
          <p>Hello {customer_name},</p>
          <p>Just a friendly reminder that your stay at <strong>{property_name}</strong> is coming up in 2 days!</p>
          
          <div style="background-color: #f9fafb; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <h3 style="margin-top: 0;">Booking Details:</h3>
            <p><strong>Check-in:</strong> {check_in_date}</p>
            <p><strong>Check-out:</strong> {check_out_date}</p>
            <p><strong>Address:</strong> {property_address}</p>
          </div>
          
          <p>If you need any last-minute assistance, please don't hesitate to contact us!</p>
          <p>Safe travels,<br>The Gesco Team</p>
        </div>
      `
    },
    listing_approved: {
      subject: "Your listing has been approved! ✅",
      content: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; color: #333;">
          <h2 style="color: #4f46e5;">Listing Approved</h2>
          <p>Congratulations {owner_name}!</p>
          <p>We're happy to inform you that your listing <strong>{listing_name}</strong> has been approved and is now live on our platform.</p>
          
          <div style="background-color: #f9fafb; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <h3 style="margin-top: 0;">Next Steps:</h3>
            <ul>
              <li>Manage your listing's availability</li>
              <li>Set up instant booking if you haven't already</li>
              <li>Complete your host profile to attract more guests</li>
            </ul>
          </div>
          
          <p>Thank you for listing with us!</p>
          <p>Best regards,<br>The Gesco Team</p>
        </div>
      `
    },
    welcome_email: {
      subject: "Welcome to GescoStay! 👋",
      content: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; color: #333;">
          <h2 style="color: #4f46e5;">Welcome to GescoStay!</h2>
          <p>Hi {user_name},</p>
          <p>Thank you for joining our community! We're thrilled to have you with us.</p>
          
          <div style="background-color: #f9fafb; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <h3 style="margin-top: 0;">Getting Started:</h3>
            <ul>
              <li>Complete your profile information</li>
              <li>Browse available properties and cars</li>
              <li>Save your favorite listings</li>
              <li>Make your first booking!</li>
            </ul>
          </div>
          
          <p>If you have any questions, our support team is ready to assist you.</p>
          <p>Happy travels!<br>The Gesco Team</p>
        </div>
      `
    }
  };

  const handleSendTest = () => {
    toast.success("Test email sent successfully!");
  };

  const handlePreview = () => {
    setPreviewOpen(true);
  };

  const selectedTemplateName = {
    booking_confirmation: "Booking Confirmation",
    booking_reminder: "Booking Reminder",
    listing_approved: "Listing Approved",
    welcome_email: "Welcome Email"
  }[selectedTemplate];

  const currentTemplate = emailTemplates[selectedTemplate as keyof typeof emailTemplates];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Mail className="h-5 w-5" />
          Email Templates
        </CardTitle>
        <CardDescription>
          Preview and test email notifications sent to users
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col md:flex-row gap-4 items-start md:items-center mb-4">
          <Select value={selectedTemplate} onValueChange={setSelectedTemplate}>
            <SelectTrigger className="w-[240px]">
              <SelectValue placeholder="Select email template" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="booking_confirmation">Booking Confirmation</SelectItem>
              <SelectItem value="booking_reminder">Booking Reminder</SelectItem>
              <SelectItem value="listing_approved">Listing Approved</SelectItem>
              <SelectItem value="welcome_email">Welcome Email</SelectItem>
            </SelectContent>
          </Select>

          <div className="flex gap-2">
            <Button variant="outline" onClick={handlePreview} className="flex items-center gap-2">
              <Eye className="h-4 w-4" />
              Preview
            </Button>
            <Button onClick={handleSendTest} className="flex items-center gap-2">
              <Send className="h-4 w-4" />
              Send Test
            </Button>
          </div>
        </div>

        <div className="bg-gray-50 p-4 rounded-md">
          <div className="flex justify-between items-center mb-2">
            <p className="font-medium">Subject:</p>
            <p className="text-gray-600">{currentTemplate.subject}</p>
          </div>
          <div className="text-sm text-gray-500">
            <p>This template contains the following placeholders:</p>
            <div className="flex flex-wrap gap-2 mt-2">
              {currentTemplate.content.match(/\{([^}]+)\}/g)?.map((placeholder, index) => (
                <div key={index} className="bg-gray-200 px-2 py-1 rounded text-xs">
                  {placeholder}
                </div>
              ))}
            </div>
          </div>
        </div>

        <Dialog open={previewOpen} onOpenChange={setPreviewOpen}>
          <DialogContent className="max-w-[700px]">
            <DialogHeader>
              <DialogTitle>{selectedTemplateName} Preview</DialogTitle>
            </DialogHeader>
            
            <div className="border rounded-md p-2">
              <div className="bg-gray-100 p-2 mb-2 rounded">
                <p className="text-sm"><strong>Subject:</strong> {currentTemplate.subject}</p>
              </div>
              <div 
                className="overflow-auto max-h-[400px]" 
                dangerouslySetInnerHTML={{ __html: currentTemplate.content }}
              />
            </div>
            
            <DialogFooter>
              <Button variant="outline" onClick={() => setPreviewOpen(false)}>
                Close
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
};

export default EmailPreview;
