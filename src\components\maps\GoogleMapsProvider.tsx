import React, { createContext, useContext, ReactNode } from 'react';
import { Wrapper, Status } from '@googlemaps/react-wrapper';
import { GOOGLE_MAPS_CONFIG, validateApiKey } from '@/lib/google-maps';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertTriangle } from 'lucide-react';

interface GoogleMapsContextType {
  isLoaded: boolean;
}

const GoogleMapsContext = createContext<GoogleMapsContextType>({
  isLoaded: false,
});

export const useGoogleMaps = () => {
  const context = useContext(GoogleMapsContext);
  if (!context) {
    throw new Error('useGoogleMaps must be used within a GoogleMapsProvider');
  }
  return context;
};

interface GoogleMapsProviderProps {
  children: ReactNode;
}

// Loading component
const MapLoadingComponent = () => (
  <div className="flex items-center justify-center h-64 bg-gray-100 rounded-lg">
    <div className="text-center">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-accent mx-auto mb-2"></div>
      <p className="text-sm text-gray-600">Loading Google Maps...</p>
    </div>
  </div>
);

// Error component
const MapErrorComponent = ({ status }: { status: Status }) => {
  let errorMessage = 'Failed to load Google Maps';
  let suggestion = 'Please try again later.';

  switch (status) {
    case Status.FAILURE:
      errorMessage = 'Google Maps failed to load';
      suggestion = 'Please check your internet connection and try again.';
      break;
    case Status.LOADING:
      errorMessage = 'Google Maps is taking longer than expected to load';
      suggestion = 'Please wait or refresh the page.';
      break;
    default:
      errorMessage = `Google Maps error: ${status}`;
      suggestion = 'Please contact support if this issue persists.';
  }

  return (
    <div className="h-64 flex items-center justify-center">
      <Alert className="max-w-md">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          <div className="space-y-2">
            <p className="font-medium">{errorMessage}</p>
            <p className="text-sm text-gray-600">{suggestion}</p>
          </div>
        </AlertDescription>
      </Alert>
    </div>
  );
};

// API Key validation component
const ApiKeyError = () => (
  <div className="h-64 flex items-center justify-center">
    <Alert className="max-w-md border-red-200 bg-red-50">
      <AlertTriangle className="h-4 w-4 text-red-600" />
      <AlertDescription>
        <div className="space-y-2">
          <p className="font-medium text-red-800">Google Maps API Key Required</p>
          <p className="text-sm text-red-700">
            Please add your Google Maps API key to the environment variables.
          </p>
          <p className="text-xs text-red-600">
            Set VITE_GOOGLE_MAPS_API_KEY in your .env file
          </p>
        </div>
      </AlertDescription>
    </Alert>
  </div>
);

export const GoogleMapsProvider: React.FC<GoogleMapsProviderProps> = ({ children }) => {
  // Validate API key first
  if (!validateApiKey()) {
    return <ApiKeyError />;
  }

  const render = (status: Status) => {
    switch (status) {
      case Status.LOADING:
        return <MapLoadingComponent />;
      case Status.FAILURE:
        return <MapErrorComponent status={status} />;
      case Status.SUCCESS:
        return (
          <GoogleMapsContext.Provider value={{ isLoaded: true }}>
            {children}
          </GoogleMapsContext.Provider>
        );
      default:
        return <MapErrorComponent status={status} />;
    }
  };

  return (
    <Wrapper
      apiKey={GOOGLE_MAPS_CONFIG.apiKey}
      libraries={GOOGLE_MAPS_CONFIG.libraries}
      version={GOOGLE_MAPS_CONFIG.version}
      region={GOOGLE_MAPS_CONFIG.region}
      language={GOOGLE_MAPS_CONFIG.language}
      render={render}
    />
  );
};
