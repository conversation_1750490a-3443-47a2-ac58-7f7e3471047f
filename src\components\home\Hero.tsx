import { But<PERSON> } from "@/components/ui/button";
import { Search, MapPin, Calendar, Users } from "lucide-react";
import { useState, useEffect } from "react";
import { Link } from "react-router-dom";

const Hero = () => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  const heroImages = [
    {
      url: "/images/hero-bg-1.jpg",
      title: "Luxury Beach Resorts",
      description: "Experience pristine beaches and world-class hospitality",
    },
    {
      url: "/images/hero-bg-2.jpg",
      title: "Cultural Heritage Sites",
      description:
        "Discover the rich history, diverse cultures, and breathtaking landscapes of Africa.",
    },
    {
      url: "/images/hero-bg-3.jpg",
      title: "Modern City Living",
      description: "Comfortable accommodations in bustling urban centers",
    },
  ];

  // Auto-rotate background images
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentImageIndex((prevIndex) => (prevIndex + 1) % heroImages.length);
    }, 6000); // Change image every 6 seconds

    return () => clearInterval(interval);
  }, [heroImages.length]);

  return (
    <div className="relative min-h-screen bg-white overflow-hidden">
      {/* Split Screen Layout */}
      <div className="grid lg:grid-cols-2 min-h-screen">
        {/* Left Side - Content */}
        <div className="flex flex-col justify-center px-6 lg:px-12 xl:px-16 py-12 bg-gradient-to-br from-warm-tan/10 via-white to-warm-tan/20 relative">
          {/* Decorative elements */}
          <div className="absolute top-10 right-10 w-32 h-32 bg-accent/10 rounded-full blur-2xl"></div>
          <div className="absolute bottom-20 left-10 w-24 h-24 bg-secondary/10 rounded-full blur-xl"></div>

          <div className="max-w-xl mx-auto lg:mx-0 relative z-10">
            {/* Badge */}
            <div className="mb-6">
              <span className="inline-flex items-center px-4 py-2 bg-accent/10 text-accent rounded-full text-sm font-medium">
                Gescostay - The Spirit of African Hospitality. Book, Stay,
                Belong
              </span>
            </div>

            {/* Main Heading */}
            <h1 className="text-4xl lg:text-5xl xl:text-6xl font-bold text-dark-brown mb-6 leading-tight">
              Welcome to{" "}
              <span className="bg-gradient-to-r from-accent via-secondary to-accent bg-clip-text text-transparent">
                Gescostay
              </span>
            </h1>

            {/* Subtitle */}
            <p className="text-xl lg:text-2xl text-taupe-brown mb-8 leading-relaxed">
              Africa's Gateway to Trustworthy Travel & Stays
            </p>

            {/* Description */}
            <p className="text-lg text-gray-600 mb-8 leading-relaxed">
              Discover comfort, connect with culture, and book with confidence.
              Gescostay is Africa's next-generation booking platform designed
              for locals, the diaspora, and global travelers.
            </p>

            {/* Trust Indicators */}
            <div className="flex flex-wrap gap-4 mb-8">
              <div className="flex items-center space-x-2 bg-white px-4 py-2 rounded-full shadow-sm border">
                <span className="text-green-500 text-sm">✓</span>
                <span className="text-sm font-medium text-gray-700">
                  Verified Hosts
                </span>
              </div>
              <div className="flex items-center space-x-2 bg-white px-4 py-2 rounded-full shadow-sm border">
                <span className="text-green-500 text-sm">✓</span>
                <span className="text-sm font-medium text-gray-700">
                  Secure Payments
                </span>
              </div>
              <div className="flex items-center space-x-2 bg-white px-4 py-2 rounded-full shadow-sm border">
                <span className="text-green-500 text-sm">✓</span>
                <span className="text-sm font-medium text-gray-700">
                  Secure Environment
                </span>
              </div>
              <div className="flex items-center space-x-2 bg-white px-4 py-2 rounded-full shadow-sm border">
                <span className="text-green-500 text-sm">✓</span>
                <span className="text-sm font-medium text-gray-700">
                  24/7 Support
                </span>
              </div>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Button
                asChild
                className="bg-gradient-to-r from-accent to-secondary hover:from-accent/90 hover:to-secondary/90 text-white px-8 py-4 text-lg font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"
              >
                <Link to="/listings">Start Exploring</Link>
              </Button>
              <Button
                asChild
                variant="outline"
                className="border-2 border-accent text-accent hover:bg-accent hover:text-white px-8 py-4 text-lg font-semibold rounded-xl transition-all duration-300"
              >
                <Link to="/about">Learn More</Link>
              </Button>
            </div>
          </div>
        </div>

        {/* Right Side - Image Carousel */}
        <div className="relative min-h-[50vh] md:min-h-[70vh] lg:min-h-screen">
          {heroImages.map((image, index) => (
            <div
              key={index}
              className={`absolute inset-0 transition-opacity duration-1000 ${
                index === currentImageIndex ? "opacity-100" : "opacity-0"
              }`}
            >
              <img
                src={image.url}
                alt={image.title}
                className="w-full h-full object-cover"
              />
              {/* Stronger overlay for better text contrast */}
              <div className="absolute inset-0 bg-black/40 lg:bg-black/20"></div>

              {/* Image Info */}
              <div className="absolute bottom-4 left-4 right-4 lg:bottom-8 lg:left-8 lg:right-8 text-white">
                <h3 className="text-lg lg:text-2xl font-bold mb-1 lg:mb-2 drop-shadow-lg">
                  {image.title}
                </h3>
                <p className="text-sm lg:text-lg opacity-90 drop-shadow-md">
                  {image.description}
                </p>
              </div>
            </div>
          ))}

          {/* Image Navigation */}
          <div className="absolute bottom-8 right-8 flex space-x-2 z-20">
            {heroImages.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentImageIndex(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentImageIndex
                    ? "bg-white scale-125"
                    : "bg-white/50 hover:bg-white/75"
                }`}
              />
            ))}
          </div>
        </div>
      </div>

      {/* Search Section - Hidden for now */}
      <div className="bg-white py-16 border-t hidden">
        <div className="container mx-auto px-6">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                Start Your Journey
              </h2>
              <p className="text-xl text-gray-600">
                Find your perfect stay in Africa
              </p>
            </div>

            <div className="bg-white rounded-2xl shadow-xl border p-8">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="space-y-2">
                  <label className="flex items-center text-sm font-semibold text-gray-700 mb-2">
                    <MapPin size={16} className="mr-2 text-accent" />
                    Where to?
                  </label>
                  <input
                    type="text"
                    placeholder="Enter destination..."
                    className="w-full p-4 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-accent focus:border-transparent transition-all duration-300 text-gray-800 placeholder-gray-400"
                  />
                </div>

                <div className="space-y-2">
                  <label className="flex items-center text-sm font-semibold text-gray-700 mb-2">
                    <Calendar size={16} className="mr-2 text-accent" />
                    Check-in
                  </label>
                  <input
                    type="date"
                    className="w-full p-4 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-accent focus:border-transparent transition-all duration-300 text-gray-800"
                  />
                </div>

                <div className="space-y-2">
                  <label className="flex items-center text-sm font-semibold text-gray-700 mb-2">
                    <Calendar size={16} className="mr-2 text-accent" />
                    Check-out
                  </label>
                  <input
                    type="date"
                    className="w-full p-4 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-accent focus:border-transparent transition-all duration-300 text-gray-800"
                  />
                </div>

                <div className="space-y-2">
                  <label className="flex items-center text-sm font-semibold text-gray-700 mb-2">
                    <Users size={16} className="mr-2 text-accent" />
                    Guests
                  </label>
                  <select className="w-full p-4 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-accent focus:border-transparent transition-all duration-300 text-gray-800">
                    <option>1 Guest</option>
                    <option>2 Guests</option>
                    <option>3 Guests</option>
                    <option>4+ Guests</option>
                  </select>
                </div>
              </div>

              <div className="mt-8 flex flex-col sm:flex-row justify-center gap-4">
                <Button
                  asChild
                  className="bg-gradient-to-r from-accent to-secondary hover:from-accent/90 hover:to-secondary/90 text-white px-12 py-4 text-lg font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 flex items-center gap-3 group"
                >
                  <Link to="/listings">
                    <Search
                      size={20}
                      className="group-hover:rotate-12 transition-transform duration-300"
                    />
                    <span>Search Properties</span>
                  </Link>
                </Button>
                <Button
                  asChild
                  variant="outline"
                  className="border-2 border-secondary text-secondary hover:bg-secondary hover:text-white px-8 py-4 text-lg font-semibold rounded-xl transition-all duration-300"
                >
                  <Link to="/cars">Browse Car Rentals</Link>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Hero;
