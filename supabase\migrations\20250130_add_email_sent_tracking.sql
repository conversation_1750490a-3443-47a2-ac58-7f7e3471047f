-- Add email_sent tracking to prevent duplicate confirmation emails

-- Add email_sent field to bookings table
ALTER TABLE public.bookings 
ADD COLUMN IF NOT EXISTS email_sent BOOLEAN DEFAULT FALSE;

-- Add email_sent field to car_bookings table  
ALTER TABLE public.car_bookings 
ADD COLUMN IF NOT EXISTS email_sent BOOLEAN DEFAULT FALSE;

-- Create indexes for email_sent lookups
CREATE INDEX IF NOT EXISTS idx_bookings_email_sent ON public.bookings(email_sent);
CREATE INDEX IF NOT EXISTS idx_car_bookings_email_sent ON public.car_bookings(email_sent);

-- Add comments for documentation
COMMENT ON COLUMN public.bookings.email_sent IS 'Tracks whether confirmation email has been sent for this booking';
COMMENT ON COLUMN public.car_bookings.email_sent IS 'Tracks whether confirmation email has been sent for this car booking';
