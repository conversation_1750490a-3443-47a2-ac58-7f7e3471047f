// Test script to check admin listings functionality
// Run this in the browser console on the admin listings page

console.log("=== Admin Listings Debug Test ===");

// Test 1: Check if we can fetch listings directly
async function testDirectFetch() {
  console.log("\n1. Testing direct Supabase fetch...");
  
  try {
    // Import supabase (this should work if you're on the admin page)
    const { supabase } = await import('/src/integrations/supabase/client.ts');
    
    // Test properties fetch
    const { data: properties, error: propError } = await supabase
      .from('properties')
      .select('id, title, status, updated_at')
      .limit(5);
    
    if (propError) {
      console.error("Properties fetch error:", propError);
    } else {
      console.log("Properties found:", properties);
    }
    
    // Test hotels fetch
    const { data: hotels, error: hotelError } = await supabase
      .from('hotels')
      .select('id, title, status, updated_at')
      .limit(5);
    
    if (hotelError) {
      console.error("Hotels fetch error:", hotelError);
    } else {
      console.log("Hotels found:", hotels);
    }
    
    // Test cars fetch
    const { data: cars, error: carError } = await supabase
      .from('cars')
      .select('id, title, status, updated_at')
      .limit(5);
    
    if (carError) {
      console.error("Cars fetch error:", carError);
    } else {
      console.log("Cars found:", cars);
    }
    
  } catch (error) {
    console.error("Error in direct fetch test:", error);
  }
}

// Test 2: Check status update functionality
async function testStatusUpdate(listingId, listingType = 'properties') {
  console.log(`\n2. Testing status update for ${listingType} ID: ${listingId}...`);
  
  try {
    const { supabase } = await import('/src/integrations/supabase/client.ts');
    
    // First, get current status
    const { data: currentData, error: fetchError } = await supabase
      .from(listingType)
      .select('id, status, updated_at')
      .eq('id', listingId)
      .single();
    
    if (fetchError) {
      console.error("Error fetching current status:", fetchError);
      return;
    }
    
    console.log("Current status:", currentData);
    
    // Update to approved
    const { data: updateData, error: updateError } = await supabase
      .from(listingType)
      .update({ 
        status: 'approved', 
        updated_at: new Date().toISOString() 
      })
      .eq('id', listingId)
      .select();
    
    if (updateError) {
      console.error("Error updating status:", updateError);
    } else {
      console.log("Update successful:", updateData);
    }
    
    // Verify the update
    const { data: verifyData, error: verifyError } = await supabase
      .from(listingType)
      .select('id, status, updated_at')
      .eq('id', listingId)
      .single();
    
    if (verifyError) {
      console.error("Error verifying update:", verifyError);
    } else {
      console.log("Verified status:", verifyData);
    }
    
  } catch (error) {
    console.error("Error in status update test:", error);
  }
}

// Test 3: Check data types
async function testDataTypes() {
  console.log("\n3. Testing data types...");
  
  try {
    const { supabase } = await import('/src/integrations/supabase/client.ts');
    
    const { data: sample, error } = await supabase
      .from('properties')
      .select('id, title, status')
      .limit(1)
      .single();
    
    if (error) {
      console.error("Error fetching sample:", error);
    } else {
      console.log("Sample data:", sample);
      console.log("ID type:", typeof sample.id, "Value:", sample.id);
      console.log("Status type:", typeof sample.status, "Value:", sample.status);
    }
    
  } catch (error) {
    console.error("Error in data types test:", error);
  }
}

// Run all tests
async function runAllTests() {
  await testDirectFetch();
  await testDataTypes();
  
  // Uncomment and provide a real listing ID to test status updates
  // await testStatusUpdate(123456, 'properties');
  
  console.log("\n=== Tests Complete ===");
  console.log("Check the console output above for any errors.");
  console.log("If you see data, the connection is working.");
  console.log("To test status updates, uncomment the testStatusUpdate line and provide a real listing ID.");
}

// Auto-run tests
runAllTests();

// Export functions for manual testing
window.testAdminListings = {
  testDirectFetch,
  testStatusUpdate,
  testDataTypes,
  runAllTests
};

console.log("Test functions available as window.testAdminListings");
