import { useState } from "react";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Plus, X } from "lucide-react";

interface HotelAmenitiesSelectorProps {
  selectedAmenities: string[];
  onAmenitiesChange: (amenities: string[]) => void;
}

const HOTEL_AMENITIES = [
  "Free Wi-Fi",
  "Swimming Pool",
  "Fitness Center",
  "Spa & Wellness",
  "Restaurant",
  "Bar/Lounge",
  "Room Service",
  "Concierge",
  "Business Center",
  "Conference Rooms",
  "Parking",
  "Valet Parking",
  "Airport Shuttle",
  "Laundry Service",
  "Dry Cleaning",
  "24-Hour Front Desk",
  "Multilingual Staff",
  "Currency Exchange",
  "Tour Desk",
  "Luggage Storage",
  "Safe Deposit Box",
  "ATM/Banking",
  "Gift Shop",
  "Elevator",
  "Wheelchair Accessible",
  "Pet Friendly",
  "Non-Smoking Rooms",
  "Family Rooms",
  "Connecting Rooms",
  "Balcony/Terrace",
  "Garden",
  "Rooftop",
  "Beach Access",
  "Ocean View",
  "City View",
  "Mountain View",
  "Air Conditioning",
  "Heating",
  "Soundproof Rooms",
  "Minibar",
  "Coffee/Tea Maker",
  "Safe",
  "Hair Dryer",
  "Bathrobe",
  "Slippers",
  "Toiletries",
  "Daily Housekeeping",
  "Turn-down Service",
  "Wake-up Service",
  "Babysitting",
  "Kids Club",
  "Playground",
  "Game Room",
  "Library",
  "Chapel",
  "Casino",
  "Nightclub",
  "Live Entertainment",
  "Karaoke",
  "Tennis Court",
  "Golf Course",
  "Water Sports",
  "Diving",
  "Fishing",
  "Bicycle Rental",
  "Car Rental",
  "Electric Vehicle Charging"
];

const HotelAmenitiesSelector = ({ selectedAmenities, onAmenitiesChange }: HotelAmenitiesSelectorProps) => {
  const [showOthers, setShowOthers] = useState(false);
  const [customAmenity, setCustomAmenity] = useState("");
  const [customAmenities, setCustomAmenities] = useState<string[]>([]);

  const handleAmenityToggle = (amenity: string, checked: boolean) => {
    if (checked) {
      onAmenitiesChange([...selectedAmenities, amenity]);
    } else {
      onAmenitiesChange(selectedAmenities.filter(a => a !== amenity));
    }
  };

  const addCustomAmenity = () => {
    if (customAmenity.trim() && !selectedAmenities.includes(customAmenity.trim())) {
      const newAmenity = customAmenity.trim();
      setCustomAmenities([...customAmenities, newAmenity]);
      onAmenitiesChange([...selectedAmenities, newAmenity]);
      setCustomAmenity("");
    }
  };

  const removeCustomAmenity = (amenity: string) => {
    setCustomAmenities(customAmenities.filter(a => a !== amenity));
    onAmenitiesChange(selectedAmenities.filter(a => a !== amenity));
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addCustomAmenity();
    }
  };

  return (
    <div className="space-y-4">
      <Label className="text-base font-semibold">Hotel Amenities</Label>
      
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
        {HOTEL_AMENITIES.map((amenity) => (
          <div key={amenity} className="flex items-center space-x-2">
            <Checkbox
              id={amenity}
              checked={selectedAmenities.includes(amenity)}
              onCheckedChange={(checked) => handleAmenityToggle(amenity, checked as boolean)}
            />
            <Label
              htmlFor={amenity}
              className="text-sm font-normal cursor-pointer"
            >
              {amenity}
            </Label>
          </div>
        ))}
      </div>

      <div className="flex items-center gap-2">
        <Button
          type="button"
          variant="outline"
          onClick={() => setShowOthers(!showOthers)}
          className="text-sm"
        >
          {showOthers ? "Hide" : "Add"} Custom Amenities
        </Button>
      </div>

      {showOthers && (
        <div className="space-y-3 p-4 border rounded-lg bg-gray-50">
          <Label className="text-sm font-medium">Add Custom Amenities</Label>
          <div className="flex gap-2">
            <Input
              placeholder="Enter a custom amenity..."
              value={customAmenity}
              onChange={(e) => setCustomAmenity(e.target.value)}
              onKeyPress={handleKeyPress}
              className="flex-1"
            />
            <Button
              type="button"
              onClick={addCustomAmenity}
              size="sm"
              className="bg-accent hover:bg-accent/90"
            >
              <Plus className="w-4 h-4" />
            </Button>
          </div>
          
          {customAmenities.length > 0 && (
            <div className="space-y-2">
              <Label className="text-sm font-medium">Custom Amenities:</Label>
              <div className="flex flex-wrap gap-2">
                {customAmenities.map((amenity) => (
                  <div
                    key={amenity}
                    className="flex items-center gap-1 bg-accent text-white px-2 py-1 rounded-md text-sm"
                  >
                    <span>{amenity}</span>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeCustomAmenity(amenity)}
                      className="h-auto p-0 text-white hover:bg-accent/80"
                    >
                      <X className="w-3 h-3" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {selectedAmenities.length > 0 && (
        <div className="mt-4">
          <Label className="text-sm font-medium text-gray-600">
            Selected: {selectedAmenities.length} amenities
          </Label>
        </div>
      )}
    </div>
  );
};

export default HotelAmenitiesSelector;
