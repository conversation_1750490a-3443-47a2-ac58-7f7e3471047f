
import React from 'react';
import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { LineChart } from "@/components/admin/charts";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";

const UserActivityChart = () => {
  const [period, setPeriod] = React.useState('30d');

  // Sample data
  const userActivityData = {
    labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4', 'Week 5'],
    datasets: [
      {
        label: 'New Users',
        data: [5, 10, 8, 12, 9],
        fill: false,
        borderColor: 'rgb(75, 192, 192)',
      },
      {
        label: 'Active Users',
        data: [20, 25, 30, 35, 42],
        fill: false,
        borderColor: 'rgb(54, 162, 235)',
      },
      {
        label: 'Bookings',
        data: [15, 20, 18, 25, 22],
        fill: false,
        borderColor: 'rgb(255, 159, 64)',
      }
    ]
  };

  // Calculate overall growth percentages
  const calculateGrowth = (dataset: number[]) => {
    if (dataset.length < 2) return 0;
    const firstValue = dataset[0];
    const lastValue = dataset[dataset.length - 1];
    return firstValue === 0 ? 0 : ((lastValue - firstValue) / firstValue) * 100;
  };

  const newUsersGrowth = calculateGrowth(userActivityData.datasets[0].data);
  const activeUsersGrowth = calculateGrowth(userActivityData.datasets[1].data);
  const bookingsGrowth = calculateGrowth(userActivityData.datasets[2].data);

  const renderGrowthBadge = (value: number) => {
    const isPositive = value >= 0;
    return (
      <Badge variant={isPositive ? "secondary" : "destructive"} className="ml-2">
        {isPositive ? '+' : ''}{value.toFixed(1)}%
      </Badge>
    );
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>User Activity</CardTitle>
          <CardDescription>
            Track user engagement and platform activity
          </CardDescription>
        </div>
        <Select value={period} onValueChange={setPeriod}>
          <SelectTrigger className="w-[120px]">
            <SelectValue placeholder="Select period" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="7d">Last 7 days</SelectItem>
            <SelectItem value="14d">Last 14 days</SelectItem>
            <SelectItem value="30d">Last 30 days</SelectItem>
            <SelectItem value="90d">Last 90 days</SelectItem>
          </SelectContent>
        </Select>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-3 gap-4 mb-6">
          <div className="bg-gray-50 p-3 rounded-md">
            <div className="text-sm text-gray-500">New Users</div>
            <div className="text-2xl font-bold flex items-center">
              {userActivityData.datasets[0].data.reduce((a, b) => a + b, 0)}
              {renderGrowthBadge(newUsersGrowth)}
            </div>
          </div>
          <div className="bg-gray-50 p-3 rounded-md">
            <div className="text-sm text-gray-500">Active Users</div>
            <div className="text-2xl font-bold flex items-center">
              {userActivityData.datasets[1].data[userActivityData.datasets[1].data.length - 1]}
              {renderGrowthBadge(activeUsersGrowth)}
            </div>
          </div>
          <div className="bg-gray-50 p-3 rounded-md">
            <div className="text-sm text-gray-500">Total Bookings</div>
            <div className="text-2xl font-bold flex items-center">
              {userActivityData.datasets[2].data.reduce((a, b) => a + b, 0)}
              {renderGrowthBadge(bookingsGrowth)}
            </div>
          </div>
        </div>
        
        <LineChart
          data={userActivityData}
          width={600}
          height={300}
          options={{
            maintainAspectRatio: false,
            scales: {
              y: {
                beginAtZero: true,
              }
            }
          }}
        />
      </CardContent>
    </Card>
  );
};

export default UserActivityChart;
