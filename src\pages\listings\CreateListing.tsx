
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useForm } from "react-hook-form";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/components/layout/AuthProvider";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import FeatureSelector from "@/components/features/FeatureSelector";
import { LocationPicker } from "@/components/maps/LocationPicker";
import { LocationData } from "@/lib/google-maps";

type CreateListingForm = {
  title: string;
  description: string;
  location: string;
  price: number;
  beds: number;
  baths: number;
  images: FileList;
};

type CreateListingFormData = CreateListingForm & {
  features: string[];
};

const CreateListing = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedFeatures, setSelectedFeatures] = useState<string[]>([]);
  const [selectedLocation, setSelectedLocation] = useState<LocationData | null>(null);

  const form = useForm<CreateListingForm>({
    defaultValues: {
      title: '',
      description: '',
      location: '',
      price: 0,
      beds: 1,
      baths: 1,
    },
  });

  const onSubmit = async (data: CreateListingForm) => {
    if (!user) return;
    setIsSubmitting(true);

    try {
      const imageUrls: string[] = [];

      // Upload images first
      if (data.images.length > 0) {
        for (let i = 0; i < data.images.length; i++) {
          const file = data.images[i];
          const fileExt = file.name.split('.').pop();
          const fileName = `${Math.random()}.${fileExt}`;
          const filePath = `${fileName}`;

          const { error: uploadError } = await supabase.storage
            .from('property-images')
            .upload(filePath, file);

          if (uploadError) {
            throw uploadError;
          }

          const { data: urlData } = supabase.storage
            .from('property-images')
            .getPublicUrl(filePath);

          imageUrls.push(urlData.publicUrl);
        }
      }

      // Create the property listing
      const { error } = await supabase.from('properties').insert({
        title: data.title,
        description: data.description,
        location: data.location,
        latitude: selectedLocation?.latitude || null,
        longitude: selectedLocation?.longitude || null,
        formatted_address: selectedLocation?.formatted_address || null,
        price: data.price,
        beds: data.beds,
        baths: data.baths,
        owner_id: user.id,
        images: imageUrls,
        features: selectedFeatures,
      });

      if (error) throw error;

      toast({
        title: "Success",
        description: "Your property has been listed successfully!",
      });

      navigate("/listings");
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Something went wrong. Please try again.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-white">
      <Navbar />
      <main className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold text-brown mb-2">List Your Property</h1>
        <p className="text-muted-foreground mb-8">
          Share your property with travelers and earn extra income
        </p>

        <Card className="max-w-3xl mx-auto">
          <CardHeader>
            <CardTitle>Property Details</CardTitle>
            <CardDescription>
              Provide information about your property to attract guests
            </CardDescription>
          </CardHeader>
          <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Title</FormLabel>
                    <FormControl>
                      <Input placeholder="Beachfront Villa in Kololi" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describe your property..."
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="location"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Location</FormLabel>
                    <FormControl>
                      <Input placeholder="Kololi Beach" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="space-y-2">
                <FormLabel>Property Location on Map</FormLabel>
                <LocationPicker
                  onLocationSelect={(location) => {
                    setSelectedLocation(location);
                    // Also update the text field with the formatted address
                    form.setValue('location', location.formatted_address || location.latitude + ', ' + location.longitude);
                  }}
                  initialLocation={selectedLocation || undefined}
                  height="400px"
                  showSearch={true}
                  showCurrentLocationButton={true}
                />
                {selectedLocation && (
                  <p className="text-sm text-gray-600">
                    Selected: {selectedLocation.formatted_address}
                  </p>
                )}
              </div>

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="price"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Price per night ($)</FormLabel>
                      <FormControl>
                        <Input type="number" min="0" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="beds"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Bedrooms</FormLabel>
                      <FormControl>
                        <Input type="number" min="1" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="baths"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Bathrooms</FormLabel>
                      <FormControl>
                        <Input type="number" min="1" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="images"
                  render={({ field: { value, onChange, ...field } }) => (
                    <FormItem>
                      <FormLabel>Images</FormLabel>
                      <FormControl>
                        <Input
                          type="file"
                          multiple
                          accept="image/*"
                          onChange={(e) => onChange(e.target.files)}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <Separator />

              <FeatureSelector
                type="property"
                selectedFeatures={selectedFeatures}
                onFeaturesChange={setSelectedFeatures}
              />

              <Button
                type="submit"
                className="w-full bg-accent hover:bg-accent/90"
                disabled={isSubmitting}
              >
                {isSubmitting ? "Creating listing..." : "Create Listing"}
              </Button>
            </form>
          </Form>
          </CardContent>
        </Card>
      </main>
      <Footer />
    </div>
  );
};

export default CreateListing;
