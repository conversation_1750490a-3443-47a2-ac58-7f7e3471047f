import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/components/layout/AuthProvider';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

interface VerificationRedirectProps {
  children: React.ReactNode;
}

export const VerificationRedirect = ({ children }: VerificationRedirectProps) => {
  const { user, isVerified } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();

  useEffect(() => {
    const checkVerificationStatus = async () => {
      if (!user) return;

      // If user is already verified, continue
      if (isVerified()) return;

      try {
        // Fetch user's primary login method from profile
        const { data: profile, error } = await supabase
          .from('profiles')
          .select('primary_login_method, email, phone_number')
          .eq('id', user.id)
          .single();

        if (error) {
          console.error('Error fetching user profile:', error);
          return;
        }

        const primaryMethod = profile?.primary_login_method || 'email';

        // Check if the primary method is verified
        const isEmailPrimary = primaryMethod === 'email';
        const isPhonePrimary = primaryMethod === 'phone';

        const emailVerified = !!user.email_confirmed_at;
        const phoneVerified = !!user.phone_confirmed_at;

        // If primary method is not verified, redirect to verification
        if (isEmailPrimary && !emailVerified) {
          toast({
            title: "Email verification required",
            description: "Please verify your email address to continue.",
            variant: "destructive",
          });
          
          navigate('/auth/verify-otp', {
            state: {
              email: profile.email || user.email,
              method: 'email',
              timestamp: Date.now(),
            },
          });
        } else if (isPhonePrimary && !phoneVerified) {
          toast({
            title: "Phone verification required",
            description: "Please verify your phone number to continue.",
            variant: "destructive",
          });
          
          navigate('/auth/verify-otp', {
            state: {
              phoneNumber: profile.phone_number || user.phone,
              method: 'phone',
              timestamp: Date.now(),
            },
          });
        }
      } catch (error) {
        console.error('Error checking verification status:', error);
      }
    };

    checkVerificationStatus();
  }, [user, isVerified, navigate, toast]);

  // If user is verified or not logged in, render children
  if (!user || isVerified()) {
    return <>{children}</>;
  }

  // If user is not verified, show loading or verification message
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Checking verification status...</p>
      </div>
    </div>
  );
};
