import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.45.0";

const SUPABASE_URL = Deno.env.get("SUPABASE_URL") || "";
const SUPABASE_SERVICE_ROLE_KEY =
  Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") || "";
const AWS_ACCESS_KEY_ID = Deno.env.get("AWS_ACCESS_KEY_ID") || "";
const AWS_SECRET_ACCESS_KEY = Deno.env.get("AWS_SECRET_ACCESS_KEY") || "";
const AWS_REGION = Deno.env.get("AWS_REGION") || "eu-west-2";

// Email addresses for different purposes
const EMAIL_ADDRESSES = {
  booking: Deno.env.get("SES_BOOKING_EMAIL") || "<EMAIL>",
  noreply: Deno.env.get("SES_NOREPLY_EMAIL") || "<EMAIL>",
  support: Deno.env.get("SES_SUPPORT_EMAIL") || "<EMAIL>",
  notifications:
    Deno.env.get("SES_NOTIFICATIONS_EMAIL") || "<EMAIL>",
};

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
};

interface EmailPayload {
  bookingId: string;
  type?: string; // "confirmation" | "reminder" | "checkout_reminder" | "status_update"
  status?: string;
}

// Helper function to get appropriate email address based on email type
function getFromEmail(emailType: string): string {
  switch (emailType) {
    case "confirmation":
    case "status_update":
      return EMAIL_ADDRESSES.booking;
    case "reminder":
    case "checkout_reminder":
      return EMAIL_ADDRESSES.notifications;
    default:
      return EMAIL_ADDRESSES.noreply;
  }
}

// AWS SES helper function
async function sendEmailWithSES(
  to: string,
  subject: string,
  htmlBody: string,
  fromEmail: string
) {
  const timestamp = new Date().toISOString().replace(/[:\-]|\.\d{3}/g, "");
  const date = timestamp.substr(0, 8);

  const payload = JSON.stringify({
    Action: "SendEmail",
    Version: "2010-12-01",
    Source: fromEmail,
    "Destination.ToAddresses.member.1": to,
    "Message.Subject.Data": subject,
    "Message.Body.Html.Data": htmlBody,
  });

  // Create AWS signature
  const algorithm = "AWS4-HMAC-SHA256";
  const service = "ses";
  const host = `${service}.${AWS_REGION}.amazonaws.com`;
  const amzDate = timestamp;
  const dateStamp = date;

  const canonicalUri = "/";
  const canonicalQuerystring = "";
  const canonicalHeaders = `host:${host}\nx-amz-date:${amzDate}\n`;
  const signedHeaders = "host;x-amz-date";
  const payloadHash = await crypto.subtle
    .digest("SHA-256", new TextEncoder().encode(payload))
    .then((buffer) =>
      Array.from(new Uint8Array(buffer))
        .map((b) => b.toString(16).padStart(2, "0"))
        .join("")
    );

  const canonicalRequest = `POST\n${canonicalUri}\n${canonicalQuerystring}\n${canonicalHeaders}\n${signedHeaders}\n${payloadHash}`;

  const credentialScope = `${dateStamp}/${AWS_REGION}/${service}/aws4_request`;
  const stringToSign = `${algorithm}\n${amzDate}\n${credentialScope}\n${await crypto.subtle
    .digest("SHA-256", new TextEncoder().encode(canonicalRequest))
    .then((buffer) =>
      Array.from(new Uint8Array(buffer))
        .map((b) => b.toString(16).padStart(2, "0"))
        .join("")
    )}`;

  const signingKey = await getSignatureKey(
    AWS_SECRET_ACCESS_KEY,
    dateStamp,
    AWS_REGION,
    service
  );
  const signature = await crypto.subtle
    .sign("HMAC", signingKey, new TextEncoder().encode(stringToSign))
    .then((buffer) =>
      Array.from(new Uint8Array(buffer))
        .map((b) => b.toString(16).padStart(2, "0"))
        .join("")
    );

  const authorizationHeader = `${algorithm} Credential=${AWS_ACCESS_KEY_ID}/${credentialScope}, SignedHeaders=${signedHeaders}, Signature=${signature}`;

  const response = await fetch(`https://${host}/`, {
    method: "POST",
    headers: {
      Authorization: authorizationHeader,
      "Content-Type": "application/x-www-form-urlencoded; charset=utf-8",
      "X-Amz-Date": amzDate,
    },
    body: new URLSearchParams(JSON.parse(payload)).toString(),
  });

  return response;
}

async function getSignatureKey(
  key: string,
  dateStamp: string,
  regionName: string,
  serviceName: string
) {
  const kDate = await crypto.subtle
    .importKey(
      "raw",
      new TextEncoder().encode("AWS4" + key),
      { name: "HMAC", hash: "SHA-256" },
      false,
      ["sign"]
    )
    .then((k) =>
      crypto.subtle.sign("HMAC", k, new TextEncoder().encode(dateStamp))
    );
  const kRegion = await crypto.subtle
    .importKey("raw", kDate, { name: "HMAC", hash: "SHA-256" }, false, ["sign"])
    .then((k) =>
      crypto.subtle.sign("HMAC", k, new TextEncoder().encode(regionName))
    );
  const kService = await crypto.subtle
    .importKey("raw", kRegion, { name: "HMAC", hash: "SHA-256" }, false, [
      "sign",
    ])
    .then((k) =>
      crypto.subtle.sign("HMAC", k, new TextEncoder().encode(serviceName))
    );
  const kSigning = await crypto.subtle
    .importKey("raw", kService, { name: "HMAC", hash: "SHA-256" }, false, [
      "sign",
    ])
    .then((k) =>
      crypto.subtle.sign("HMAC", k, new TextEncoder().encode("aws4_request"))
    );

  return crypto.subtle.importKey(
    "raw",
    kSigning,
    { name: "HMAC", hash: "SHA-256" },
    false,
    ["sign"]
  );
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const payload: EmailPayload = await req.json();
    const { bookingId, type = "confirmation", status } = payload;

    // Create Supabase client with admin privileges
    const supabaseAdmin = createClient(
      SUPABASE_URL,
      SUPABASE_SERVICE_ROLE_KEY,
      {
        auth: { persistSession: false },
      }
    );

    // Get booking details
    const { data: booking, error: bookingError } = await supabaseAdmin
      .from("bookings")
      .select(
        `
        *, 
        properties(*),
        profiles:user_id(*)
      `
      )
      .eq("id", bookingId)
      .single();

    if (bookingError || !booking) {
      throw new Error("Booking not found");
    }

    // Get property owner details
    const { data: owner, error: ownerError } = await supabaseAdmin
      .from("profiles")
      .select("*")
      .eq("id", booking.properties.owner_id)
      .single();

    if (ownerError) {
      console.error("Could not fetch owner details:", ownerError);
    }

    // Get user preferences for email notifications
    const { data: guestPrefs } = await supabaseAdmin
      .from("user_preferences")
      .select("*")
      .eq("user_id", booking.user_id)
      .maybeSingle();

    const { data: ownerPrefs } = await supabaseAdmin
      .from("user_preferences")
      .select("*")
      .eq("user_id", booking.properties.owner_id)
      .maybeSingle();

    // Check if user wants to receive this type of email
    const shouldSendToGuest =
      !guestPrefs ||
      (type === "confirmation" && guestPrefs.booking_confirmations) ||
      (type === "status_update" && guestPrefs.status_updates) ||
      (type === "reminder" && guestPrefs.reminders);

    const shouldSendToOwner =
      !ownerPrefs ||
      (type === "confirmation" && ownerPrefs.booking_confirmations) ||
      (type === "status_update" && ownerPrefs.status_updates) ||
      (type === "reminder" && ownerPrefs.reminders);

    // Format dates
    const checkIn = new Date(booking.check_in).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
    const checkOut = new Date(booking.check_out).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });

    // Email content based on type
    let guestSubject = "Booking Confirmation";
    let ownerSubject = "New Booking Notification";

    if (type === "reminder") {
      guestSubject = "Upcoming Booking Reminder";
      ownerSubject = "Upcoming Guest Arrival Reminder";
    } else if (type === "checkout_reminder") {
      guestSubject = "Checkout Reminder - 2 Hours Notice";
      ownerSubject = "Guest Checkout Reminder";
    } else if (type === "status_update" && status) {
      guestSubject = `Booking Status: ${
        status.charAt(0).toUpperCase() + status.slice(1)
      }`;
      ownerSubject = `Booking Status Update: ${
        status.charAt(0).toUpperCase() + status.slice(1)
      }`;
    }

    // Prepare email to guest
    let guestEmailContent = "";
    if (type === "confirmation") {
      guestEmailContent = `
        <h1>Booking Confirmation</h1>
        <p>Dear ${booking.profiles.first_name || "Guest"},</p>
        <p>Your booking has been confirmed and paid for. Here are the details:</p>
        <ul>
          <li><strong>Property:</strong> ${booking.properties.title}</li>
          <li><strong>Location:</strong> ${booking.properties.location}</li>
          <li><strong>Check-in:</strong> ${checkIn}</li>
          <li><strong>Check-out:</strong> ${checkOut}</li>
          <li><strong>Total Amount:</strong> $${booking.total_price}</li>
        </ul>
        <p>We hope you enjoy your stay!</p>
        <p>Regards,<br/>GescosStay</p>
      `;
    } else if (type === "reminder") {
      // Calculate days until check-in
      const now = new Date();
      const checkInDate = new Date(booking.check_in);
      const daysUntil = Math.ceil(
        (checkInDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)
      );

      guestEmailContent = `
        <h1>Upcoming Booking Reminder</h1>
        <p>Dear ${booking.profiles.first_name || "Guest"},</p>
        <p>This is a friendly reminder that your stay at ${
          booking.properties.title
        } begins in ${daysUntil} day${daysUntil !== 1 ? "s" : ""}.</p>
        <ul>
          <li><strong>Property:</strong> ${booking.properties.title}</li>
          <li><strong>Location:</strong> ${booking.properties.location}</li>
          <li><strong>Check-in:</strong> ${checkIn}</li>
          <li><strong>Check-out:</strong> ${checkOut}</li>
        </ul>
        <p>We look forward to hosting you!</p>
        <p>Regards,<br/>GescosStay</p>
      `;
    } else if (type === "checkout_reminder") {
      guestEmailContent = `
        <h1>Checkout Reminder</h1>
        <p>Dear ${booking.profiles.first_name || "Guest"},</p>
        <p>This is a friendly reminder that your checkout time is approaching in approximately 2 hours.</p>
        <ul>
          <li><strong>Property:</strong> ${booking.properties.title}</li>
          <li><strong>Location:</strong> ${booking.properties.location}</li>
          <li><strong>Checkout Date:</strong> ${checkOut}</li>
        </ul>
        <p>Please ensure you have all your belongings and that the property is left in good condition.</p>
        <p>Thank you for staying with us!</p>
        <p>Regards,<br/>GescosStay</p>
      `;
    } else if (type === "status_update" && status) {
      guestEmailContent = `
        <h1>Booking Status Update</h1>
        <p>Dear ${booking.profiles.first_name || "Guest"},</p>
        <p>The status of your booking at ${
          booking.properties.title
        } has been updated to: <strong>${status}</strong>.</p>
        <ul>
          <li><strong>Property:</strong> ${booking.properties.title}</li>
          <li><strong>Location:</strong> ${booking.properties.location}</li>
          <li><strong>Check-in:</strong> ${checkIn}</li>
          <li><strong>Check-out:</strong> ${checkOut}</li>
        </ul>
        <p>If you have any questions about this update, please contact us.</p>
        <p>Regards,<br/>GescosStay</p>
      `;
    }

    // Prepare email to property owner
    let ownerEmailContent = "";
    if (type === "confirmation") {
      ownerEmailContent = `
        <h1>New Booking Notification</h1>
        <p>Dear ${owner?.first_name || "Owner"},</p>
        <p>You have a new confirmed booking for your property. Here are the details:</p>
        <ul>
          <li><strong>Property:</strong> ${booking.properties.title}</li>
          <li><strong>Guest Name:</strong> ${booking.profiles.first_name} ${
        booking.profiles.last_name
      }</li>
          <li><strong>Check-in:</strong> ${checkIn}</li>
          <li><strong>Check-out:</strong> ${checkOut}</li>
          <li><strong>Total Amount:</strong> $${booking.total_price}</li>
        </ul>
        <p>Please prepare for your guest's arrival.</p>
        <p>Regards,<br/>GescosStay</p>
      `;
    } else if (type === "reminder") {
      // Calculate days until check-in
      const now = new Date();
      const checkInDate = new Date(booking.check_in);
      const daysUntil = Math.ceil(
        (checkInDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)
      );

      ownerEmailContent = `
        <h1>Upcoming Guest Arrival Reminder</h1>
        <p>Dear ${owner?.first_name || "Owner"},</p>
        <p>This is a friendly reminder that your guest will be arriving at ${
          booking.properties.title
        } in ${daysUntil} day${daysUntil !== 1 ? "s" : ""}.</p>
        <ul>
          <li><strong>Property:</strong> ${booking.properties.title}</li>
          <li><strong>Guest Name:</strong> ${booking.profiles.first_name} ${
        booking.profiles.last_name
      }</li>
          <li><strong>Check-in:</strong> ${checkIn}</li>
          <li><strong>Check-out:</strong> ${checkOut}</li>
        </ul>
        <p>Please ensure everything is ready for their arrival.</p>
        <p>Regards,<br/>GescosStay</p>
      `;
    } else if (type === "status_update" && status) {
      ownerEmailContent = `
        <h1>Booking Status Update</h1>
        <p>Dear ${owner?.first_name || "Owner"},</p>
        <p>The status of a booking for your property ${
          booking.properties.title
        } has been updated to: <strong>${status}</strong>.</p>
        <ul>
          <li><strong>Property:</strong> ${booking.properties.title}</li>
          <li><strong>Guest Name:</strong> ${booking.profiles.first_name} ${
        booking.profiles.last_name
      }</li>
          <li><strong>Check-in:</strong> ${checkIn}</li>
          <li><strong>Check-out:</strong> ${checkOut}</li>
        </ul>
        <p>Regards,<br/>GescosStay</p>
      `;
    }

    const emailPromises = [];
    const fromEmail = getFromEmail(type || "confirmation");

    // Send email to guest if they want to receive this type of email
    if (shouldSendToGuest && booking.profiles.email) {
      const guestEmailPromise = sendEmailWithSES(
        booking.profiles.email,
        guestSubject,
        guestEmailContent,
        fromEmail
      );
      emailPromises.push(guestEmailPromise);
    }

    // Send email to property owner if they want to receive this type of email
    if (shouldSendToOwner && owner?.email) {
      const ownerEmailPromise = sendEmailWithSES(
        owner.email,
        ownerSubject,
        ownerEmailContent,
        fromEmail
      );
      emailPromises.push(ownerEmailPromise);
    }

    // Wait for all emails to be sent
    await Promise.all(emailPromises);

    return new Response(
      JSON.stringify({
        success: true,
        message: "Email notifications sent successfully",
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200,
      }
    );
  } catch (error) {
    console.error("Error sending email notifications:", error);
    return new Response(
      JSON.stringify({ success: false, error: error.message }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 500,
      }
    );
  }
});
