import { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/components/layout/AuthProvider";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "sonner";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import { LocationPicker } from "@/components/maps/LocationPicker";
import HotelAmenitiesSelector from "@/components/hotels/HotelAmenitiesSelector";
import RoomTypeManager from "@/components/hotels/RoomTypeManager";

const formSchema = z.object({
  title: z.string().min(1, "Title is required"),
  description: z.string().min(10, "Description must be at least 10 characters"),
  location: z.string().min(1, "Location is required"),
  check_in_time: z.string().default("15:00"),
  check_out_time: z.string().default("11:00"),
  images: z.any().optional(),
});

type FormData = z.infer<typeof formSchema>;

const EditHotel = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const { user } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedLocation, setSelectedLocation] = useState<{
    latitude: number;
    longitude: number;
    formatted_address: string;
  } | null>(null);
  const [selectedAmenities, setSelectedAmenities] = useState<string[]>([]);
  const [currentStep, setCurrentStep] = useState<"hotel" | "rooms">("hotel");
  const [existingImages, setExistingImages] = useState<string[]>([]);

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: "",
      description: "",
      location: "",
      check_in_time: "15:00",
      check_out_time: "11:00",
    },
  });

  useEffect(() => {
    if (!user || !id) {
      navigate("/auth");
      return;
    }
    fetchHotelData();
  }, [user, id, navigate]);

  const fetchHotelData = async () => {
    try {
      const { data: hotel, error } = await supabase
        .from("hotels")
        .select("*")
        .eq("id", id)
        .eq("owner_id", user?.id)
        .single();

      if (error) throw error;

      if (!hotel) {
        toast.error("Hotel not found or you don't have permission to edit it.");
        navigate("/host/listings");
        return;
      }

      // Populate form with existing data
      form.reset({
        title: hotel.title,
        description: hotel.description,
        location: hotel.location,
        check_in_time: hotel.check_in_time || "15:00",
        check_out_time: hotel.check_out_time || "11:00",
      });

      setSelectedAmenities(hotel.amenities || []);
      setExistingImages(hotel.images || []);

      if (hotel.latitude && hotel.longitude) {
        setSelectedLocation({
          latitude: hotel.latitude,
          longitude: hotel.longitude,
          formatted_address: hotel.formatted_address || hotel.location,
        });
      }
    } catch (error) {
      console.error("Error fetching hotel:", error);
      toast.error("Failed to load hotel data.");
      navigate("/host/listings");
    } finally {
      setIsLoading(false);
    }
  };

  const uploadImages = async (files: FileList): Promise<string[]> => {
    const imageUrls: string[] = [];

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const fileExt = file.name.split('.').pop();
      const fileName = `${Math.random()}.${fileExt}`;
      const filePath = `hotel-images/${fileName}`;

      const { error: uploadError } = await supabase.storage
        .from('hotel-images')
        .upload(filePath, file);

      if (uploadError) {
        throw uploadError;
      }

      const { data } = supabase.storage
        .from('hotel-images')
        .getPublicUrl(filePath);

      imageUrls.push(data.publicUrl);
    }

    return imageUrls;
  };

  const onSubmit = async (data: FormData) => {
    if (!user) return;

    setIsSubmitting(true);
    try {
      let imageUrls = existingImages;

      // Upload new images if provided
      if (data.images && data.images.length > 0) {
        const newImageUrls = await uploadImages(data.images);
        imageUrls = [...existingImages, ...newImageUrls];
      }

      // Update the hotel listing
      const { error } = await supabase
        .from("hotels")
        .update({
          title: data.title,
          description: data.description,
          location: data.location,
          latitude: selectedLocation?.latitude || null,
          longitude: selectedLocation?.longitude || null,
          formatted_address: selectedLocation?.formatted_address || null,
          check_in_time: data.check_in_time,
          check_out_time: data.check_out_time,
          images: imageUrls,
          amenities: selectedAmenities,
          updated_at: new Date().toISOString(),
        })
        .eq('id', id)
        .eq('owner_id', user.id);

      if (error) throw error;

      setCurrentStep("rooms");

      toast.success("Hotel updated successfully! You can now manage room types.");
    } catch (error) {
      console.error("Error updating hotel:", error);
      toast.error("Failed to update hotel. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-800"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />

      <main className="container mx-auto px-4 py-8">
        <Card className="max-w-4xl mx-auto">
          <CardHeader>
            <CardTitle>Edit Hotel Listing</CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs value={currentStep} onValueChange={(value) => setCurrentStep(value as "hotel" | "rooms")}>
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="hotel">Hotel Details</TabsTrigger>
                <TabsTrigger value="rooms">Room Types</TabsTrigger>
              </TabsList>

              <TabsContent value="hotel" className="mt-6">
                <Form {...form}>
                  <form
                    onSubmit={form.handleSubmit(onSubmit)}
                    className="space-y-6"
                  >
                    <FormField
                      control={form.control}
                      name="title"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Hotel Name</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Grand Hotel Banjul"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Description</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Describe your hotel, its unique features, and what makes it special..."
                              className="min-h-[100px]"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="location"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Location</FormLabel>
                          <FormControl>
                            <Input placeholder="City, Country" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div>
                      <label className="text-sm font-medium">
                        Select Location on Map
                      </label>
                      <LocationPicker
                        onLocationSelect={setSelectedLocation}
                        initialLocation={selectedLocation}
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="check_in_time"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Check-in Time</FormLabel>
                            <FormControl>
                              <Input type="time" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="check_out_time"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Check-out Time</FormLabel>
                            <FormControl>
                              <Input type="time" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div>
                      <label className="text-sm font-medium">
                        Hotel Amenities
                      </label>
                      <HotelAmenitiesSelector
                        selectedAmenities={selectedAmenities}
                        onAmenitiesChange={setSelectedAmenities}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name="images"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Add More Images (Optional)</FormLabel>
                          <FormControl>
                            <Input
                              type="file"
                              multiple
                              accept="image/*"
                              onChange={(e) => field.onChange(e.target.files)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {existingImages.length > 0 && (
                      <div>
                        <label className="text-sm font-medium">Current Images</label>
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mt-2">
                          {existingImages.map((image, index) => (
                            <img
                              key={index}
                              src={image}
                              alt={`Hotel image ${index + 1}`}
                              className="w-full h-24 object-cover rounded"
                            />
                          ))}
                        </div>
                      </div>
                    )}

                    <div className="flex gap-4">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => navigate("/host/listings")}
                        className="flex-1"
                      >
                        Cancel
                      </Button>
                      <Button type="submit" disabled={isSubmitting} className="flex-1">
                        {isSubmitting ? "Updating..." : "Update Hotel"}
                      </Button>
                    </div>
                  </form>
                </Form>
              </TabsContent>

              <TabsContent value="rooms" className="mt-6">
                {id && <RoomTypeManager hotelId={id} />}
                <div className="mt-6 flex justify-center">
                  <Button onClick={() => navigate("/host/listings")}>
                    Done - Return to Listings
                  </Button>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </main>

      <Footer />
    </div>
  );
};

export default EditHotel;
