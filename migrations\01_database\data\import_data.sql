-- Gesco Stay Data Import Script
-- This script imports all data into the new Supabase project
-- Run this script against the new project after schema creation

-- Disable triggers temporarily to avoid conflicts during import
SET session_replication_role = replica;

-- Import profiles (this should be done after auth.users are created)
\copy public.profiles FROM 'profiles.csv' WITH CSV HEADER;

-- Import properties
\copy public.properties FROM 'properties.csv' WITH CSV HEADER;

-- Import cars
\copy public.cars FROM 'cars.csv' WITH CSV HEADER;

-- Import hotels
\copy public.hotels FROM 'hotels.csv' WITH CSV HEADER;

-- Import room types
\copy public.room_types FROM 'room_types.csv' WITH CSV HEADER;

-- Import room inventory
\copy public.room_inventory FROM 'room_inventory.csv' WITH CSV HEADER;

-- Import seasonal pricing
\copy public.seasonal_pricing FROM 'seasonal_pricing.csv' WITH CSV HEADER;

-- Import bookings
\copy public.bookings FROM 'bookings.csv' WITH CSV HEADER;

-- Import car bookings
\copy public.car_bookings FROM 'car_bookings.csv' WITH CSV HEADER;

-- Import hotel bookings
\copy public.hotel_bookings FROM 'hotel_bookings.csv' WITH CSV HEADER;

-- Import room assignments
\copy public.room_assignments FROM 'room_assignments.csv' WITH CSV HEADER;

-- Import reviews
\copy public.reviews FROM 'reviews.csv' WITH CSV HEADER;

-- Import car insurance options
\copy public.car_insurance_options FROM 'car_insurance_options.csv' WITH CSV HEADER;

-- Import conversations
\copy public.conversations FROM 'conversations.csv' WITH CSV HEADER;

-- Import messages
\copy public.messages FROM 'messages.csv' WITH CSV HEADER;

-- Import host payment methods
\copy public.host_payment_methods FROM 'host_payment_methods.csv' WITH CSV HEADER;

-- Import payout requests
\copy public.payout_requests FROM 'payout_requests.csv' WITH CSV HEADER;

-- Import platform earnings
\copy public.platform_earnings FROM 'platform_earnings.csv' WITH CSV HEADER;

-- Import payment logs
\copy public.payment_logs FROM 'payment_logs.csv' WITH CSV HEADER;

-- Import admin users (passwords will need to be reset)
\copy public.admin_users FROM 'admin_users.csv' WITH CSV HEADER;

-- Import admin sessions (optional, may want to skip this)
-- \copy public.admin_sessions FROM 'admin_sessions.csv' WITH CSV HEADER;

-- Import checkout reminders sent
\copy public.checkout_reminders_sent FROM 'checkout_reminders_sent.csv' WITH CSV HEADER;

-- Re-enable triggers
SET session_replication_role = DEFAULT;

-- Update sequences to prevent conflicts
SELECT setval('public.profiles_id_seq', (SELECT MAX(id) FROM public.profiles)) WHERE EXISTS (SELECT 1 FROM public.profiles);
-- Note: Most tables use UUID primary keys, so sequence updates are not needed

-- Verify data integrity after import
SELECT 'profiles' as table_name, COUNT(*) as record_count FROM public.profiles
UNION ALL
SELECT 'properties', COUNT(*) FROM public.properties
UNION ALL
SELECT 'cars', COUNT(*) FROM public.cars
UNION ALL
SELECT 'hotels', COUNT(*) FROM public.hotels
UNION ALL
SELECT 'room_types', COUNT(*) FROM public.room_types
UNION ALL
SELECT 'room_inventory', COUNT(*) FROM public.room_inventory
UNION ALL
SELECT 'seasonal_pricing', COUNT(*) FROM public.seasonal_pricing
UNION ALL
SELECT 'bookings', COUNT(*) FROM public.bookings
UNION ALL
SELECT 'car_bookings', COUNT(*) FROM public.car_bookings
UNION ALL
SELECT 'hotel_bookings', COUNT(*) FROM public.hotel_bookings
UNION ALL
SELECT 'room_assignments', COUNT(*) FROM public.room_assignments
UNION ALL
SELECT 'reviews', COUNT(*) FROM public.reviews
UNION ALL
SELECT 'car_insurance_options', COUNT(*) FROM public.car_insurance_options
UNION ALL
SELECT 'conversations', COUNT(*) FROM public.conversations
UNION ALL
SELECT 'messages', COUNT(*) FROM public.messages
UNION ALL
SELECT 'host_payment_methods', COUNT(*) FROM public.host_payment_methods
UNION ALL
SELECT 'payout_requests', COUNT(*) FROM public.payout_requests
UNION ALL
SELECT 'platform_earnings', COUNT(*) FROM public.platform_earnings
UNION ALL
SELECT 'payment_logs', COUNT(*) FROM public.payment_logs
UNION ALL
SELECT 'admin_users', COUNT(*) FROM public.admin_users
UNION ALL
SELECT 'admin_sessions', COUNT(*) FROM public.admin_sessions
UNION ALL
SELECT 'checkout_reminders_sent', COUNT(*) FROM public.checkout_reminders_sent
ORDER BY table_name;

-- Check for referential integrity issues
SELECT 'Orphaned bookings' as issue, COUNT(*) as count
FROM public.bookings b
LEFT JOIN public.properties p ON b.property_id = p.id
WHERE b.property_id IS NOT NULL AND p.id IS NULL

UNION ALL

SELECT 'Orphaned car bookings', COUNT(*)
FROM public.car_bookings cb
LEFT JOIN public.cars c ON cb.car_id = c.id
WHERE c.id IS NULL

UNION ALL

SELECT 'Orphaned hotel bookings', COUNT(*)
FROM public.hotel_bookings hb
LEFT JOIN public.hotels h ON hb.hotel_id = h.id
WHERE h.id IS NULL

UNION ALL

SELECT 'Orphaned reviews', COUNT(*)
FROM public.reviews r
LEFT JOIN public.properties p ON r.property_id = p.id
LEFT JOIN public.cars c ON r.car_id = c.id
LEFT JOIN public.hotels h ON r.hotel_id = h.id
WHERE p.id IS NULL AND c.id IS NULL AND h.id IS NULL;

-- Refresh materialized views if any exist
-- REFRESH MATERIALIZED VIEW view_name;

-- Update statistics for better query performance
ANALYZE;

-- Import summary
SELECT 
  'Data import completed at: ' || now()::text as summary,
  'Please verify all data and test functionality' as next_steps;

-- Post-import tasks reminder
SELECT 'POST-IMPORT TASKS:' as reminder
UNION ALL SELECT '1. Reset admin user passwords'
UNION ALL SELECT '2. Update environment variables in application'
UNION ALL SELECT '3. Test all major functionality'
UNION ALL SELECT '4. Verify payment integrations'
UNION ALL SELECT '5. Test edge functions'
UNION ALL SELECT '6. Check email notifications'
UNION ALL SELECT '7. Verify file uploads work'
UNION ALL SELECT '8. Test authentication flows';

-- Clean up any temporary data or test records if needed
-- DELETE FROM public.properties WHERE is_dummy = true;
-- DELETE FROM public.cars WHERE is_dummy = true;
-- DELETE FROM public.hotels WHERE is_dummy = true;
