
# A string used to distinguish different Supabase projects on the same host. Defaults to the working
# directory name when running `supabase init`.
project_id = "gesco-stay"
[api]
# Port to use for the API URL.
port = 54321
# Schemas to expose in your API. Tables, views and stored procedures in this schema will get API
# endpoints. public and storage are always included.
schemas = ["public", "storage", "graphql_public"]
# Extra schemas to add to the search_path of every request. public is always included.
extra_search_path = ["public", "extensions"]
# The maximum number of rows returns from a view, table, or stored procedure. Limits payload size
# for accidental or malicious requests.
max_rows = 1000

[db]
# Port to use for the local database URL.
port = 54322
# The database major version to use. This has to be the same as your remote database's. Run `SHOW
# server_version;` on the remote database to check.
major_version = 15

[studio]
# Port to use for Supabase Studio.
port = 54323

# Email testing server. Emails sent with the local dev setup are not actually sent - rather, they
# are monitored, and you can view the emails that would have been sent from the web interface.
[inbucket]
# Port to use for the email testing server web interface.
port = 54324
smtp_port = 54325
pop3_port = 54326

[storage]
# The maximum file size allowed (e.g. "5MB", "500KB").
file_size_limit = "50MiB"

[auth]
# The base URL of your website. Used as an allow-list for redirects and for constructing URLs used
# in emails.
site_url = "http://localhost:3000"
# A list of *exact* URLs that auth providers are permitted to redirect to post authentication.
additional_redirect_urls = ["https://localhost:3000"]
# How long tokens are valid for, in seconds. Defaults to 3600 (1 hour), maximum 604,800 (1 week).
jwt_expiry = 3600
# Allow/disallow new user signups to your project.
enable_signup = true
# If enabled, a user will be required to confirm any email change on both the old, and new email
# addresses. If disabled, only the new email is required to confirm.
double_confirm_changes = true
# If enabled, users need to confirm their email address before signing in.
enable_confirmations = true

# Custom email templates with Gesco Stay branding
[auth.email]
# Templates for your emails
enable_signup = true
enable_confirmations = true
template_create_user = """
<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
  <div style="text-align: center; margin-bottom: 30px;">
    <h1 style="color: #2563eb; margin-bottom: 10px;">Gesco Stay</h1>
    <h2 style="color: #374151; margin-bottom: 20px;">Email Verification</h2>
  </div>

  <div style="background-color: #f9fafb; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
    <p style="color: #374151; margin-bottom: 15px;">Hello there,</p>
    <p style="color: #374151; margin-bottom: 15px;">Thank you for signing up with Gesco Stay! To complete your registration, please use the verification code below:</p>

    <div style="text-align: center; margin: 30px 0;">
      <div style="background-color: #2563eb; color: white; font-size: 32px; font-weight: bold; padding: 15px 30px; border-radius: 8px; letter-spacing: 8px; display: inline-block;">
        {{ .Token }}
      </div>
    </div>

    <p style="color: #374151; margin-bottom: 15px;">This code will expire in 10 minutes for security reasons.</p>

    <p style="color: #6b7280; font-size: 14px; margin-bottom: 0;">If you didn't request this verification code, please ignore this email.</p>
  </div>

  <div style="text-align: center; color: #6b7280; font-size: 12px;">
    <p>© 2024 Gesco Stay. All rights reserved.</p>
    <p>This is an automated message, please do not reply to this email.</p>
  </div>
</div>
"""
template_password_reset = """
<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
  <div style="text-align: center; margin-bottom: 30px;">
    <h1 style="color: #2563eb; margin-bottom: 10px;">Gesco Stay</h1>
    <h2 style="color: #374151; margin-bottom: 20px;">Reset Your Password</h2>
  </div>

  <div style="background-color: #f9fafb; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
    <p style="color: #374151; margin-bottom: 15px;">Hello,</p>
    <p style="color: #374151; margin-bottom: 15px;">We received a request to reset your password for your Gesco Stay account. Click the button below to reset your password:</p>

    <div style="text-align: center; margin: 30px 0;">
      <a href="{{ .ConfirmationURL }}" style="background-color: #2563eb; color: white; padding: 12px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; display: inline-block;">Reset Password</a>
    </div>

    <p style="color: #374151; margin-bottom: 15px;">If the button doesn't work, you can copy and paste this link into your browser:</p>
    <p style="color: #6b7280; font-size: 14px; word-break: break-all; margin-bottom: 15px;">{{ .ConfirmationURL }}</p>

    <p style="color: #6b7280; font-size: 14px; margin-bottom: 0;">If you didn't request a password reset, please ignore this email.</p>
  </div>

  <div style="text-align: center; color: #6b7280; font-size: 12px;">
    <p>© 2024 Gesco Stay. All rights reserved.</p>
    <p>This is an automated message, please do not reply to this email.</p>
  </div>
</div>
"""
template_magic_link = """
<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
  <div style="text-align: center; margin-bottom: 30px;">
    <h1 style="color: #2563eb; margin-bottom: 10px;">Gesco Stay</h1>
    <h2 style="color: #374151; margin-bottom: 20px;">Magic Link Login</h2>
  </div>

  <div style="background-color: #f9fafb; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
    <p style="color: #374151; margin-bottom: 15px;">Hello,</p>
    <p style="color: #374151; margin-bottom: 15px;">Click the button below to sign in to your Gesco Stay account:</p>

    <div style="text-align: center; margin: 30px 0;">
      <a href="{{ .ConfirmationURL }}" style="background-color: #2563eb; color: white; padding: 12px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; display: inline-block;">Sign In</a>
    </div>

    <p style="color: #374151; margin-bottom: 15px;">If the button doesn't work, you can copy and paste this link into your browser:</p>
    <p style="color: #6b7280; font-size: 14px; word-break: break-all; margin-bottom: 15px;">{{ .ConfirmationURL }}</p>

    <p style="color: #6b7280; font-size: 14px; margin-bottom: 0;">If you didn't request this login link, please ignore this email.</p>
  </div>

  <div style="text-align: center; color: #6b7280; font-size: 12px;">
    <p>© 2024 Gesco Stay. All rights reserved.</p>
    <p>This is an automated message, please do not reply to this email.</p>
  </div>
</div>
"""
template_email_change = """
<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
  <div style="text-align: center; margin-bottom: 30px;">
    <h1 style="color: #2563eb; margin-bottom: 10px;">Gesco Stay</h1>
    <h2 style="color: #374151; margin-bottom: 20px;">Confirm Email Change</h2>
  </div>

  <div style="background-color: #f9fafb; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
    <p style="color: #374151; margin-bottom: 15px;">Hello,</p>
    <p style="color: #374151; margin-bottom: 15px;">We received a request to change your email address. Click the button below to confirm this change:</p>

    <div style="text-align: center; margin: 30px 0;">
      <a href="{{ .ConfirmationURL }}" style="background-color: #2563eb; color: white; padding: 12px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; display: inline-block;">Confirm Email Change</a>
    </div>

    <p style="color: #374151; margin-bottom: 15px;">If the button doesn't work, you can copy and paste this link into your browser:</p>
    <p style="color: #6b7280; font-size: 14px; word-break: break-all; margin-bottom: 15px;">{{ .ConfirmationURL }}</p>

    <p style="color: #6b7280; font-size: 14px; margin-bottom: 0;">If you didn't request this email change, please ignore this email.</p>
  </div>

  <div style="text-align: center; color: #6b7280; font-size: 12px;">
    <p>© 2024 Gesco Stay. All rights reserved.</p>
    <p>This is an automated message, please do not reply to this email.</p>
  </div>
</div>
"""

# Configure Edge Functions
[functions]
[functions.send-booking-confirmation]
verify_jwt = true
[functions.send-booking-reminders]
verify_jwt = false
[functions.verify-stripe-payment]
verify_jwt = true
[functions.process-stripe-payment]
verify_jwt = true

[analytics]
enabled = false
port = 54327
vector_port = 54328
