
import React, { useState, useEffect } from "react";
import { useAuth } from "@/components/layout/AuthProvider";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import AdminLayout from "@/components/layout/AdminLayout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Check, X, AlertCircle, DollarSign, Wallet } from "lucide-react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import PlatformFeesTable, { PlatformEarning, EarningsSummary } from "@/components/admin/PlatformFeesTable";

// Define the PayoutRequest type to match the structure from the database
interface PayoutRequest {
  id: string;
  host_id: string;
  amount: number;
  status: string;
  notes: string;
  admin_notes: string;
  created_at: string;
  updated_at: string;
  payment_method_id: string;
  processed_at: string;
  processed_by: string;
  payment_method: {
    id: string;
    provider: string;
    account_id: string;
    status: string;
  };
  host_details: {
    first_name: string;
    last_name: string;
    avatar_url: string;
  };
}

const formSchema = z.object({
  admin_notes: z.string().min(1, "Admin notes are required"),
});

const AdminPayments = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [payoutRequests, setPayoutRequests] = useState<PayoutRequest[]>([]);
  const [platformEarnings, setPlatformEarnings] = useState<PlatformEarning[]>([]);
  const [selectedRequest, setSelectedRequest] = useState<PayoutRequest | null>(null);
  const [approveDialogOpen, setApproveDialogOpen] = useState(false);
  const [rejectDialogOpen, setRejectDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState("requests");
  const [earningsSummary, setEarningsSummary] = useState<EarningsSummary>({
    totalPlatformFees: 0,
    totalBookingAmount: 0,
    totalTransactions: 0
  });

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      admin_notes: "",
    },
  });

  useEffect(() => {
    if (user) {
      fetchPayoutRequests();
      fetchPlatformEarnings();
    }
  }, [user]);

  const fetchPayoutRequests = async () => {
    if (!user) return;
    setIsLoading(true);
    
    try {
      // Use the custom function to get detailed payout requests
      const { data, error } = await supabase.rpc('get_payout_requests_with_details', {
        admin_id: user.id
      });
      
      if (error) throw error;
      
      // Ensure the data matches the PayoutRequest type
      const typedData = data.map(item => ({
        ...item,
        payment_method: item.payment_method as PayoutRequest['payment_method'],
        host_details: item.host_details as PayoutRequest['host_details']
      }));
      
      setPayoutRequests(typedData);
    } catch (error) {
      console.error("Error fetching payout requests:", error);
      toast({
        title: "Error",
        description: "Could not fetch payout requests",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const fetchPlatformEarnings = async () => {
    try {
      const { data, error } = await supabase
        .from("platform_earnings")
        .select("*")
        .order("created_at", { ascending: false });
        
      if (error) throw error;
      
      const earnings = data || [];
      setPlatformEarnings(earnings);
      
      // Calculate summary
      const totalPlatformFees = earnings.reduce((sum, record) => sum + (record.platform_fee || 0), 0);
      const totalBookingAmount = earnings.reduce((sum, record) => sum + (record.total_booking_amount || 0), 0);
      
      setEarningsSummary({
        totalPlatformFees,
        totalBookingAmount,
        totalTransactions: earnings.length
      });
      
    } catch (error) {
      console.error("Error fetching platform earnings:", error);
    }
  };

  const handleApproveRequest = async (formValues: z.infer<typeof formSchema>) => {
    if (!selectedRequest || !user) return;
    
    try {
      const { error } = await supabase
        .from("payout_requests")
        .update({
          status: "approved",
          admin_notes: formValues.admin_notes,
          processed_at: new Date().toISOString(),
          processed_by: user.id
        })
        .eq("id", selectedRequest.id);
        
      if (error) throw error;
      
      toast({
        title: "Success",
        description: "Payout request approved",
      });
      
      form.reset();
      setApproveDialogOpen(false);
      fetchPayoutRequests();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const handleRejectRequest = async (formValues: z.infer<typeof formSchema>) => {
    if (!selectedRequest || !user) return;
    
    try {
      const { error } = await supabase
        .from("payout_requests")
        .update({
          status: "rejected",
          admin_notes: formValues.admin_notes,
          processed_at: new Date().toISOString(),
          processed_by: user.id
        })
        .eq("id", selectedRequest.id);
        
      if (error) throw error;
      
      toast({
        title: "Success",
        description: "Payout request rejected",
      });
      
      form.reset();
      setRejectDialogOpen(false);
      fetchPayoutRequests();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  if (!user) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-screen">
          <AlertCircle className="h-8 w-8 text-red-500 mr-2" />
          <span>Authentication required</span>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="p-4 md:p-6">
        <h1 className="text-3xl font-bold mb-6">Payments Management</h1>
        
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2 mb-8">
            <TabsTrigger value="requests">Payout Requests</TabsTrigger>
            <TabsTrigger value="platform">Platform Earnings</TabsTrigger>
          </TabsList>
          
          <TabsContent value="requests">
            <Card>
              <CardHeader>
                <CardTitle>Host Payout Requests</CardTitle>
                <CardDescription>
                  Review and approve or reject payout requests from hosts
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="flex justify-center my-12">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
                  </div>
                ) : payoutRequests.length === 0 ? (
                  <div className="text-center py-8">
                    <p className="text-muted-foreground">No payout requests found</p>
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Host</TableHead>
                          <TableHead>Amount</TableHead>
                          <TableHead>Payment Method</TableHead>
                          <TableHead>Date Requested</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {payoutRequests.map((request) => (
                          <TableRow key={request.id}>
                            <TableCell>
                              <div className="flex items-center">
                                <div className="w-8 h-8 rounded-full bg-gray-200 mr-2 overflow-hidden">
                                  {request.host_details?.avatar_url ? (
                                    <img 
                                      src={request.host_details.avatar_url} 
                                      alt={`${request.host_details.first_name} ${request.host_details.last_name}`}
                                      className="w-full h-full object-cover"
                                    />
                                  ) : (
                                    <div className="w-full h-full flex items-center justify-center bg-secondary text-secondary-foreground">
                                      {request.host_details?.first_name?.[0] || 'U'}
                                    </div>
                                  )}
                                </div>
                                <span>
                                  {request.host_details?.first_name} {request.host_details?.last_name}
                                </span>
                              </div>
                            </TableCell>
                            <TableCell className="font-medium">${request.amount.toFixed(2)}</TableCell>
                            <TableCell>
                              {request.payment_method?.provider === 'stripe' ? (
                                <div className="flex items-center">
                                  <DollarSign className="h-4 w-4 mr-1" />
                                  <span>Stripe</span>
                                </div>
                              ) : (
                                <div className="flex items-center">
                                  <Wallet className="h-4 w-4 mr-1" />
                                  <span>Wave</span>
                                </div>
                              )}
                            </TableCell>
                            <TableCell>{new Date(request.created_at).toLocaleDateString()}</TableCell>
                            <TableCell>
                              <Badge variant={
                                request.status === "pending" ? "secondary" : 
                                request.status === "approved" ? "outline" : 
                                "destructive"
                              }>
                                {request.status.charAt(0).toUpperCase() + request.status.slice(1)}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              {request.status === "pending" && (
                                <div className="flex space-x-2">
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    className="flex items-center"
                                    onClick={() => {
                                      setSelectedRequest(request);
                                      setApproveDialogOpen(true);
                                    }}
                                  >
                                    <Check className="h-4 w-4 mr-1" />
                                    Approve
                                  </Button>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    className="flex items-center text-destructive hover:text-destructive"
                                    onClick={() => {
                                      setSelectedRequest(request);
                                      setRejectDialogOpen(true);
                                    }}
                                  >
                                    <X className="h-4 w-4 mr-1" />
                                    Reject
                                  </Button>
                                </div>
                              )}
                              {(request.status === "approved" || request.status === "rejected") && (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => {
                                    setSelectedRequest(request);
                                    toast({
                                      title: "Admin Notes",
                                      description: request.admin_notes || "No notes provided",
                                    });
                                  }}
                                >
                                  View Notes
                                </Button>
                              )}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="platform">
            <PlatformFeesTable 
              earnings={platformEarnings} 
              summary={earningsSummary}
            />
          </TabsContent>
        </Tabs>

        {/* Approve Dialog */}
        <Dialog open={approveDialogOpen} onOpenChange={setApproveDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Approve Payout Request</DialogTitle>
              <DialogDescription>
                This will approve the payout request of ${selectedRequest?.amount.toFixed(2)} for {selectedRequest?.host_details?.first_name} {selectedRequest?.host_details?.last_name}
              </DialogDescription>
            </DialogHeader>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(handleApproveRequest)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="admin_notes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Admin Notes</FormLabel>
                      <FormControl>
                        <Textarea placeholder="Enter notes for this approval" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <DialogFooter>
                  <Button variant="outline" onClick={() => setApproveDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button type="submit">Approve</Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>

        {/* Reject Dialog */}
        <Dialog open={rejectDialogOpen} onOpenChange={setRejectDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Reject Payout Request</DialogTitle>
              <DialogDescription>
                This will reject the payout request of ${selectedRequest?.amount.toFixed(2)} for {selectedRequest?.host_details?.first_name} {selectedRequest?.host_details?.last_name}
              </DialogDescription>
            </DialogHeader>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(handleRejectRequest)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="admin_notes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Admin Notes</FormLabel>
                      <FormControl>
                        <Textarea placeholder="Enter reason for rejection" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <DialogFooter>
                  <Button variant="outline" onClick={() => setRejectDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button variant="destructive" type="submit">Reject</Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>
    </AdminLayout>
  );
};

export default AdminPayments;
