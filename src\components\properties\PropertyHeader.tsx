import { Link } from "react-router-dom";
import { MapPin, Star, Share, Heart, ArrowLeft, TestTube } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";

interface PropertyHeaderProps {
  title: string;
  location: string;
  rating: number;
  reviews: number;
  isSuperHost: boolean;
  is_dummy?: boolean;
}

const PropertyHeader = ({
  title,
  location,
  rating,
  reviews,
  isSuperHost,
  is_dummy,
}: PropertyHeaderProps) => {
  const handleShare = async () => {
    const url = window.location.href;
    const shareData = {
      title: `${title} - Gesco Stay`,
      text: `Check out this amazing property: ${title} in ${location}`,
      url: url,
    };

    try {
      // Check if Web Share API is supported
      if (
        navigator.share &&
        navigator.canShare &&
        navigator.canShare(shareData)
      ) {
        await navigator.share(shareData);
        // Only show success message after share is completed
        toast.success("Shared successfully!");
      } else {
        // Fallback: Copy URL to clipboard
        await navigator.clipboard.writeText(url);
        toast.success("Property link copied to clipboard!");
      }
    } catch (error: any) {
      // Handle user cancellation or other errors
      if (error.name === "AbortError") {
        // User cancelled the share, don't show error
        return;
      }
      // If clipboard API fails, show the URL in a toast
      toast.info(`Share this property: ${url}`);
    }
  };

  return (
    <div className="mb-6 relative">
      {is_dummy && (
        <div className="absolute top-0 right-0 z-10">
          <Badge className="bg-orange-100 text-orange-800 flex items-center gap-1">
            <TestTube className="w-3 h-3" />
            Test Listing
          </Badge>
        </div>
      )}
      <Link
        to="/listings"
        className="flex items-center text-brown hover:text-accent mb-4"
      >
        <ArrowLeft className="w-4 h-4 mr-2" />
        Back to listings
      </Link>

      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-brown">{title}</h1>
        <div className="flex space-x-4">
          <Button
            variant="outline"
            className="flex items-center"
            onClick={handleShare}
          >
            <Share className="w-4 h-4 mr-2" />
            Share
          </Button>
          <Button variant="outline" className="flex items-center">
            <Heart className="w-4 h-4 mr-2" />
            Save
          </Button>
        </div>
      </div>

      <div className="flex items-center mt-2 text-gray-600">
        <MapPin className="w-4 h-4 mr-1" />
        <span>{location}</span>
        {isSuperHost && (
          <span className="ml-4 px-2 py-1 bg-secondary/10 text-secondary text-xs font-medium rounded-md">
            Superhost
          </span>
        )}
      </div>

      <div className="flex items-center mt-1">
        <Star className="w-4 h-4 text-yellow-500 mr-1" />
        <span className="font-medium">{rating}</span>
        <span className="mx-1">·</span>
        <span className="text-blue-600 hover:underline cursor-pointer">
          {reviews} reviews
        </span>
      </div>
    </div>
  );
};

export default PropertyHeader;
