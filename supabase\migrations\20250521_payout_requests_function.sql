
-- Function to get payout requests with host and payment method details
CREATE OR REPLACE FUNCTION public.get_payout_requests_with_details()
RETURNS TABLE (
  id uuid,
  host_id uuid,
  amount numeric,
  status text,
  notes text,
  admin_notes text,
  created_at timestamptz,
  updated_at timestamptz,
  payment_method_id uuid,
  processed_at timestamptz,
  processed_by uuid,
  host_name text,
  host_email text,
  payment_method jsonb
)
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT 
    pr.id,
    pr.host_id,
    pr.amount,
    pr.status,
    pr.notes,
    pr.admin_notes,
    pr.created_at,
    pr.updated_at,
    pr.payment_method_id,
    pr.processed_at,
    pr.processed_by,
    (p.first_name || ' ' || p.last_name) as host_name,
    (SELECT email FROM auth.users WHERE id = pr.host_id) as host_email,
    jsonb_build_object(
      'provider', hpm.provider,
      'account_id', hpm.account_id,
      'status', hpm.status
    ) as payment_method
  FROM 
    payout_requests pr
  LEFT JOIN 
    profiles p ON pr.host_id = p.id
  LEFT JOIN 
    host_payment_methods hpm ON pr.payment_method_id = hpm.id
  ORDER BY
    pr.created_at DESC;
$$;
