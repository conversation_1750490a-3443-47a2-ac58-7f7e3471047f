-- Add verification status fields to profiles table
ALTER TABLE profiles 
ADD COLUMN IF NOT EXISTS email_verified BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS phone_verified BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS email_verified_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS phone_verified_at TIMESTAMP WITH TIME ZONE;

-- Create email_otps table for storing email verification codes
CREATE TABLE IF NOT EXISTS email_otps (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    email TEXT NOT NULL,
    otp TEXT NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    used_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(email)
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_email_otps_email ON email_otps(email);
CREATE INDEX IF NOT EXISTS idx_email_otps_expires_at ON email_otps(expires_at);

-- Function to update verification status when auth.users is updated
CREATE OR REPLACE FUNCTION update_profile_verification_status()
RETURNS TRIGGER AS $$
BEGIN
    -- Update email verification status
    IF NEW.email_confirmed_at IS NOT NULL AND OLD.email_confirmed_at IS NULL THEN
        UPDATE profiles 
        SET 
            email_verified = TRUE,
            email_verified_at = NEW.email_confirmed_at
        WHERE id = NEW.id;
    END IF;
    
    -- Update phone verification status
    IF NEW.phone_confirmed_at IS NOT NULL AND OLD.phone_confirmed_at IS NULL THEN
        UPDATE profiles 
        SET 
            phone_verified = TRUE,
            phone_verified_at = NEW.phone_confirmed_at
        WHERE id = NEW.id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically update verification status
DROP TRIGGER IF EXISTS trigger_update_verification_status ON auth.users;
CREATE TRIGGER trigger_update_verification_status
    AFTER UPDATE ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION update_profile_verification_status();

-- Update existing profiles based on current auth.users data
UPDATE profiles 
SET 
    email_verified = CASE 
        WHEN au.email_confirmed_at IS NOT NULL THEN TRUE 
        ELSE FALSE 
    END,
    email_verified_at = au.email_confirmed_at,
    phone_verified = CASE 
        WHEN au.phone_confirmed_at IS NOT NULL THEN TRUE 
        ELSE FALSE 
    END,
    phone_verified_at = au.phone_confirmed_at
FROM auth.users au 
WHERE profiles.id = au.id;

-- Function to clean up expired OTPs
CREATE OR REPLACE FUNCTION cleanup_expired_otps()
RETURNS void AS $$
BEGIN
    DELETE FROM email_otps 
    WHERE expires_at < NOW() OR used_at IS NOT NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a scheduled job to clean up expired OTPs (runs every hour)
-- Note: This requires pg_cron extension to be enabled
-- SELECT cron.schedule('cleanup-expired-otps', '0 * * * *', 'SELECT cleanup_expired_otps();');

COMMENT ON TABLE email_otps IS 'Stores email verification OTP codes';
COMMENT ON COLUMN profiles.email_verified IS 'Whether the user email has been verified';
COMMENT ON COLUMN profiles.phone_verified IS 'Whether the user phone has been verified';
COMMENT ON COLUMN profiles.email_verified_at IS 'When the email was verified';
COMMENT ON COLUMN profiles.phone_verified_at IS 'When the phone was verified';
