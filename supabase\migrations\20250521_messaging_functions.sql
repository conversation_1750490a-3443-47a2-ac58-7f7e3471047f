-- Messaging system functions

-- Function to create or get existing conversation between two users
CREATE OR REPLACE FUNCTION get_or_create_conversation(
  other_user_id UUID,
  property_id_param UUID DEFAULT NULL,
  car_id_param UUID DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  conversation_id UUID;
  current_user_id UUID;
BEGIN
  current_user_id := auth.uid();
  
  -- Check if user is authenticated
  IF current_user_id IS NULL THEN
    RAISE EXCEPTION 'User must be authenticated';
  END IF;
  
  -- Check if trying to create conversation with self
  IF current_user_id = other_user_id THEN
    RAISE EXCEPTION 'Cannot create conversation with yourself';
  END IF;
  
  -- Try to find existing conversation (check both participant orders)
  SELECT id INTO conversation_id
  FROM public.conversations
  WHERE (
    (participant_1_id = current_user_id AND participant_2_id = other_user_id) OR
    (participant_1_id = other_user_id AND participant_2_id = current_user_id)
  )
  AND (
    (property_id_param IS NULL AND property_id IS NULL) OR
    (property_id = property_id_param)
  )
  AND (
    (car_id_param IS NULL AND car_id IS NULL) OR
    (car_id = car_id_param)
  )
  LIMIT 1;
  
  -- If conversation doesn't exist, create it
  IF conversation_id IS NULL THEN
    INSERT INTO public.conversations (
      participant_1_id,
      participant_2_id,
      property_id,
      car_id
    )
    VALUES (
      current_user_id,
      other_user_id,
      property_id_param,
      car_id_param
    )
    RETURNING id INTO conversation_id;
  END IF;
  
  RETURN conversation_id;
END;
$$;

-- Function to send a message
CREATE OR REPLACE FUNCTION send_message(
  conversation_id_param UUID,
  content_param TEXT
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  message_id UUID;
  current_user_id UUID;
BEGIN
  current_user_id := auth.uid();
  
  -- Check if user is authenticated
  IF current_user_id IS NULL THEN
    RAISE EXCEPTION 'User must be authenticated';
  END IF;
  
  -- Validate content
  IF length(trim(content_param)) = 0 THEN
    RAISE EXCEPTION 'Message content cannot be empty';
  END IF;
  
  -- Check if user is participant in the conversation
  IF NOT EXISTS (
    SELECT 1 FROM public.conversations
    WHERE id = conversation_id_param
    AND (participant_1_id = current_user_id OR participant_2_id = current_user_id)
  ) THEN
    RAISE EXCEPTION 'User is not a participant in this conversation';
  END IF;
  
  -- Insert the message
  INSERT INTO public.messages (
    conversation_id,
    sender_id,
    content
  )
  VALUES (
    conversation_id_param,
    current_user_id,
    content_param
  )
  RETURNING id INTO message_id;
  
  RETURN message_id;
END;
$$;

-- Function to mark messages as read
CREATE OR REPLACE FUNCTION mark_messages_as_read(
  conversation_id_param UUID
)
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  updated_count INTEGER;
  current_user_id UUID;
BEGIN
  current_user_id := auth.uid();
  
  -- Check if user is authenticated
  IF current_user_id IS NULL THEN
    RAISE EXCEPTION 'User must be authenticated';
  END IF;
  
  -- Check if user is participant in the conversation
  IF NOT EXISTS (
    SELECT 1 FROM public.conversations
    WHERE id = conversation_id_param
    AND (participant_1_id = current_user_id OR participant_2_id = current_user_id)
  ) THEN
    RAISE EXCEPTION 'User is not a participant in this conversation';
  END IF;
  
  -- Mark unread messages as read (only messages not sent by current user)
  UPDATE public.messages
  SET read_at = now()
  WHERE conversation_id = conversation_id_param
    AND sender_id != current_user_id
    AND read_at IS NULL;
  
  GET DIAGNOSTICS updated_count = ROW_COUNT;
  
  RETURN updated_count;
END;
$$;

-- Function to get conversations with last message info
CREATE OR REPLACE FUNCTION get_user_conversations()
RETURNS TABLE (
  id UUID,
  other_user_id UUID,
  other_user_name TEXT,
  other_user_avatar TEXT,
  property_id UUID,
  property_title TEXT,
  car_id UUID,
  car_title TEXT,
  last_message_content TEXT,
  last_message_at TIMESTAMPTZ,
  last_message_sender_id UUID,
  unread_count BIGINT,
  created_at TIMESTAMPTZ
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  current_user_id UUID;
BEGIN
  current_user_id := auth.uid();
  
  -- Check if user is authenticated
  IF current_user_id IS NULL THEN
    RAISE EXCEPTION 'User must be authenticated';
  END IF;
  
  RETURN QUERY
  SELECT 
    c.id,
    CASE 
      WHEN c.participant_1_id = current_user_id THEN c.participant_2_id
      ELSE c.participant_1_id
    END as other_user_id,
    CASE 
      WHEN c.participant_1_id = current_user_id THEN 
        COALESCE(p2.first_name || ' ' || p2.last_name, 'Unknown User')
      ELSE 
        COALESCE(p1.first_name || ' ' || p1.last_name, 'Unknown User')
    END as other_user_name,
    CASE 
      WHEN c.participant_1_id = current_user_id THEN p2.avatar_url
      ELSE p1.avatar_url
    END as other_user_avatar,
    c.property_id,
    prop.title as property_title,
    c.car_id,
    car.title as car_title,
    lm.content as last_message_content,
    c.last_message_at,
    lm.sender_id as last_message_sender_id,
    COALESCE(unread.count, 0) as unread_count,
    c.created_at
  FROM public.conversations c
  LEFT JOIN public.profiles p1 ON c.participant_1_id = p1.id
  LEFT JOIN public.profiles p2 ON c.participant_2_id = p2.id
  LEFT JOIN public.properties prop ON c.property_id = prop.id
  LEFT JOIN public.cars car ON c.car_id = car.id
  LEFT JOIN LATERAL (
    SELECT content, sender_id
    FROM public.messages m
    WHERE m.conversation_id = c.id
    ORDER BY m.created_at DESC
    LIMIT 1
  ) lm ON true
  LEFT JOIN LATERAL (
    SELECT COUNT(*) as count
    FROM public.messages m
    WHERE m.conversation_id = c.id
      AND m.sender_id != current_user_id
      AND m.read_at IS NULL
  ) unread ON true
  WHERE c.participant_1_id = current_user_id OR c.participant_2_id = current_user_id
  ORDER BY c.last_message_at DESC;
END;
$$;
