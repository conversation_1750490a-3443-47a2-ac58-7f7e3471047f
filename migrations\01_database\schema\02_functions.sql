-- Gesco Stay Database Schema - Functions
-- This file contains all database functions for the Gesco Stay application

-- Function to update updated_at column
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY INVOKER;

-- Function to update profiles updated_at
CREATE OR REPLACE FUNCTION update_profiles_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY INVOKER;

-- Function to update modified column (generic)
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY INVOKER;

-- Function to handle new user registration
CREATE OR REPLACE FUNCTION handle_new_user()
R<PERSON>URNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (
    id,
    first_name,
    last_name,
    phone_number,
    email
  )
  VALUES (
    NEW.id,
    COALESCE(NEW.raw_user_meta_data->>'first_name', ''),
    COALESCE(NEW.raw_user_meta_data->>'last_name', ''),
    COALESCE(NEW.phone, NEW.raw_user_meta_data->>'phone_number', ''),
    COALESCE(NEW.email, NEW.raw_user_meta_data->>'email', '')
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Manual function to create user profile (use this in your application)
CREATE OR REPLACE FUNCTION handle_new_user_manual(
  user_id UUID,
  first_name TEXT DEFAULT '',
  last_name TEXT DEFAULT '',
  email TEXT DEFAULT '',
  phone_number TEXT DEFAULT ''
)
RETURNS UUID AS $$
BEGIN
  INSERT INTO public.profiles (
    id,
    first_name,
    last_name,
    phone_number,
    email
  )
  VALUES (
    user_id,
    COALESCE(first_name, ''),
    COALESCE(last_name, ''),
    COALESCE(phone_number, ''),
    COALESCE(email, '')
  )
  ON CONFLICT (id) DO UPDATE SET
    first_name = COALESCE(EXCLUDED.first_name, profiles.first_name),
    last_name = COALESCE(EXCLUDED.last_name, profiles.last_name),
    phone_number = COALESCE(EXCLUDED.phone_number, profiles.phone_number),
    email = COALESCE(EXCLUDED.email, profiles.email),
    updated_at = now();

  RETURN user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user is admin
CREATE OR REPLACE FUNCTION is_admin(uid UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = uid AND role = 'admin'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check property availability
CREATE OR REPLACE FUNCTION check_property_availability(
  property_id UUID,
  check_in_date DATE,
  check_out_date DATE
)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN NOT EXISTS (
    SELECT 1 FROM bookings
    WHERE bookings.property_id = check_property_availability.property_id
    AND status = 'confirmed'
    AND (
      (check_in_date, check_out_date) OVERLAPS (check_in, check_out)
    )
  );
END;
$$ LANGUAGE plpgsql SECURITY INVOKER;

-- Function to check car availability
CREATE OR REPLACE FUNCTION check_car_availability(
  car_id UUID,
  start_date DATE,
  end_date DATE
)
RETURNS BOOLEAN AS $$
declare
  overlap_count integer;
begin
  select count(*) into overlap_count
  from car_bookings cb
  where cb.car_id = check_car_availability.car_id
    and cb.status = 'confirmed'
    and (
      (cb.start_date, cb.end_date) overlaps (check_car_availability.start_date, check_car_availability.end_date)
    );
  return overlap_count = 0;
end;
$$ LANGUAGE plpgsql SECURITY INVOKER;

-- Function to check room availability
CREATE OR REPLACE FUNCTION check_room_availability(
  p_room_type_id UUID,
  p_check_in DATE,
  p_check_out DATE
)
RETURNS INTEGER AS $$
DECLARE
  total_rooms INTEGER;
  booked_rooms INTEGER;
  available_rooms INTEGER;
BEGIN
  -- Get total rooms for this room type
  SELECT COUNT(*) INTO total_rooms
  FROM public.room_inventory ri
  WHERE ri.room_type_id = p_room_type_id
    AND ri.status = 'available';

  -- Get booked rooms for the date range - only confirmed bookings
  SELECT COALESCE(SUM(hb.rooms_count), 0) INTO booked_rooms
  FROM public.hotel_bookings hb
  WHERE hb.room_type_id = p_room_type_id
    AND hb.status = 'confirmed'
    AND (
      (hb.check_in <= p_check_in AND hb.check_out > p_check_in) OR
      (hb.check_in < p_check_out AND hb.check_out >= p_check_out) OR
      (hb.check_in >= p_check_in AND hb.check_out <= p_check_out)
    );

  available_rooms := total_rooms - booked_rooms;

  RETURN GREATEST(0, available_rooms);
END;
$$ LANGUAGE plpgsql SECURITY INVOKER;

-- Function to calculate platform fee (15%)
CREATE OR REPLACE FUNCTION calculate_platform_fee(booking_amount DECIMAL)
RETURNS DECIMAL AS $$
DECLARE
  platform_fee DECIMAL;
BEGIN
  platform_fee := booking_amount * 0.15;
  RETURN platform_fee;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to calculate host payout (85%)
CREATE OR REPLACE FUNCTION calculate_host_payout(booking_amount DECIMAL)
RETURNS DECIMAL AS $$
DECLARE
  host_payout DECIMAL;
BEGIN
  host_payout := booking_amount * 0.85;
  RETURN host_payout;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to ensure correct platform fee calculation
CREATE OR REPLACE FUNCTION ensure_correct_platform_fee()
RETURNS TRIGGER AS $$
BEGIN
  NEW.platform_fee := NEW.total_booking_amount * 0.15;
  NEW.host_payout_amount := NEW.total_booking_amount * 0.85;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY INVOKER;

-- Function to get or create conversation
CREATE OR REPLACE FUNCTION get_or_create_conversation(
  other_user_id UUID,
  property_id_param UUID DEFAULT NULL,
  car_id_param UUID DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  conversation_id UUID;
  current_user_id UUID;
BEGIN
  current_user_id := auth.uid();

  -- Check if user is authenticated
  IF current_user_id IS NULL THEN
    RAISE EXCEPTION 'User must be authenticated';
  END IF;

  -- Check if trying to create conversation with self
  IF current_user_id = other_user_id THEN
    RAISE EXCEPTION 'Cannot create conversation with yourself';
  END IF;

  -- Try to find existing conversation (check both participant orders)
  SELECT id INTO conversation_id
  FROM public.conversations
  WHERE (
    (participant_1_id = current_user_id AND participant_2_id = other_user_id) OR
    (participant_1_id = other_user_id AND participant_2_id = current_user_id)
  )
  AND (
    (property_id_param IS NULL AND property_id IS NULL) OR
    (property_id = property_id_param)
  )
  AND (
    (car_id_param IS NULL AND car_id IS NULL) OR
    (car_id = car_id_param)
  )
  LIMIT 1;

  -- If conversation doesn't exist, create it
  IF conversation_id IS NULL THEN
    INSERT INTO public.conversations (
      participant_1_id,
      participant_2_id,
      property_id,
      car_id
    )
    VALUES (
      current_user_id,
      other_user_id,
      property_id_param,
      car_id_param
    )
    RETURNING id INTO conversation_id;
  END IF;

  RETURN conversation_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to send message
CREATE OR REPLACE FUNCTION send_message(
  conversation_id_param UUID,
  content_param TEXT
)
RETURNS UUID AS $$
DECLARE
  message_id UUID;
  current_user_id UUID;
BEGIN
  current_user_id := auth.uid();
  
  -- Check if user is authenticated
  IF current_user_id IS NULL THEN
    RAISE EXCEPTION 'User must be authenticated';
  END IF;
  
  -- Validate content
  IF length(trim(content_param)) = 0 THEN
    RAISE EXCEPTION 'Message content cannot be empty';
  END IF;
  
  -- Check if user is participant in the conversation
  IF NOT EXISTS (
    SELECT 1 FROM public.conversations
    WHERE id = conversation_id_param
    AND (participant_1_id = current_user_id OR participant_2_id = current_user_id)
  ) THEN
    RAISE EXCEPTION 'User is not a participant in this conversation';
  END IF;
  
  -- Insert the message
  INSERT INTO public.messages (
    conversation_id,
    sender_id,
    content
  )
  VALUES (
    conversation_id_param,
    current_user_id,
    content_param
  )
  RETURNING id INTO message_id;
  
  RETURN message_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to mark messages as read
CREATE OR REPLACE FUNCTION mark_messages_as_read(
  conversation_id_param UUID
)
RETURNS INTEGER AS $$
DECLARE
  updated_count INTEGER;
  current_user_id UUID;
BEGIN
  current_user_id := auth.uid();
  
  -- Check if user is authenticated
  IF current_user_id IS NULL THEN
    RAISE EXCEPTION 'User must be authenticated';
  END IF;
  
  -- Check if user is participant in the conversation
  IF NOT EXISTS (
    SELECT 1 FROM public.conversations
    WHERE id = conversation_id_param
    AND (participant_1_id = current_user_id OR participant_2_id = current_user_id)
  ) THEN
    RAISE EXCEPTION 'User is not a participant in this conversation';
  END IF;
  
  -- Mark unread messages as read (only messages not sent by current user)
  UPDATE public.messages
  SET read_at = now()
  WHERE conversation_id = conversation_id_param
    AND sender_id != current_user_id
    AND read_at IS NULL;
  
  GET DIAGNOSTICS updated_count = ROW_COUNT;
  
  RETURN updated_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update conversation last message timestamp
CREATE OR REPLACE FUNCTION update_conversation_last_message()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE public.conversations 
  SET 
    last_message_at = NEW.created_at,
    updated_at = NEW.created_at
  WHERE id = NEW.conversation_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user conversations
CREATE OR REPLACE FUNCTION get_user_conversations()
RETURNS TABLE(
  id UUID,
  other_user_id UUID,
  other_user_name TEXT,
  other_user_avatar TEXT,
  property_id UUID,
  property_title TEXT,
  car_id UUID,
  car_title TEXT,
  last_message_content TEXT,
  last_message_at TIMESTAMP WITH TIME ZONE,
  last_message_sender_id UUID,
  unread_count BIGINT,
  created_at TIMESTAMP WITH TIME ZONE
) AS $$
DECLARE
  current_user_id UUID;
BEGIN
  current_user_id := auth.uid();
  
  -- Check if user is authenticated
  IF current_user_id IS NULL THEN
    RAISE EXCEPTION 'User must be authenticated';
  END IF;
  
  RETURN QUERY
  SELECT 
    c.id,
    CASE 
      WHEN c.participant_1_id = current_user_id THEN c.participant_2_id
      ELSE c.participant_1_id
    END as other_user_id,
    CASE 
      WHEN c.participant_1_id = current_user_id THEN 
        COALESCE(p2.first_name || ' ' || p2.last_name, 'Unknown User')
      ELSE 
        COALESCE(p1.first_name || ' ' || p1.last_name, 'Unknown User')
    END as other_user_name,
    CASE 
      WHEN c.participant_1_id = current_user_id THEN p2.avatar_url
      ELSE p1.avatar_url
    END as other_user_avatar,
    c.property_id,
    prop.title as property_title,
    c.car_id,
    car.title as car_title,
    lm.content as last_message_content,
    c.last_message_at,
    lm.sender_id as last_message_sender_id,
    COALESCE(unread.count, 0) as unread_count,
    c.created_at
  FROM public.conversations c
  LEFT JOIN public.profiles p1 ON c.participant_1_id = p1.id
  LEFT JOIN public.profiles p2 ON c.participant_2_id = p2.id
  LEFT JOIN public.properties prop ON c.property_id = prop.id
  LEFT JOIN public.cars car ON c.car_id = car.id
  LEFT JOIN LATERAL (
    SELECT content, sender_id
    FROM public.messages m
    WHERE m.conversation_id = c.id
    ORDER BY m.created_at DESC
    LIMIT 1
  ) lm ON true
  LEFT JOIN LATERAL (
    SELECT COUNT(*) as count
    FROM public.messages m
    WHERE m.conversation_id = c.id
      AND m.sender_id != current_user_id
      AND m.read_at IS NULL
  ) unread ON true
  WHERE c.participant_1_id = current_user_id OR c.participant_2_id = current_user_id
  ORDER BY c.last_message_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Admin functions

-- Function to create initial super admin
CREATE OR REPLACE FUNCTION create_initial_super_admin(
  admin_email TEXT,
  admin_password TEXT,
  admin_first_name TEXT,
  admin_last_name TEXT
)
RETURNS UUID AS $$
DECLARE
  admin_id UUID;
  password_hash TEXT;
BEGIN
  -- Only allow this if no admin users exist
  IF EXISTS (SELECT 1 FROM public.admin_users LIMIT 1) THEN
    RAISE EXCEPTION 'Admin users already exist. Cannot create initial super admin.';
  END IF;

  -- Hash the password (in production, use proper password hashing)
  password_hash := crypt(admin_password, gen_salt('bf'));

  -- Insert the super admin
  INSERT INTO public.admin_users (email, password_hash, first_name, last_name, role)
  VALUES (admin_email, password_hash, admin_first_name, admin_last_name, 'super_admin')
  RETURNING id INTO admin_id;

  RETURN admin_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to authenticate admin
CREATE OR REPLACE FUNCTION authenticate_admin(
  admin_email TEXT,
  admin_password TEXT
)
RETURNS TABLE(admin_id UUID, session_token TEXT, expires_at TIMESTAMP WITH TIME ZONE) AS $$
DECLARE
  admin_record RECORD;
  new_session_token TEXT;
  session_expires TIMESTAMP WITH TIME ZONE;
BEGIN
  -- Find admin user and verify password
  SELECT au.id, au.password_hash, au.is_active
  INTO admin_record
  FROM public.admin_users au
  WHERE au.email = admin_email;

  -- Check if admin exists and is active
  IF NOT FOUND OR NOT admin_record.is_active THEN
    RAISE EXCEPTION 'Invalid credentials or inactive account';
  END IF;

  -- Verify password
  IF admin_record.password_hash != crypt(admin_password, admin_record.password_hash) THEN
    RAISE EXCEPTION 'Invalid credentials';
  END IF;

  -- Generate session token and expiry
  new_session_token := encode(gen_random_bytes(32), 'base64');
  session_expires := now() + interval '24 hours';

  -- Create session
  INSERT INTO public.admin_sessions (admin_user_id, session_token, expires_at)
  VALUES (admin_record.id, new_session_token, session_expires);

  -- Update last login
  UPDATE public.admin_users
  SET last_login_at = now(), updated_at = now()
  WHERE id = admin_record.id;

  -- Return session info
  RETURN QUERY SELECT admin_record.id, new_session_token, session_expires;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to validate admin session
CREATE OR REPLACE FUNCTION validate_admin_session(token TEXT)
RETURNS TABLE(id UUID, email TEXT, role TEXT) AS $$
BEGIN
  RETURN QUERY
  SELECT au.id, au.email, au.role
  FROM public.admin_users au
  JOIN public.admin_sessions s ON au.id = s.admin_user_id
  WHERE s.session_token = token
  AND s.expires_at > now()
  AND au.is_active = true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to logout admin
CREATE OR REPLACE FUNCTION logout_admin(token TEXT)
RETURNS BOOLEAN AS $$
BEGIN
  DELETE FROM public.admin_sessions WHERE session_token = token;
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Payout functions

-- Function to get host payout requests
CREATE OR REPLACE FUNCTION get_host_payout_requests(p_host_id UUID)
RETURNS TABLE(
  id UUID,
  host_id UUID,
  amount DECIMAL,
  status TEXT,
  notes TEXT,
  admin_notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE,
  payment_method_id UUID,
  processed_at TIMESTAMP WITH TIME ZONE,
  processed_by UUID,
  payment_method JSONB
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    pr.id,
    pr.host_id,
    pr.amount,
    pr.status,
    pr.notes,
    pr.admin_notes,
    pr.created_at,
    pr.updated_at,
    pr.payment_method_id,
    pr.processed_at,
    pr.processed_by,
    jsonb_build_object(
      'provider', hpm.provider,
      'account_id', hpm.account_id,
      'status', hpm.status
    ) as payment_method
  FROM
    payout_requests pr
  LEFT JOIN
    host_payment_methods hpm ON pr.payment_method_id = hpm.id
  WHERE
    pr.host_id = p_host_id
  ORDER BY
    pr.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get payout requests with details (admin view)
CREATE OR REPLACE FUNCTION get_payout_requests_with_details()
RETURNS TABLE(
  id UUID,
  host_id UUID,
  amount DECIMAL,
  status TEXT,
  notes TEXT,
  admin_notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE,
  payment_method_id UUID,
  processed_at TIMESTAMP WITH TIME ZONE,
  processed_by UUID,
  payment_method JSONB,
  host_details JSONB
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    pr.id,
    pr.host_id,
    pr.amount,
    pr.status,
    pr.notes,
    pr.admin_notes,
    pr.created_at,
    pr.updated_at,
    pr.payment_method_id,
    pr.processed_at,
    pr.processed_by,
    jsonb_build_object(
      'id', hpm.id,
      'provider', hpm.provider,
      'account_id', hpm.account_id,
      'status', hpm.status
    ) as payment_method,
    jsonb_build_object(
      'first_name', p.first_name,
      'last_name', p.last_name,
      'avatar_url', p.avatar_url
    ) as host_details
  FROM
    payout_requests pr
  LEFT JOIN
    host_payment_methods hpm ON pr.payment_method_id = hpm.id
  LEFT JOIN
    profiles p ON pr.host_id = p.id
  ORDER BY
    CASE
      WHEN pr.status = 'pending' THEN 1
      WHEN pr.status = 'approved' THEN 2
      ELSE 3
    END,
    pr.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Utility functions

-- Function to get first user ID (for testing)
CREATE OR REPLACE FUNCTION get_first_user_id()
RETURNS UUID AS $$
DECLARE
  first_user_id UUID;
BEGIN
  SELECT id INTO first_user_id FROM auth.users LIMIT 1;
  RETURN first_user_id;
END;
$$ LANGUAGE plpgsql SECURITY INVOKER;

-- Function to schedule checkout reminders (placeholder)
CREATE OR REPLACE FUNCTION schedule_checkout_reminders()
RETURNS VOID AS $$
BEGIN
  -- This function would be called by a cron job every hour
  -- It finds bookings that need checkout reminders (2 hours before checkout)
  -- and calls the send-booking-reminders edge function

  -- For now, this is a placeholder that logs the action
  RAISE NOTICE 'Checkout reminder scheduler executed at %', NOW();

  -- In production, this would make HTTP calls to the edge function
  -- or insert into a queue table for processing
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Note: User preferences functions not in current Supabase project
