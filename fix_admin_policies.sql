-- Fix Admin Policies for Gesco Stay
-- This script adds missing admin policies for hotels and ensures all admin permissions work correctly

-- 1. Add missing admin policies for hotels
CREATE POLICY "Ad<PERSON> can view all hotels" ON public.hotels
  FOR SELECT USING (is_admin(auth.uid()) OR (owner_id = auth.uid()));

CREATE POLICY "Ad<PERSON> can update all hotels" ON public.hotels
  FOR UPDATE USING (is_admin(auth.uid()) OR (owner_id = auth.uid()));

-- 2. Ensure the is_admin function is working correctly
-- (This should already exist, but let's make sure)
CREATE OR REPLACE FUNCTION is_admin(uid UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = uid AND role = 'admin'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. Add admin policies for bookings (so admins can manage all bookings)
CREATE POLICY "Ad<PERSON> can view all bookings" ON public.bookings
  FOR SELECT USING (is_admin(auth.uid()) OR (user_id = auth.uid()));

CREATE POLICY "Ad<PERSON> can update all bookings" ON public.bookings
  FOR UPDATE USING (is_admin(auth.uid()) OR (user_id = auth.uid()));

CREATE POLICY "Admins can view all car bookings" ON public.car_bookings
  FOR SELECT USING (
    is_admin(auth.uid()) OR 
    (auth.uid() = user_id) OR
    (auth.uid() IN (SELECT cars.owner_id FROM cars WHERE cars.id = car_bookings.car_id))
  );

CREATE POLICY "Admins can update all car bookings" ON public.car_bookings
  FOR UPDATE USING (
    is_admin(auth.uid()) OR 
    (auth.uid() = user_id) OR
    (auth.uid() IN (SELECT cars.owner_id FROM cars WHERE cars.id = car_bookings.car_id))
  );

CREATE POLICY "Admins can view all hotel bookings" ON public.hotel_bookings
  FOR SELECT USING (is_admin(auth.uid()) OR (user_id = auth.uid()));

CREATE POLICY "Admins can update all hotel bookings" ON public.hotel_bookings
  FOR UPDATE USING (is_admin(auth.uid()) OR (user_id = auth.uid()));

-- 4. Test the admin function with current admin user
-- Run this to verify the admin user exists and the function works:
-- SELECT id, email, role, is_admin(id) as is_admin_result FROM profiles WHERE email = '<EMAIL>';

-- 5. Grant necessary permissions to authenticated users
GRANT SELECT, UPDATE ON public.properties TO authenticated;
GRANT SELECT, UPDATE ON public.cars TO authenticated;
GRANT SELECT, UPDATE ON public.hotels TO authenticated;
GRANT SELECT, UPDATE ON public.bookings TO authenticated;
GRANT SELECT, UPDATE ON public.car_bookings TO authenticated;
GRANT SELECT, UPDATE ON public.hotel_bookings TO authenticated;

-- 6. Verification queries (uncomment to test)
-- Check if admin policies are working:
-- SELECT 'Properties' as table_name, COUNT(*) as total FROM properties;
-- SELECT 'Cars' as table_name, COUNT(*) as total FROM cars;
-- SELECT 'Hotels' as table_name, COUNT(*) as total FROM hotels;

-- Test admin function:
-- SELECT email, role, is_admin(id) as admin_check FROM profiles WHERE role = 'admin';

-- Test update permissions (replace with actual listing ID):
-- UPDATE properties SET status = 'approved' WHERE id = 1;
-- UPDATE cars SET status = 'approved' WHERE id = 1;
-- UPDATE hotels SET status = 'approved' WHERE id = 1;
