// Booking continuation utility to handle auth flow interruptions

export interface BookingIntent {
  type: 'property' | 'car';
  id: string;
  checkIn?: string;
  checkOut?: string;
  startDate?: string;
  endDate?: string;
  guests?: number;
  durationType?: string;
  totalPrice?: number;
  returnUrl?: string;
}

const BOOKING_INTENT_KEY = 'gesco_booking_intent';

export const saveBookingIntent = (intent: BookingIntent): void => {
  try {
    localStorage.setItem(BOOKING_INTENT_KEY, JSON.stringify(intent));
  } catch (error) {
    console.error('Failed to save booking intent:', error);
  }
};

export const getBookingIntent = (): BookingIntent | null => {
  try {
    const stored = localStorage.getItem(BOOKING_INTENT_KEY);
    return stored ? JSON.parse(stored) : null;
  } catch (error) {
    console.error('Failed to get booking intent:', error);
    return null;
  }
};

export const clearBookingIntent = (): void => {
  try {
    localStorage.removeItem(BOOKING_INTENT_KEY);
  } catch (error) {
    console.error('Failed to clear booking intent:', error);
  }
};

export const hasBookingIntent = (): boolean => {
  return getBookingIntent() !== null;
};
