import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import Stripe from "https://esm.sh/stripe@14.21.0";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.45.0";

const SUPABASE_URL = Deno.env.get("SUPABASE_URL") || "";
const SUPABASE_ANON_KEY = Deno.env.get("SUPABASE_ANON_KEY") || "";
const SUPABASE_SERVICE_ROLE_KEY =
  Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") || "";
const STRIPE_SECRET_KEY = Deno.env.get("STRIPE_SECRET_KEY") || "";

const stripe = new Stripe(STRIPE_SECRET_KEY, {
  apiVersion: "2023-10-16",
});

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const {
      bookingId,
      carBookingId,
      propertyId,
      amount,
      returnUrl,
      userEmail,
    } = await req.json();

    // Determine if this is a car booking or property booking
    const isCarBooking = !!carBookingId;
    const finalBookingId = carBookingId || bookingId;

    console.log("Processing payment for:", {
      isCarBooking,
      carBookingId,
      bookingId,
      finalBookingId,
      amount,
      returnUrl,
    });

    // Create Supabase client
    const supabaseClient = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
    const supabaseAdmin = createClient(
      SUPABASE_URL,
      SUPABASE_SERVICE_ROLE_KEY,
      {
        auth: { persistSession: false },
      }
    );

    // Get auth header
    const authHeader = req.headers.get("Authorization");
    if (!authHeader) {
      throw new Error("Missing Authorization header");
    }

    const token = authHeader.replace("Bearer ", "");
    const {
      data: { user },
      error: userError,
    } = await supabaseClient.auth.getUser(token);

    if (userError || !user) {
      throw new Error("Unauthorized");
    }

    console.log("Creating Stripe checkout session");

    console.log("Creating Stripe session for booking:", finalBookingId);
    console.log("User email:", userEmail || user.email);
    console.log("Amount:", amount);

    // Validate amount
    if (!amount || amount <= 0) {
      throw new Error("Invalid amount: " + amount);
    }

    // Validate required fields
    if (!finalBookingId) {
      throw new Error("Missing booking ID");
    }

    // Email is optional for mobile-only users
    // if (!user?.email && !userEmail) {
    //   throw new Error("Missing customer email");
    // }

    // Create Stripe checkout session with Gesco Stay branding
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ["card"],
      line_items: [
        {
          price_data: {
            currency: "usd",
            product_data: {
              name: isCarBooking
                ? "Car Rental - Gesco Stay"
                : "Property Booking - Gesco Stay",
              description: isCarBooking
                ? `Car rental booking with Gesco Stay. Booking ID: ${finalBookingId}`
                : `Property booking with Gesco Stay. Booking ID: ${finalBookingId}`,
            },
            unit_amount: Math.round(amount * 100), // Convert to cents
          },
          quantity: 1,
        },
      ],
      mode: "payment",
      success_url: `${returnUrl}/payment-success?session_id={CHECKOUT_SESSION_ID}&booking_id=${finalBookingId}${
        isCarBooking ? "&car_booking=true" : ""
      }`,
      cancel_url: `${returnUrl}/payment-cancelled?booking_id=${finalBookingId}${
        isCarBooking ? "&car_booking=true" : ""
      }`,
      customer_email: userEmail || user.email || undefined, // Don't pass empty string
      billing_address_collection: "auto", // Auto collection for better UX
      locale: "en", // Explicitly set locale to fix the module error
      custom_text: {
        submit: {
          message:
            "Complete your booking with Gesco Stay - Your trusted travel partner",
        },
      },
      metadata: {
        booking_id: finalBookingId,
        booking_type: isCarBooking ? "car" : "property",
        company: "Gesco Stay",
        service_type: isCarBooking ? "Car Rental" : "Property Booking",
      },
    });

    // Log payment attempt
    await supabaseAdmin.from("payment_logs").insert({
      booking_id: finalBookingId,
      payment_method: "stripe",
      amount,
      status: "pending",
      transaction_id: session.id,
      provider_response: { session_id: session.id },
    });

    // Update booking with payment information based on booking type
    if (isCarBooking) {
      await supabaseAdmin
        .from("car_bookings")
        .update({
          payment_method: "stripe",
          payment_status: "pending",
          stripe_session_id: session.id,
        })
        .eq("id", finalBookingId);
    } else {
      await supabaseAdmin
        .from("bookings")
        .update({
          payment_method: "stripe",
          payment_status: "pending",
          stripe_session_id: session.id,
        })
        .eq("id", finalBookingId);
    }

    return new Response(
      JSON.stringify({
        success: true,
        sessionId: session.id,
        sessionUrl: session.url,
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200,
      }
    );
  } catch (error) {
    console.error("Error processing Stripe payment:", error);
    console.error("Error details:", {
      message: error.message,
      type: error.type,
      code: error.code,
      stack: error.stack,
    });

    return new Response(
      JSON.stringify({
        success: false,
        error: error.message,
        details: error.type || "Unknown error",
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 500,
      }
    );
  }
});
