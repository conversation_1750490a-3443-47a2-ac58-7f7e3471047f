-- Update ID Schema to Short Numeric IDs
-- This file updates all tables to use short numeric IDs instead of UUIDs

-- Step 1: Create backup tables
CREATE TABLE bookings_uuid_backup AS SELECT * FROM bookings;
CREATE TABLE car_bookings_uuid_backup AS SELECT * FROM car_bookings;
CREATE TABLE hotel_bookings_uuid_backup AS SELECT * FROM hotel_bookings;
CREATE TABLE properties_uuid_backup AS SELECT * FROM properties;
CREATE TABLE cars_uuid_backup AS SELECT * FROM cars;
CREATE TABLE hotels_uuid_backup AS SELECT * FROM hotels;
CREATE TABLE room_types_uuid_backup AS SELECT * FROM room_types;
CREATE TABLE reviews_uuid_backup AS SELECT * FROM reviews;
CREATE TABLE conversations_uuid_backup AS SELECT * FROM conversations;
CREATE TABLE messages_uuid_backup AS SELECT * FROM messages;

-- Step 2: Drop existing foreign key constraints
ALTER TABLE bookings DROP CONSTRAINT IF EXISTS bookings_property_id_fkey;
ALTER TABLE bookings DROP CONSTRAINT IF EXISTS bookings_user_id_fkey;
ALTER TABLE car_bookings DROP CONSTRAINT IF EXISTS car_bookings_car_id_fkey;
ALTER TABLE car_bookings DROP CONSTRAINT IF EXISTS car_bookings_user_id_fkey;
ALTER TABLE hotel_bookings DROP CONSTRAINT IF EXISTS hotel_bookings_hotel_id_fkey;
ALTER TABLE hotel_bookings DROP CONSTRAINT IF EXISTS hotel_bookings_room_type_id_fkey;
ALTER TABLE hotel_bookings DROP CONSTRAINT IF EXISTS hotel_bookings_user_id_fkey;
ALTER TABLE room_types DROP CONSTRAINT IF EXISTS room_types_hotel_id_fkey;
ALTER TABLE reviews DROP CONSTRAINT IF EXISTS reviews_property_id_fkey;
ALTER TABLE reviews DROP CONSTRAINT IF EXISTS reviews_car_id_fkey;
ALTER TABLE reviews DROP CONSTRAINT IF EXISTS reviews_hotel_id_fkey;
ALTER TABLE reviews DROP CONSTRAINT IF EXISTS reviews_user_id_fkey;
ALTER TABLE conversations DROP CONSTRAINT IF EXISTS conversations_participant_1_id_fkey;
ALTER TABLE conversations DROP CONSTRAINT IF EXISTS conversations_participant_2_id_fkey;
ALTER TABLE conversations DROP CONSTRAINT IF EXISTS conversations_property_id_fkey;
ALTER TABLE conversations DROP CONSTRAINT IF EXISTS conversations_car_id_fkey;
ALTER TABLE conversations DROP CONSTRAINT IF EXISTS conversations_hotel_id_fkey;
ALTER TABLE messages DROP CONSTRAINT IF EXISTS messages_conversation_id_fkey;
ALTER TABLE messages DROP CONSTRAINT IF EXISTS messages_sender_id_fkey;

-- Step 3: Drop existing tables
DROP TABLE IF EXISTS bookings CASCADE;
DROP TABLE IF EXISTS car_bookings CASCADE;
DROP TABLE IF EXISTS hotel_bookings CASCADE;
DROP TABLE IF EXISTS properties CASCADE;
DROP TABLE IF EXISTS cars CASCADE;
DROP TABLE IF EXISTS hotels CASCADE;
DROP TABLE IF EXISTS room_types CASCADE;
DROP TABLE IF EXISTS reviews CASCADE;
DROP TABLE IF EXISTS conversations CASCADE;
DROP TABLE IF EXISTS messages CASCADE;

-- Step 4: Create new tables with BIGINT IDs

-- Properties table with BIGINT ID
CREATE TABLE properties (
  id BIGINT PRIMARY KEY DEFAULT generate_unique_property_id(),
  owner_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  location TEXT NOT NULL,
  formatted_address TEXT,
  latitude DECIMAL(10, 8),
  longitude DECIMAL(11, 8),
  price DECIMAL(10, 2) NOT NULL CHECK (price > 0),
  beds INTEGER NOT NULL CHECK (beds > 0),
  baths INTEGER NOT NULL CHECK (baths > 0),
  images TEXT[] NOT NULL DEFAULT '{}',
  features TEXT[] DEFAULT '{}',
  status TEXT DEFAULT 'approved' CHECK (status IN ('pending', 'approved', 'rejected')),
  is_dummy BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Cars table with BIGINT ID
CREATE TABLE cars (
  id BIGINT PRIMARY KEY DEFAULT generate_unique_property_id(),
  owner_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  car_type TEXT NOT NULL,
  make TEXT NOT NULL,
  model TEXT NOT NULL,
  year INTEGER NOT NULL CHECK (year > 1900 AND year <= EXTRACT(YEAR FROM CURRENT_DATE) + 1),
  location TEXT NOT NULL,
  formatted_address TEXT,
  latitude DECIMAL(10, 8),
  longitude DECIMAL(11, 8),
  images TEXT[] NOT NULL DEFAULT '{}',
  price_day DECIMAL(10, 2) NOT NULL CHECK (price_day > 0),
  price_week DECIMAL(10, 2) NOT NULL CHECK (price_week > 0),
  price_month DECIMAL(10, 2) NOT NULL CHECK (price_month > 0),
  seats INTEGER NOT NULL CHECK (seats > 0),
  transmission TEXT NOT NULL,
  fuel_type TEXT NOT NULL,
  status TEXT DEFAULT 'approved' CHECK (status IN ('pending', 'approved', 'rejected')),
  is_dummy BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Hotels table with BIGINT ID
CREATE TABLE hotels (
  id BIGINT PRIMARY KEY DEFAULT generate_unique_property_id(),
  title VARCHAR(255) NOT NULL,
  description TEXT NOT NULL,
  location VARCHAR(255) NOT NULL,
  formatted_address TEXT,
  latitude DECIMAL(10, 8),
  longitude DECIMAL(11, 8),
  owner_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  images TEXT[] DEFAULT '{}',
  amenities TEXT[] DEFAULT '{}',
  policies JSONB DEFAULT '{}',
  check_in_time TIME DEFAULT '15:00:00',
  check_out_time TIME DEFAULT '11:00:00',
  status TEXT DEFAULT 'approved' CHECK (status IN ('pending', 'approved', 'rejected')),
  is_dummy BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Room types table with BIGINT ID
CREATE TABLE room_types (
  id BIGINT PRIMARY KEY DEFAULT generate_unique_room_type_id(),
  hotel_id BIGINT NOT NULL REFERENCES hotels(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  price_per_night DECIMAL(10, 2) NOT NULL CHECK (price_per_night > 0),
  max_occupancy INTEGER NOT NULL CHECK (max_occupancy > 0),
  amenities TEXT[] DEFAULT '{}',
  images TEXT[] DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Bookings table with BIGINT ID
CREATE TABLE bookings (
  id BIGINT PRIMARY KEY DEFAULT generate_unique_booking_id(),
  property_id BIGINT REFERENCES properties(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  check_in DATE NOT NULL,
  check_out DATE NOT NULL,
  total_price DECIMAL(10, 2) NOT NULL CHECK (total_price > 0),
  status TEXT NOT NULL DEFAULT 'pending',
  payment_method TEXT,
  payment_status TEXT DEFAULT 'pending',
  payment_id TEXT,
  stripe_session_id TEXT,
  wave_checkout_id TEXT,
  wave_transaction_id TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  CHECK (check_out > check_in)
);

-- Car bookings table with BIGINT ID
CREATE TABLE car_bookings (
  id BIGINT PRIMARY KEY DEFAULT generate_unique_booking_id(),
  car_id BIGINT NOT NULL REFERENCES cars(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  start_date DATE NOT NULL,
  end_date DATE NOT NULL,
  duration_type TEXT NOT NULL,
  total_price DECIMAL(10, 2) NOT NULL CHECK (total_price > 0),
  status TEXT NOT NULL DEFAULT 'pending',
  payment_method TEXT,
  payment_status TEXT,
  payment_id TEXT,
  stripe_session_id TEXT,
  wave_checkout_id TEXT,
  wave_transaction_id TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  CHECK (end_date > start_date)
);

-- Hotel bookings table with BIGINT ID
CREATE TABLE hotel_bookings (
  id BIGINT PRIMARY KEY DEFAULT generate_unique_booking_id(),
  hotel_id BIGINT NOT NULL REFERENCES hotels(id) ON DELETE CASCADE,
  room_type_id BIGINT REFERENCES room_types(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  check_in DATE NOT NULL,
  check_out DATE NOT NULL,
  adults INTEGER NOT NULL DEFAULT 1 CHECK (adults > 0),
  children INTEGER DEFAULT 0 CHECK (children >= 0),
  rooms_count INTEGER DEFAULT 1 CHECK (rooms_count > 0),
  total_price DECIMAL(10, 2) NOT NULL CHECK (total_price > 0),
  status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'cancelled', 'completed')),
  payment_status VARCHAR(20) DEFAULT 'pending' CHECK (payment_status IN ('pending', 'paid', 'failed', 'refunded')),
  payment_method VARCHAR(50),
  payment_id VARCHAR(255),
  stripe_session_id VARCHAR(255),
  special_requests TEXT,
  guest_details JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  CHECK (check_out > check_in),
  CHECK (check_in >= CURRENT_DATE)
);

-- Reviews table with BIGINT ID
CREATE TABLE reviews (
  id BIGINT PRIMARY KEY DEFAULT generate_unique_review_id(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  property_id BIGINT REFERENCES properties(id) ON DELETE CASCADE,
  car_id BIGINT REFERENCES cars(id) ON DELETE CASCADE,
  hotel_id BIGINT REFERENCES hotels(id) ON DELETE CASCADE,
  rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
  comment TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  CHECK (
    (property_id IS NOT NULL AND car_id IS NULL AND hotel_id IS NULL) OR
    (property_id IS NULL AND car_id IS NOT NULL AND hotel_id IS NULL) OR
    (property_id IS NULL AND car_id IS NULL AND hotel_id IS NOT NULL)
  )
);

-- Conversations table with BIGINT ID
CREATE TABLE conversations (
  id BIGINT PRIMARY KEY DEFAULT generate_unique_conversation_id(),
  participant_1_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  participant_2_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  property_id BIGINT REFERENCES properties(id) ON DELETE SET NULL,
  car_id BIGINT REFERENCES cars(id) ON DELETE SET NULL,
  hotel_id BIGINT REFERENCES hotels(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  CHECK (participant_1_id != participant_2_id)
);

-- Messages table with BIGINT ID
CREATE TABLE messages (
  id BIGINT PRIMARY KEY DEFAULT generate_unique_message_id(),
  conversation_id BIGINT NOT NULL REFERENCES conversations(id) ON DELETE CASCADE,
  sender_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create indexes for performance
CREATE INDEX idx_properties_owner_id ON properties(owner_id);
CREATE INDEX idx_properties_status ON properties(status);
CREATE INDEX idx_properties_location ON properties(location);
CREATE INDEX idx_properties_is_dummy ON properties(is_dummy);

CREATE INDEX idx_cars_owner_id ON cars(owner_id);
CREATE INDEX idx_cars_status ON cars(status);
CREATE INDEX idx_cars_location ON cars(location);
CREATE INDEX idx_cars_is_dummy ON cars(is_dummy);

CREATE INDEX idx_hotels_owner_id ON hotels(owner_id);
CREATE INDEX idx_hotels_status ON hotels(status);
CREATE INDEX idx_hotels_location ON hotels(location);
CREATE INDEX idx_hotels_is_dummy ON hotels(is_dummy);

CREATE INDEX idx_bookings_user_id ON bookings(user_id);
CREATE INDEX idx_bookings_property_id ON bookings(property_id);
CREATE INDEX idx_bookings_status ON bookings(status);
CREATE INDEX idx_bookings_check_in ON bookings(check_in);
CREATE INDEX idx_bookings_check_out ON bookings(check_out);

CREATE INDEX idx_car_bookings_user_id ON car_bookings(user_id);
CREATE INDEX idx_car_bookings_car_id ON car_bookings(car_id);
CREATE INDEX idx_car_bookings_status ON car_bookings(status);
CREATE INDEX idx_car_bookings_dates ON car_bookings(start_date, end_date);

CREATE INDEX idx_hotel_bookings_user_id ON hotel_bookings(user_id);
CREATE INDEX idx_hotel_bookings_hotel_id ON hotel_bookings(hotel_id);
CREATE INDEX idx_hotel_bookings_room_type_id ON hotel_bookings(room_type_id);
CREATE INDEX idx_hotel_bookings_status ON hotel_bookings(status);
CREATE INDEX idx_hotel_bookings_dates ON hotel_bookings(check_in, check_out);

CREATE INDEX idx_conversations_participants ON conversations(participant_1_id, participant_2_id);
CREATE INDEX idx_messages_conversation_id ON messages(conversation_id);
CREATE INDEX idx_messages_sender_id ON messages(sender_id);
