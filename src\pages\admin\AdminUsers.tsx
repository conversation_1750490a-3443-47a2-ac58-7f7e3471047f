
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableCaption, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { format } from "date-fns";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle,
  DialogTrigger,
  DialogClose,
  DialogFooter
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import type { Database } from "@/integrations/supabase/types";

// Define the UserProfile interface correctly
type UserProfile = Database['public']['Tables']['profiles']['Row'] & {
  bookings?: { total_price: number; created_at: string }[];
}

const AdminUsers = () => {
  const [users, setUsers] = useState<UserProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [editOpen, setEditOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<UserProfile | null>(null);
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [avatarUrl, setAvatarUrl] = useState("");
  const [bookingsByUser, setBookingsByUser] = useState<{ [userId: string]: any[] }>({});

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*');

      if (error) throw error;

      const usersWithBookings = await Promise.all(
        data.map(async (user) => {
          const { data: bookings, error: bookingError } = await supabase
            .from('bookings')
            .select('total_price, created_at')
            .eq('user_id', user.id);

          if (bookingError) {
            console.error(`Error fetching bookings for user ${user.id}:`, bookingError);
            return { ...user, bookings: [] };
          }

          return { ...user, bookings: bookings || [] };
        })
      );

      setUsers(usersWithBookings);

      // Organize bookings by user ID
      const bookingsMap: { [userId: string]: any[] } = {};
      usersWithBookings.forEach(user => {
        bookingsMap[user.id] = user.bookings || [];
      });
      setBookingsByUser(bookingsMap);

    } catch (error: any) {
      toast.error("Failed to fetch users: " + error.message);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM dd, yyyy');
    } catch (error) {
      return dateString || 'N/A';
    }
  };

  const editUser = (user: UserProfile) => {
    setSelectedUser(user);
    setFirstName(user.first_name || "");
    setLastName(user.last_name || "");
    setAvatarUrl(user.avatar_url || "");
    setEditOpen(true);
  };

  const updateUser = async () => {
    if (!selectedUser) return;

    try {
      const { error } = await supabase
        .from('profiles')
        .update({
          first_name: firstName,
          last_name: lastName,
          avatar_url: avatarUrl,
          updated_at: new Date().toISOString(),
        })
        .eq('id', selectedUser.id);

      if (error) throw error;

      toast.success("User updated successfully");
      setEditOpen(false);
      fetchUsers();
    } catch (error: any) {
      toast.error("Failed to update user: " + error.message);
    }
  };

  const renderBookingStats = (userId: string) => {
    const userBookings = bookingsByUser[userId] || [];
    const totalSpent = userBookings.reduce((sum, booking) => sum + (booking?.total_price || 0), 0);
    
    return (
      <div className="text-sm">
        <p><span className="font-medium">Total Bookings:</span> {userBookings.length}</p>
        <p><span className="font-medium">Total Spent:</span> ${totalSpent}</p>
        {userBookings.length > 0 && userBookings[0] && (
          <p><span className="font-medium">Latest Booking:</span> {formatDate(userBookings[0].created_at || '')}</p>
        )}
      </div>
    );
  };

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-6">Manage Users</h1>
      
      {loading ? (
        <div className="flex justify-center p-8">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-800"></div>
        </div>
      ) : (
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>User</TableHead>
                <TableHead>Name</TableHead>
                <TableHead>Role</TableHead>
                <TableHead>Bookings</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {users.map((user) => (
                <TableRow key={user.id}>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Avatar>
                        <AvatarImage src={user.avatar_url} alt={user.first_name || "User"} />
                        <AvatarFallback>{user.first_name?.charAt(0) || user.last_name?.charAt(0) || "U"}</AvatarFallback>
                      </Avatar>
                      <span>{user.id}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    {user.first_name} {user.last_name}
                  </TableCell>
                  <TableCell>
                    <Badge variant="secondary">{user.role}</Badge>
                  </TableCell>
                  <TableCell>
                    {renderBookingStats(user.id)}
                  </TableCell>
                  <TableCell className="text-right">
                    <Button variant="outline" size="sm" onClick={() => editUser(user)}>
                      Edit
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}

      {/* Edit User Dialog */}
      <Dialog open={editOpen} onOpenChange={setEditOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Edit User</DialogTitle>
            <DialogDescription>
              Make changes to the user profile here. Click save when you're done.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="firstName" className="text-right">
                First Name
              </Label>
              <Input id="firstName" value={firstName} onChange={(e) => setFirstName(e.target.value)} className="col-span-3" />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="lastName" className="text-right">
                Last Name
              </Label>
              <Input id="lastName" value={lastName} onChange={(e) => setLastName(e.target.value)} className="col-span-3" />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="avatarUrl" className="text-right">
                Avatar URL
              </Label>
              <Input id="avatarUrl" value={avatarUrl} onChange={(e) => setAvatarUrl(e.target.value)} className="col-span-3" />
            </div>
          </div>
          <DialogFooter>
            <Button type="submit" onClick={updateUser}>Save changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AdminUsers;
