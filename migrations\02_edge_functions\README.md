# Edge Functions Migration

This directory contains all the edge functions from the Gesco Stay application that need to be deployed to the new Supabase project.

## Functions Overview

### 1. send-booking-confirmation

- **Purpose**: Sends email confirmations for bookings
- **Trigger**: Called after successful booking creation/payment
- **Dependencies**: AWS SES for email sending (EU-West-2 region)
- **JWT Verification**: Required
- **Email Types**:
  - Booking confirmations (from: <EMAIL>)
  - Status updates (from: <EMAIL>)
  - Reminders (from: <EMAIL>)
  - Checkout reminders (from: <EMAIL>)

### 2. send-booking-reminders

- **Purpose**: Sends reminder emails for upcoming bookings
- **Trigger**: Scheduled or manual trigger
- **Dependencies**: AWS SES for email sending (EU-West-2 region)
- **JWT Verification**: Not required (system function)
- **Email From**: <EMAIL>

### 3. process-stripe-payment

- **Purpose**: Creates Stripe checkout sessions for payments
- **Trigger**: Called when user initiates payment
- **Dependencies**: Stripe API
- **JWT Verification**: Required

### 4. verify-stripe-payment

- **Purpose**: Verifies and processes Stripe webhook events
- **Trigger**: Stripe webhook
- **Dependencies**: Stripe API
- **JWT Verification**: Required

### 5. process-wave-payment

- **Purpose**: Handles Wave payment processing
- **Trigger**: Called when user chooses Wave payment
- **Dependencies**: Wave API
- **JWT Verification**: Required

### 6. verify-wave-payment

- **Purpose**: Verifies Wave payment webhooks
- **Trigger**: Wave webhook
- **Dependencies**: Wave API
- **JWT Verification**: Required

## Deployment Instructions

### Prerequisites

1. Supabase CLI installed and configured
2. New project created and configured
3. Environment variables set in Supabase dashboard

### Environment Variables Required

Set these in your Supabase project dashboard under Settings > Edge Functions > Environment Variables:

```bash
# Supabase
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# AWS SES (for email functions)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=eu-west-2

# SES Email Addresses for different purposes
SES_BOOKING_EMAIL=<EMAIL>
SES_NOREPLY_EMAIL=<EMAIL>
SES_SUPPORT_EMAIL=<EMAIL>
SES_NOTIFICATIONS_EMAIL=<EMAIL>

# Stripe (for payment functions)
STRIPE_SECRET_KEY=sk_test_... (or sk_live_...)
STRIPE_WEBHOOK_SECRET=whsec_...

# Wave (for Wave payment functions)
WAVE_API_KEY=your-wave-api-key
```

## Email Configuration

The email system uses different sender addresses based on the email type:

- **Booking Confirmations & Status Updates**: `<EMAIL>`
- **Reminders & Checkout Notifications**: `<EMAIL>`
- **General/System Emails**: `<EMAIL>`
- **Support Communications**: `<EMAIL>`

All emails are sent through AWS SES in the EU-West-2 region for optimal delivery performance in Europe.

### Manual Deployment

Deploy each function individually:

```bash
# Deploy booking confirmation function
supabase functions deploy send-booking-confirmation --project-ref YOUR_PROJECT_ID

# Deploy booking reminders function
supabase functions deploy send-booking-reminders --project-ref YOUR_PROJECT_ID

# Deploy Stripe payment functions
supabase functions deploy process-stripe-payment --project-ref YOUR_PROJECT_ID
supabase functions deploy verify-stripe-payment --project-ref YOUR_PROJECT_ID

# Deploy Wave payment functions
supabase functions deploy process-wave-payment --project-ref YOUR_PROJECT_ID
supabase functions deploy verify-wave-payment --project-ref YOUR_PROJECT_ID
```

### Automated Deployment

Use the migration script:

```bash
# Linux/Mac
./migrations/05_scripts/migrate.sh

# Windows
./migrations/05_scripts/migrate.ps1
```

## Function Configuration

The functions are configured in `config.toml`:

```toml
[functions]
[functions.send-booking-confirmation]
verify_jwt = true

[functions.send-booking-reminders]
verify_jwt = false

[functions.verify-stripe-payment]
verify_jwt = true

[functions.process-stripe-payment]
verify_jwt = true

[functions.process-wave-payment]
verify_jwt = true

[functions.verify-wave-payment]
verify_jwt = true
```

## Testing Functions

After deployment, test each function:

### Test Booking Confirmation

```bash
curl -X POST https://your-project-id.supabase.co/functions/v1/send-booking-confirmation \
  -H "Authorization: Bearer YOUR_ANON_KEY" \
  -H "Content-Type: application/json" \
  -d '{"bookingId": "test-booking-id", "type": "confirmation"}'
```

### Test Stripe Payment

```bash
curl -X POST https://your-project-id.supabase.co/functions/v1/process-stripe-payment \
  -H "Authorization: Bearer YOUR_USER_JWT" \
  -H "Content-Type: application/json" \
  -d '{"bookingId": "test", "amount": 100, "returnUrl": "https://yoursite.com"}'
```

## Webhook Configuration

### Stripe Webhooks

1. Go to Stripe Dashboard > Webhooks
2. Add endpoint: `https://your-project-id.supabase.co/functions/v1/verify-stripe-payment`
3. Select events: `checkout.session.completed`, `payment_intent.succeeded`
4. Copy webhook secret to environment variables

### Wave Webhooks

1. Configure Wave webhook endpoint: `https://your-project-id.supabase.co/functions/v1/verify-wave-payment`
2. Set up required events in Wave dashboard
3. Copy webhook secret to environment variables

## Troubleshooting

### Common Issues

1. **Environment variables not set**: Check Supabase dashboard settings
2. **CORS errors**: Verify function CORS headers
3. **JWT verification failures**: Check token validity and function configuration
4. **Email sending failures**: Verify AWS SES configuration and credentials
5. **Payment processing errors**: Check Stripe/Wave API keys and webhook secrets

### Logs

View function logs in Supabase dashboard:

1. Go to Edge Functions
2. Select function
3. View Logs tab

### Local Development

Test functions locally:

```bash
supabase functions serve --env-file .env.local
```

## Security Notes

1. **Never expose service role keys** in client-side code
2. **Use environment variables** for all sensitive data
3. **Validate all inputs** in function code
4. **Implement proper error handling** to avoid information leakage
5. **Monitor function logs** for suspicious activity

## Migration Checklist

- [ ] All functions deployed successfully
- [ ] Environment variables configured
- [ ] Webhook endpoints updated
- [ ] Functions tested with real data
- [ ] Error handling verified
- [ ] Logs monitored for issues
- [ ] Performance benchmarked
