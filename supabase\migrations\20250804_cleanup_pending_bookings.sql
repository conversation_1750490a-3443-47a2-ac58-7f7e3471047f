-- Cleanup old pending bookings that were never completed
-- This prevents the database from accumulating abandoned bookings

-- Function to cleanup old pending bookings (older than 1 hour)
CREATE OR REPLACE FUNCTION cleanup_old_pending_bookings()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER := 0;
  temp_count INTEGER;
BEGIN
  -- Delete old pending property bookings
  DELETE FROM bookings
  WHERE status = 'pending'
  AND payment_status = 'pending'
  AND created_at < NOW() - INTERVAL '1 hour';

  GET DIAGNOSTICS temp_count = ROW_COUNT;
  deleted_count := deleted_count + temp_count;

  -- Delete old pending car bookings
  DELETE FROM car_bookings
  WHERE status = 'pending'
  AND payment_status = 'pending'
  AND created_at < NOW() - INTERVAL '1 hour';

  GET DIAGNOSTICS temp_count = ROW_COUNT;
  deleted_count := deleted_count + temp_count;

  -- Delete old pending hotel bookings
  DELETE FROM hotel_bookings
  WHERE status = 'pending'
  AND payment_status = 'pending'
  AND created_at < NOW() - INTERVAL '1 hour';

  GET DIAGNOSTICS temp_count = ROW_COUNT;
  deleted_count := deleted_count + temp_count;

  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION cleanup_old_pending_bookings() TO authenticated;

-- Run the cleanup function once to clean existing old bookings
SELECT cleanup_old_pending_bookings() as cleaned_bookings_count;

-- Note: In production, you might want to set up a cron job or scheduled function
-- to run this cleanup periodically (e.g., every hour)
-- For now, this can be run manually or called from the application when needed
