import {
  DollarSign,
  CalendarDays,
  CheckCircle2,
  BanknoteIcon,
} from "lucide-react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

interface EarningsStatsProps {
  totalEarnings: number;
  pendingPayouts: number;
  completedPayouts: number;
  availableBalance: number;
  onRequestPayout: () => void;
}

const EarningsStats = ({
  totalEarnings,
  pendingPayouts,
  completedPayouts,
  availableBalance,
  onRequestPayout,
}: EarningsStatsProps) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium flex items-center">
            <DollarSign className="mr-2 h-4 w-4" />
            Total Earnings
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">${totalEarnings.toFixed(2)}</div>
          <p className="text-xs text-muted-foreground">
            All-time earnings (after fees)
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium flex items-center">
            <CalendarDays className="mr-2 h-4 w-4" />
            Pending Payouts
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">${pendingPayouts.toFixed(2)}</div>
          <p className="text-xs text-muted-foreground">
            To be transferred soon
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium flex items-center">
            <CheckCircle2 className="mr-2 h-4 w-4" />
            Completed Payouts
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            ${completedPayouts.toFixed(2)}
          </div>
          <p className="text-xs text-muted-foreground">
            Successfully transferred
          </p>
        </CardContent>
      </Card>

      <Card className="bg-gradient-to-br from-muted-teal/10 to-secondary/10 border-muted-teal/20">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium flex items-center">
            <BanknoteIcon className="mr-2 h-4 w-4" />
            Available Balance
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-muted-teal">
            ${availableBalance.toFixed(2)}
          </div>
          <div className="flex justify-between mt-2">
            <Button
              variant="default"
              size="sm"
              className="text-xs"
              onClick={onRequestPayout}
            >
              Request Payout
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default EarningsStats;
