# User Registration Setup for Supabase

Since Supabase doesn't allow creating triggers on the `auth.users` table, you need to handle user profile creation through alternative methods.

## Option 1: Database Webhooks (Recommended)

### Setup Database Webhook in Supabase Dashboard

1. Go to your Supabase project dashboard
2. Navigate to **Database** > **Webhooks**
3. Click **Create a new webhook**
4. Configure the webhook:
   - **Name**: `create_user_profile`
   - **Table**: `auth.users`
   - **Events**: `INSERT`
   - **Type**: `HTTP Request`
   - **HTTP Method**: `POST`
   - **URL**: `https://your-project-id.supabase.co/functions/v1/create-user-profile`
   - **HTTP Headers**: 
     ```
     Authorization: Bearer YOUR_SERVICE_ROLE_KEY
     Content-Type: application/json
     ```

### Create Edge Function for User Profile Creation

Create a new edge function `create-user-profile`:

```typescript
import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.45.0";

const SUPABASE_URL = Deno.env.get("SUPABASE_URL") || "";
const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") || "";

serve(async (req) => {
  try {
    const payload = await req.json();
    const { record } = payload;
    
    if (!record || !record.id) {
      throw new Error("Invalid webhook payload");
    }

    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

    // Create user profile
    const { error } = await supabase.rpc('handle_new_user_manual', {
      user_id: record.id,
      first_name: record.raw_user_meta_data?.first_name || '',
      last_name: record.raw_user_meta_data?.last_name || '',
      email: record.email || '',
      phone_number: record.phone || record.raw_user_meta_data?.phone_number || ''
    });

    if (error) {
      console.error("Error creating user profile:", error);
      throw error;
    }

    return new Response(
      JSON.stringify({ success: true, message: "User profile created" }),
      { headers: { "Content-Type": "application/json" }, status: 200 }
    );
  } catch (error) {
    console.error("Webhook error:", error);
    return new Response(
      JSON.stringify({ success: false, error: error.message }),
      { headers: { "Content-Type": "application/json" }, status: 500 }
    );
  }
});
```

## Option 2: Client-Side Profile Creation

Handle profile creation in your frontend code after user registration:

```typescript
// In your registration component
import { supabase } from '@/integrations/supabase/client';

const handleSignUp = async (userData) => {
  try {
    // Sign up the user
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: userData.email,
      password: userData.password,
      options: {
        data: {
          first_name: userData.firstName,
          last_name: userData.lastName,
          phone_number: userData.phoneNumber,
        }
      }
    });

    if (authError) throw authError;

    // Create profile manually if user is immediately available
    if (authData.user) {
      const { error: profileError } = await supabase.rpc('handle_new_user_manual', {
        user_id: authData.user.id,
        first_name: userData.firstName,
        last_name: userData.lastName,
        email: userData.email,
        phone_number: userData.phoneNumber
      });

      if (profileError) {
        console.error("Error creating profile:", profileError);
      }
    }

  } catch (error) {
    console.error("Registration error:", error);
  }
};
```

## Option 3: Auth State Change Listener

Use Supabase's auth state change listener:

```typescript
// In your main app component or auth provider
import { useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';

useEffect(() => {
  const { data: { subscription } } = supabase.auth.onAuthStateChange(
    async (event, session) => {
      if (event === 'SIGNED_UP' && session?.user) {
        // Check if profile exists
        const { data: existingProfile } = await supabase
          .from('profiles')
          .select('id')
          .eq('id', session.user.id)
          .single();

        if (!existingProfile) {
          // Create profile
          const { error } = await supabase.rpc('handle_new_user_manual', {
            user_id: session.user.id,
            first_name: session.user.user_metadata?.first_name || '',
            last_name: session.user.user_metadata?.last_name || '',
            email: session.user.email || '',
            phone_number: session.user.phone || session.user.user_metadata?.phone_number || ''
          });

          if (error) {
            console.error("Error creating profile:", error);
          }
        }
      }
    }
  );

  return () => subscription.unsubscribe();
}, []);
```

## Testing Profile Creation

After implementing one of the above methods, test it:

```sql
-- Check if profiles are being created
SELECT 
  u.id,
  u.email,
  u.created_at as user_created,
  p.first_name,
  p.last_name,
  p.created_at as profile_created
FROM auth.users u
LEFT JOIN public.profiles p ON u.id = p.id
ORDER BY u.created_at DESC
LIMIT 10;
```

## Manual Profile Creation

If you need to create profiles for existing users:

```sql
-- Create profiles for existing users who don't have them
INSERT INTO public.profiles (id, email, first_name, last_name, phone_number)
SELECT 
  u.id,
  u.email,
  COALESCE(u.raw_user_meta_data->>'first_name', ''),
  COALESCE(u.raw_user_meta_data->>'last_name', ''),
  COALESCE(u.phone, u.raw_user_meta_data->>'phone_number', '')
FROM auth.users u
LEFT JOIN public.profiles p ON u.id = p.id
WHERE p.id IS NULL;
```

## Recommendation

For production use, **Option 1 (Database Webhooks)** is recommended as it:
- Automatically handles all user registrations
- Works regardless of how users sign up (web, mobile, third-party auth)
- Is more reliable than client-side solutions
- Doesn't require additional client-side code
