import Navbar from "@/components/layout/Navbar";

const SafetyPolicy = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      <div className="container mx-auto px-4 py-12">
        <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-8">Safety Policy</h1>
          
          <div className="prose prose-lg max-w-none">
            <p className="text-lg text-gray-600 mb-8">
              GescoStay prioritizes the safety and security of all users:
            </p>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">Verified Hosts</h2>
              <p className="text-gray-700 mb-4">
                All hosts are required to verify their identity and property ownership.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">Secure Transactions</h2>
              <p className="text-gray-700 mb-4">
                All payments are encrypted and processed through secure gateways.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">Emergency Support</h2>
              <p className="text-gray-700 mb-4">
                A 24/7 support line is available for emergencies related to property access, safety, or abuse.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">Identity Verification</h2>
              <p className="text-gray-700 mb-4">
                Guests may be asked to verify their identity to prevent fraud or misuse.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">Property Compliance</h2>
              <p className="text-gray-700 mb-4">
                Hosts must ensure properties comply with fire, health, and safety standards, 
                and maintain a clean and hospitable environment.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">Privacy Protection</h2>
              <ul className="list-disc list-inside text-gray-700 mb-4 space-y-2">
                <li>Your personal data is protected and never sold to third parties.</li>
                <li>Only necessary details (name, contact info) are shared with your host after booking confirmation.</li>
                <li>All financial data is handled through encrypted, PCI-compliant processors.</li>
              </ul>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">Contact</h2>
              <p className="text-gray-700">
                For safety concerns or emergencies, please contact our support team immediately at{" "}
                <a href="mailto:<EMAIL>" className="text-accent hover:underline">
                  <EMAIL>
                </a>
              </p>
            </section>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SafetyPolicy;
