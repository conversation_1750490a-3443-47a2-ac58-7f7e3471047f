import { useState } from "react";
import { Calendar } from "@/components/ui/calendar";
import { Button } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { format } from "date-fns";
import { Calendar as CalendarIcon } from "lucide-react";
import { cn } from "@/lib/utils";

interface DateRangePickerProps {
  checkInDate?: Date;
  checkOutDate?: Date;
  onCheckInChange: (date: Date | undefined) => void;
  onCheckOutChange: (date: Date | undefined) => void;
  disabledDates?: Date[];
  className?: string;
  checkInLabel?: string;
  checkOutLabel?: string;
}

export const DateRangePicker = ({
  checkInDate,
  checkOutDate,
  onCheckInChange,
  onCheckOutChange,
  disabledDates = [],
  className,
  checkInLabel = "Check-in Date",
  checkOutLabel = "Check-out Date",
}: DateRangePickerProps) => {
  const [checkInOpen, setCheckInOpen] = useState(false);
  const [checkOutOpen, setCheckOutOpen] = useState(false);

  const isDateDisabled = (date: Date) => {
    // Disable past dates
    if (date < new Date()) return true;

    // Disable dates that are already booked
    return disabledDates.some(
      (disabledDate) => date.toDateString() === disabledDate.toDateString()
    );
  };

  const isCheckOutDateDisabled = (date: Date) => {
    // All check-in disabled dates apply
    if (isDateDisabled(date)) return true;

    // Check-out date must be after check-in date
    if (checkInDate && date <= checkInDate) return true;

    return false;
  };

  return (
    <div className={cn("grid grid-cols-1 md:grid-cols-2 gap-4", className)}>
      {/* Check-in Date */}
      <div className="space-y-2">
        <label className="text-sm font-medium">{checkInLabel}</label>
        <Popover open={checkInOpen} onOpenChange={setCheckInOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                "w-full justify-start text-left font-normal overflow-hidden text-ellipsis whitespace-nowrap",
                !checkInDate && "text-muted-foreground"
              )}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              {checkInDate ? format(checkInDate, "PPP") : "Check-in date"}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <Calendar
              mode="single"
              selected={checkInDate}
              onSelect={(date) => {
                onCheckInChange(date);
                setCheckInOpen(false);
                // Reset check-out if it's before the new check-in
                if (checkOutDate && date && checkOutDate <= date) {
                  onCheckOutChange(undefined);
                }
              }}
              disabled={isDateDisabled}
              initialFocus
            />
          </PopoverContent>
        </Popover>
      </div>

      {/* Check-out Date */}
      <div className="space-y-2">
        <label className="text-sm font-medium">{checkOutLabel}</label>
        <Popover open={checkOutOpen} onOpenChange={setCheckOutOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                "w-full justify-start text-left font-normal overflow-hidden text-ellipsis whitespace-nowrap",
                !checkOutDate && "text-muted-foreground"
              )}
              disabled={!checkInDate}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              {checkOutDate ? format(checkOutDate, "PPP") : "Check-out date"}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <Calendar
              mode="single"
              selected={checkOutDate}
              onSelect={(date) => {
                onCheckOutChange(date);
                setCheckOutOpen(false);
              }}
              disabled={isCheckOutDateDisabled}
              initialFocus
            />
          </PopoverContent>
        </Popover>
      </div>
    </div>
  );
};
