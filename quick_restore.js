// Quick database restoration script
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://mbyiidayuburouqozgfq.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1ieWlpZGF5dWJ1cm91cW96Z2ZxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczMzQ5NzI5NCwiZXhwIjoyMDQ5MDczMjk0fQ.Ey6Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function executeSQL(sql) {
  try {
    const { data, error } = await supabase.rpc('exec_sql', { sql });
    if (error) {
      console.error('SQL Error:', error);
      return false;
    }
    return true;
  } catch (err) {
    console.error('Execution Error:', err);
    return false;
  }
}

async function restoreDatabase() {
  console.log('Starting quick database restoration...');

  // Step 1: Create properties table with sample data
  console.log('Creating properties table with sample data...');
  const success = await executeSQL(`
    -- Drop and recreate properties table
    DROP TABLE IF EXISTS properties CASCADE;
    
    CREATE TABLE properties (
      id BIGINT PRIMARY KEY DEFAULT (floor(random() * 90000000 + 10000000)::BIGINT),
      title TEXT NOT NULL,
      description TEXT NOT NULL,
      location TEXT NOT NULL,
      formatted_address TEXT,
      latitude DECIMAL(10, 8),
      longitude DECIMAL(11, 8),
      price DECIMAL(10, 2) NOT NULL CHECK (price > 0),
      beds INTEGER NOT NULL CHECK (beds > 0),
      baths INTEGER NOT NULL CHECK (baths > 0),
      owner_id UUID NOT NULL,
      images TEXT[] NOT NULL DEFAULT '{}',
      features TEXT[] NOT NULL DEFAULT '{}',
      status TEXT NOT NULL DEFAULT 'approved',
      is_dummy BOOLEAN DEFAULT false,
      created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
      updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
    );

    -- Insert sample properties
    INSERT INTO properties (owner_id, title, description, location, price, beds, baths, is_dummy)
    VALUES 
      ('00000000-0000-0000-0000-000000000000', 'Beautiful Beach House', 'A stunning beachfront property with ocean views', 'Miami Beach, FL', 250.00, 3, 2, true),
      ('00000000-0000-0000-0000-000000000000', 'Cozy Mountain Cabin', 'Perfect retreat in the mountains', 'Aspen, CO', 180.00, 2, 1, true),
      ('00000000-0000-0000-0000-000000000000', 'Downtown Loft', 'Modern loft in the heart of the city', 'New York, NY', 300.00, 1, 1, true),
      ('00000000-0000-0000-0000-000000000000', 'Family Villa', 'Spacious villa perfect for families', 'Orlando, FL', 200.00, 4, 3, true),
      ('00000000-0000-0000-0000-000000000000', 'Luxury Penthouse', 'High-end penthouse with city views', 'Los Angeles, CA', 500.00, 3, 2, true);
  `);

  if (success) {
    console.log('Properties table created successfully with sample data!');
  } else {
    console.error('Failed to create properties table');
    return;
  }

  // Step 2: Create cars table with sample data
  console.log('Creating cars table with sample data...');
  const carsSuccess = await executeSQL(`
    -- Drop and recreate cars table
    DROP TABLE IF EXISTS cars CASCADE;
    
    CREATE TABLE cars (
      id BIGINT PRIMARY KEY DEFAULT (floor(random() * 90000000 + 10000000)::BIGINT),
      owner_id UUID NOT NULL,
      title TEXT NOT NULL,
      description TEXT NOT NULL,
      car_type TEXT NOT NULL,
      make TEXT NOT NULL,
      model TEXT NOT NULL,
      year INTEGER NOT NULL CHECK (year > 1900 AND year <= EXTRACT(YEAR FROM CURRENT_DATE) + 1),
      location TEXT NOT NULL,
      formatted_address TEXT,
      latitude DECIMAL(10, 8),
      longitude DECIMAL(11, 8),
      images TEXT[] NOT NULL DEFAULT '{}',
      price_day DECIMAL(10, 2) NOT NULL CHECK (price_day > 0),
      price_week DECIMAL(10, 2) NOT NULL CHECK (price_week > 0),
      price_month DECIMAL(10, 2) NOT NULL CHECK (price_month > 0),
      seats INTEGER NOT NULL CHECK (seats > 0),
      transmission TEXT NOT NULL,
      fuel_type TEXT NOT NULL,
      status TEXT DEFAULT 'approved',
      is_dummy BOOLEAN DEFAULT false,
      created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
      updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
    );

    -- Insert sample cars
    INSERT INTO cars (owner_id, title, description, car_type, make, model, year, location, price_day, price_week, price_month, seats, transmission, fuel_type, is_dummy)
    VALUES 
      ('00000000-0000-0000-0000-000000000000', 'Toyota Camry 2022', 'Reliable and comfortable sedan', 'sedan', 'Toyota', 'Camry', 2022, 'Miami, FL', 45.00, 270.00, 900.00, 5, 'Automatic', 'Gasoline', true),
      ('00000000-0000-0000-0000-000000000000', 'Honda CR-V 2023', 'Spacious SUV perfect for families', 'suv', 'Honda', 'CR-V', 2023, 'Orlando, FL', 60.00, 360.00, 1200.00, 5, 'Automatic', 'Gasoline', true),
      ('00000000-0000-0000-0000-000000000000', 'BMW 3 Series 2021', 'Luxury sedan with premium features', 'sedan', 'BMW', '3 Series', 2021, 'Los Angeles, CA', 80.00, 480.00, 1600.00, 5, 'Automatic', 'Gasoline', true);
  `);

  if (carsSuccess) {
    console.log('Cars table created successfully with sample data!');
  } else {
    console.error('Failed to create cars table');
  }

  // Step 3: Create hotels table with sample data
  console.log('Creating hotels table with sample data...');
  const hotelsSuccess = await executeSQL(`
    -- Drop and recreate hotels table
    DROP TABLE IF EXISTS hotels CASCADE;
    
    CREATE TABLE hotels (
      id BIGINT PRIMARY KEY DEFAULT (floor(random() * 90000000 + 10000000)::BIGINT),
      title VARCHAR(255) NOT NULL,
      description TEXT NOT NULL,
      location VARCHAR(255) NOT NULL,
      formatted_address TEXT,
      latitude DECIMAL(10, 8),
      longitude DECIMAL(11, 8),
      owner_id UUID NOT NULL,
      images TEXT[] DEFAULT '{}',
      amenities TEXT[] DEFAULT '{}',
      policies JSONB DEFAULT '{}',
      check_in_time TIME DEFAULT '15:00:00',
      check_out_time TIME DEFAULT '11:00:00',
      status TEXT DEFAULT 'approved',
      is_dummy BOOLEAN DEFAULT false,
      created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
      updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
    );

    -- Insert sample hotels
    INSERT INTO hotels (owner_id, title, description, location, amenities, is_dummy)
    VALUES 
      ('00000000-0000-0000-0000-000000000000', 'Ocean View Resort', 'Luxury resort with stunning ocean views', 'Miami Beach, FL', '{"pool", "spa", "restaurant", "wifi"}', true),
      ('00000000-0000-0000-0000-000000000000', 'Mountain Lodge', 'Cozy lodge in the heart of the mountains', 'Aspen, CO', '{"fireplace", "ski_access", "restaurant", "wifi"}', true),
      ('00000000-0000-0000-0000-000000000000', 'City Center Hotel', 'Modern hotel in downtown area', 'New York, NY', '{"gym", "business_center", "restaurant", "wifi"}', true);
  `);

  if (hotelsSuccess) {
    console.log('Hotels table created successfully with sample data!');
  } else {
    console.error('Failed to create hotels table');
  }

  console.log('Quick restoration completed!');
  console.log('All tables now use BIGINT IDs and have sample data for testing.');
}

restoreDatabase().catch(console.error);
