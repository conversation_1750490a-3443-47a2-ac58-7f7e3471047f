import { useState, useEffect } from "react";
import { useSearchParams, useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/components/layout/AuthProvider";
import {
  Loader2,
  Phone,
  User,
  Mail,
  Lock,
  Eye,
  EyeOff,
  Smartphone,
} from "lucide-react";
import Navbar from "@/components/layout/Navbar";
import VerificationPrompt from "@/components/auth/VerificationPrompt";
import AuthErrorHandler from "@/components/auth/AuthErrorHandler";
import { PhoneInput } from "@/components/ui/phone-input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";

const AuthPage = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [isLogin, setIsLogin] = useState(true);
  const [email, setEmail] = useState("");
  const [phoneNumber, setPhoneNumber] = useState("+220 ");
  const [password, setPassword] = useState("");
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [loading, setLoading] = useState(false);
  const [isResetPassword, setIsResetPassword] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showVerificationPrompt, setShowVerificationPrompt] = useState(false);
  const [unverifiedPhone, setUnverifiedPhone] = useState("");
  const [authError, setAuthError] = useState("");
  const [primaryLoginMethod, setPrimaryLoginMethod] = useState<
    "email" | "phone"
  >("phone");
  const [loginMethod, setLoginMethod] = useState<"email" | "phone">("phone");
  const { toast } = useToast();
  const { signIn, signInWithPhone, signUp, signUpWithPhone, resetPassword } =
    useAuth();

  // Set initial mode based on URL parameter
  useEffect(() => {
    const mode = searchParams.get("mode");
    if (mode === "signup") {
      setIsLogin(false);
    } else if (mode === "signin") {
      setIsLogin(true);
    }
  }, [searchParams]);

  const clearForm = () => {
    setEmail("");
    setPhoneNumber("+220 ");
    setPassword("");
    setFirstName("");
    setLastName("");
    setIsResetPassword(false);
  };

  const handleAuth = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setAuthError(""); // Clear previous errors
    try {
      if (isResetPassword) {
        await resetPassword(email);
        toast({
          title: "Success!",
          description:
            "Please check your email for password reset instructions.",
        });
        clearForm();
      } else if (isLogin) {
        // Login with selected method (email or phone)
        if (loginMethod === "email") {
          await signIn(email, password);
        } else {
          await signInWithPhone(phoneNumber, password);
        }
        clearForm();
      } else {
        // Registration with selected primary method
        if (primaryLoginMethod === "email") {
          await signUp(
            email,
            password,
            firstName,
            lastName,
            phoneNumber || undefined
          );

          // Navigate to email verification page
          navigate("/auth/verify-otp", {
            state: {
              email,
              method: "email",
              timestamp: Date.now(),
            },
          });

          toast({
            title: "Success!",
            description: "Please check your email for verification code.",
          });
        } else {
          await signUpWithPhone(
            phoneNumber,
            password,
            firstName,
            lastName,
            email || undefined
          );

          // Navigate to phone verification page
          navigate("/auth/verify-otp", {
            state: {
              phoneNumber,
              method: "phone",
              timestamp: Date.now(),
            },
          });

          toast({
            title: "Success!",
            description: "Please check your phone for verification code.",
          });
        }
        clearForm();
      }
    } catch (error) {
      if (
        error instanceof Error &&
        (error.message === "PHONE_NOT_VERIFIED" ||
          error.message === "EMAIL_NOT_VERIFIED")
      ) {
        // Show verification prompt for unverified users
        if (error.message === "PHONE_NOT_VERIFIED") {
          setUnverifiedPhone(phoneNumber);
        }
        setShowVerificationPrompt(true);
      } else {
        // Set error for the error handler component
        const errorMessage =
          error instanceof Error
            ? error.message
            : "An unexpected error occurred";
        setAuthError(errorMessage);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-warm-tan/10 via-background to-muted-teal/10">
      <Navbar />
      <div className="flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <div className="text-center">
            <div className="mx-auto h-16 w-16 bg-gradient-to-r from-accent to-secondary rounded-full flex items-center justify-center mb-6">
              <Phone className="h-8 w-8 text-white" />
            </div>
            <h2 className="text-3xl font-bold tracking-tight text-foreground mb-2">
              {isResetPassword
                ? "Reset your password"
                : isLogin
                ? "Welcome back"
                : "Join Gesco Stay"}
            </h2>
            <p className="text-muted-foreground">
              {isResetPassword
                ? "Enter your email to receive reset instructions"
                : isLogin
                ? "Sign in to access your account"
                : "Create your account to get started"}
            </p>
          </div>
        </div>

        <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
          <div className="bg-card py-8 px-6 shadow-xl rounded-2xl border border-border">
            <form className="space-y-5" onSubmit={handleAuth}>
              {/* Error Handler */}
              {authError && (
                <AuthErrorHandler
                  error={authError}
                  phoneNumber={phoneNumber}
                  onRetry={() => setAuthError("")}
                  onGoToVerification={() => {
                    if (authError === "EMAIL_NOT_VERIFIED") {
                      // Navigate to email verification
                      navigate("/auth/verify-otp", {
                        state: {
                          email,
                          method: "email",
                          timestamp: Date.now(),
                        },
                      });
                    } else {
                      // Phone verification
                      setUnverifiedPhone(phoneNumber);
                      setShowVerificationPrompt(true);
                    }
                    setAuthError("");
                  }}
                  onGoToSignup={() => {
                    setIsLogin(false);
                    setAuthError("");
                  }}
                />
              )}

              {/* Registration Fields - Ordered: First Name, Last Name, Mobile Number, Email, Password */}
              {!isLogin && !isResetPassword && (
                <>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label
                        htmlFor="firstName"
                        className="block text-sm font-medium text-foreground mb-2"
                      >
                        First Name
                      </label>
                      <div className="relative">
                        <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-5 w-5" />
                        <Input
                          id="firstName"
                          type="text"
                          required
                          value={firstName}
                          onChange={(e) => setFirstName(e.target.value)}
                          placeholder="John"
                          className="pl-10 h-12 border-input focus:border-accent focus:ring-accent"
                          disabled={loading}
                        />
                      </div>
                    </div>

                    <div>
                      <label
                        htmlFor="lastName"
                        className="block text-sm font-medium text-foreground mb-2"
                      >
                        Last Name
                      </label>
                      <div className="relative">
                        <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-5 w-5" />
                        <Input
                          id="lastName"
                          type="text"
                          required
                          value={lastName}
                          onChange={(e) => setLastName(e.target.value)}
                          placeholder="Doe"
                          className="pl-10 h-12 border-input focus:border-accent focus:ring-accent"
                          disabled={loading}
                        />
                      </div>
                    </div>
                  </div>

                  {/* Primary Login Method Selector */}
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-3">
                      Primary Login Method{" "}
                      <span className="text-destructive">*</span>
                    </label>
                    <RadioGroup
                      value={primaryLoginMethod}
                      onValueChange={(value: "email" | "phone") =>
                        setPrimaryLoginMethod(value)
                      }
                      className="flex space-x-6"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="phone" id="phone-primary" />
                        <Label
                          htmlFor="phone-primary"
                          className="flex items-center space-x-2 cursor-pointer"
                        >
                          <Phone className="h-4 w-4" />
                          <span>Phone Number</span>
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="email" id="email-primary" />
                        <Label
                          htmlFor="email-primary"
                          className="flex items-center space-x-2 cursor-pointer"
                        >
                          <Mail className="h-4 w-4" />
                          <span>Email Address</span>
                        </Label>
                      </div>
                    </RadioGroup>
                    <p className="text-xs text-muted-foreground mt-2">
                      The verification code will be sent to your selected
                      primary login method.
                    </p>
                  </div>

                  <div>
                    <label
                      htmlFor="phoneNumber"
                      className="block text-sm font-medium text-foreground mb-2"
                    >
                      Mobile Number <span className="text-destructive">*</span>
                    </label>
                    <PhoneInput
                      value={phoneNumber}
                      onChange={setPhoneNumber}
                      placeholder="Enter your phone number"
                      disabled={loading}
                      required
                      className="h-12"
                    />
                  </div>

                  <div>
                    <label
                      htmlFor="email"
                      className="block text-sm font-medium text-foreground mb-2"
                    >
                      Email address <span className="text-destructive">*</span>
                    </label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-5 w-5" />
                      <Input
                        id="email"
                        type="email"
                        required
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        placeholder="<EMAIL>"
                        className="pl-10 h-12 border-input focus:border-accent focus:ring-accent"
                        disabled={loading}
                      />
                    </div>
                  </div>

                  <div>
                    <label
                      htmlFor="password"
                      className="block text-sm font-medium text-foreground mb-2"
                    >
                      Password
                    </label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-5 w-5" />
                      <Input
                        id="password"
                        type={showPassword ? "text" : "password"}
                        required
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        placeholder="Create a strong password"
                        className="pl-10 pr-10 h-12 border-input focus:border-accent focus:ring-accent"
                        disabled={loading}
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
                      >
                        {showPassword ? (
                          <EyeOff className="h-5 w-5" />
                        ) : (
                          <Eye className="h-5 w-5" />
                        )}
                      </button>
                    </div>
                  </div>
                </>
              )}

              {/* Login Fields - Email or Phone */}
              {isLogin && !isResetPassword && (
                <>
                  {/* Login Method Selector */}
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-3">
                      Login Method
                    </label>
                    <RadioGroup
                      value={loginMethod}
                      onValueChange={(value: "email" | "phone") =>
                        setLoginMethod(value)
                      }
                      className="flex space-x-6"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="phone" id="phone-login" />
                        <Label
                          htmlFor="phone-login"
                          className="flex items-center space-x-2 cursor-pointer"
                        >
                          <Phone className="h-4 w-4" />
                          <span>Phone Number</span>
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="email" id="email-login" />
                        <Label
                          htmlFor="email-login"
                          className="flex items-center space-x-2 cursor-pointer"
                        >
                          <Mail className="h-4 w-4" />
                          <span>Email Address</span>
                        </Label>
                      </div>
                    </RadioGroup>
                  </div>

                  {/* Phone Number Field */}
                  {loginMethod === "phone" && (
                    <div>
                      <label
                        htmlFor="phoneNumber"
                        className="block text-sm font-medium text-foreground mb-2"
                      >
                        Mobile Number
                      </label>
                      <PhoneInput
                        value={phoneNumber}
                        onChange={setPhoneNumber}
                        placeholder="Enter your phone number"
                        disabled={loading}
                        required
                        className="h-12"
                      />
                    </div>
                  )}

                  {/* Email Field */}
                  {loginMethod === "email" && (
                    <div>
                      <label
                        htmlFor="email"
                        className="block text-sm font-medium text-foreground mb-2"
                      >
                        Email Address
                      </label>
                      <div className="relative">
                        <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-5 w-5" />
                        <Input
                          id="email"
                          type="email"
                          required
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                          placeholder="<EMAIL>"
                          className="pl-10 h-12 border-input focus:border-accent focus:ring-accent"
                          disabled={loading}
                        />
                      </div>
                    </div>
                  )}

                  <div>
                    <label
                      htmlFor="password"
                      className="block text-sm font-medium text-foreground mb-2"
                    >
                      Password
                    </label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-5 w-5" />
                      <Input
                        id="password"
                        type={showPassword ? "text" : "password"}
                        required
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        placeholder="Enter your password"
                        className="pl-10 pr-10 h-12 border-input focus:border-accent focus:ring-accent"
                        disabled={loading}
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
                      >
                        {showPassword ? (
                          <EyeOff className="h-5 w-5" />
                        ) : (
                          <Eye className="h-5 w-5" />
                        )}
                      </button>
                    </div>
                  </div>
                </>
              )}

              {/* Reset Password Field */}
              {isResetPassword && (
                <div>
                  <label
                    htmlFor="email"
                    className="block text-sm font-medium text-foreground mb-2"
                  >
                    Email address
                  </label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-5 w-5" />
                    <Input
                      id="email"
                      type="email"
                      required
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      placeholder="Enter your email address"
                      className="pl-10 h-12 border-input focus:border-accent focus:ring-accent"
                      disabled={loading}
                    />
                  </div>
                </div>
              )}

              <Button
                type="submit"
                className="w-full h-12 bg-gradient-to-r from-accent to-secondary hover:from-accent/90 hover:to-secondary/90 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02]"
                disabled={loading}
              >
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                    {isResetPassword
                      ? "Sending reset link..."
                      : isLogin
                      ? "Signing in..."
                      : "Creating account..."}
                  </>
                ) : isResetPassword ? (
                  "Send reset link"
                ) : isLogin ? (
                  "Sign in"
                ) : (
                  "Create account"
                )}
              </Button>
            </form>

            <div className="mt-8 space-y-4">
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-border" />
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-4 bg-card text-muted-foreground">
                    {isResetPassword
                      ? "Remember your password?"
                      : isLogin
                      ? "New to Gesco Stay?"
                      : "Already have an account?"}
                  </span>
                </div>
              </div>

              <Button
                variant="ghost"
                className="w-full text-accent hover:text-accent/80 hover:bg-accent/5 font-medium"
                onClick={() => {
                  if (isResetPassword) {
                    setIsResetPassword(false);
                    clearForm();
                  } else {
                    setIsLogin(!isLogin);
                    setIsResetPassword(false);
                    clearForm();
                  }
                }}
                disabled={loading}
              >
                {isResetPassword
                  ? "Back to sign in"
                  : isLogin
                  ? "Create a new account"
                  : "Sign in to existing account"}
              </Button>

              {isLogin && !isResetPassword && (
                <Button
                  variant="ghost"
                  className="w-full text-muted-foreground hover:text-foreground hover:bg-muted text-sm"
                  onClick={() => setIsResetPassword(true)}
                  disabled={loading}
                >
                  Forgot your password?
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Verification Prompt Modal */}
      {showVerificationPrompt && (
        <VerificationPrompt
          phoneNumber={unverifiedPhone}
          onClose={() => setShowVerificationPrompt(false)}
        />
      )}
    </div>
  );
};

export default AuthPage;
