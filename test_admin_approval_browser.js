// Test Admin Approval Functionality
// Run this in the browser console on the admin listings page

console.log("=== Testing Admin Approval Functionality ===");

async function testAdminApproval() {
  try {
    // Import supabase client
    const { supabase } = await import('/src/integrations/supabase/client.ts');
    
    console.log("1. Checking current user authentication...");
    
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      console.error("❌ User not authenticated:", userError);
      return;
    }
    
    console.log("✅ User authenticated:", user.email);
    
    // Check user profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id, email, role')
      .eq('id', user.id)
      .single();
    
    if (profileError) {
      console.error("❌ Error getting profile:", profileError);
      return;
    }
    
    console.log("✅ User profile:", profile);
    
    if (profile.role !== 'admin') {
      console.error("❌ User is not admin. Current role:", profile.role);
      return;
    }
    
    console.log("2. Testing database access...");
    
    // Test properties access
    const { data: properties, error: propError } = await supabase
      .from('properties')
      .select('id, title, status, owner_id')
      .limit(5);
    
    if (propError) {
      console.error("❌ Properties access error:", propError);
    } else {
      console.log("✅ Properties accessible:", properties?.length || 0);
      console.log("Sample properties:", properties);
    }
    
    // Test cars access
    const { data: cars, error: carError } = await supabase
      .from('cars')
      .select('id, title, status, owner_id')
      .limit(5);
    
    if (carError) {
      console.error("❌ Cars access error:", carError);
    } else {
      console.log("✅ Cars accessible:", cars?.length || 0);
      console.log("Sample cars:", cars);
    }
    
    // Test hotels access
    const { data: hotels, error: hotelError } = await supabase
      .from('hotels')
      .select('id, title, status, owner_id')
      .limit(5);
    
    if (hotelError) {
      console.error("❌ Hotels access error:", hotelError);
    } else {
      console.log("✅ Hotels accessible:", hotels?.length || 0);
      console.log("Sample hotels:", hotels);
    }
    
    console.log("3. Testing update permissions...");
    
    // Function to test update on a specific table
    async function testUpdate(tableName, items) {
      if (!items || items.length === 0) {
        console.log(`⚠️ No ${tableName} found to test`);
        return;
      }
      
      const testItem = items[0];
      const originalStatus = testItem.status;
      const newStatus = originalStatus === 'pending' ? 'approved' : 'pending';
      
      console.log(`Testing ${tableName} update for ID: ${testItem.id}`);
      console.log(`Changing status from '${originalStatus}' to '${newStatus}'`);
      
      // Attempt the update
      const { data: updateResult, error: updateError } = await supabase
        .from(tableName)
        .update({ 
          status: newStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', testItem.id)
        .select();
      
      if (updateError) {
        console.error(`❌ ${tableName} update failed:`, updateError);
        return false;
      }
      
      if (updateResult && updateResult.length > 0) {
        const updatedItem = updateResult[0];
        console.log(`✅ ${tableName} update successful:`, updatedItem);
        
        if (updatedItem.status === newStatus) {
          console.log(`✅ Status correctly changed to '${newStatus}'`);
          
          // Revert the change
          const { error: revertError } = await supabase
            .from(tableName)
            .update({ 
              status: originalStatus,
              updated_at: new Date().toISOString()
            })
            .eq('id', testItem.id);
          
          if (revertError) {
            console.error(`⚠️ Failed to revert ${tableName} status:`, revertError);
          } else {
            console.log(`✅ ${tableName} status reverted to '${originalStatus}'`);
          }
          
          return true;
        } else {
          console.error(`❌ Status not changed correctly. Expected '${newStatus}', got '${updatedItem.status}'`);
          return false;
        }
      } else {
        console.error(`❌ No data returned from ${tableName} update`);
        return false;
      }
    }
    
    // Test updates on all tables
    const propertyTest = await testUpdate('properties', properties);
    const carTest = await testUpdate('cars', cars);
    const hotelTest = await testUpdate('hotels', hotels);
    
    console.log("4. Test Results Summary:");
    console.log(`Properties update: ${propertyTest ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Cars update: ${carTest ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Hotels update: ${hotelTest ? '✅ PASS' : '❌ FAIL'}`);
    
    if (propertyTest && carTest && hotelTest) {
      console.log("🎉 ALL TESTS PASSED! Admin approval should work correctly.");
    } else {
      console.log("⚠️ Some tests failed. Check the errors above and run the SQL fix script.");
    }
    
  } catch (error) {
    console.error("❌ Test failed with error:", error);
  }
}

// Function to test specific listing approval (use this after running the main test)
async function testSpecificApproval(listingType, listingId, newStatus) {
  try {
    const { supabase } = await import('/src/integrations/supabase/client.ts');
    
    console.log(`Testing specific approval: ${listingType} ${listingId} -> ${newStatus}`);
    
    const { data: result, error } = await supabase
      .from(listingType)
      .update({ 
        status: newStatus,
        updated_at: new Date().toISOString()
      })
      .eq('id', listingId)
      .select();
    
    if (error) {
      console.error("❌ Specific approval failed:", error);
    } else {
      console.log("✅ Specific approval successful:", result);
    }
    
  } catch (error) {
    console.error("❌ Specific approval test failed:", error);
  }
}

// Run the main test
testAdminApproval();

// Export functions for manual testing
window.testAdminApproval = testAdminApproval;
window.testSpecificApproval = testSpecificApproval;

console.log("Functions available:");
console.log("- testAdminApproval() - Run full test suite");
console.log("- testSpecificApproval('properties', 'listing-id', 'approved') - Test specific listing");
