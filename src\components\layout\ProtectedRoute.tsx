import { useAuth } from "@/components/layout/AuthProvider";
import { Navigate } from "react-router-dom";
import { ReactNode } from "react";
import { VerificationRedirect } from "@/components/auth/VerificationRedirect";

interface ProtectedRouteProps {
  children: ReactNode;
  requireVerification?: boolean;
}

const ProtectedRoute = ({
  children,
  requireVerification = true,
}: ProtectedRouteProps) => {
  const { user, loading } = useAuth();

  // Show loading while checking authentication
  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-800"></div>
      </div>
    );
  }

  // Redirect to auth if not logged in
  if (!user) {
    return <Navigate to="/auth" replace />;
  }

  // If verification is required, use VerificationRedirect to handle primary method verification
  if (requireVerification) {
    return <VerificationRedirect>{children}</VerificationRedirect>;
  }

  return <>{children}</>;
};

export default ProtectedRoute;
