-- Short ID Generation Functions
-- This file creates functions to generate short numeric IDs similar to Airbnb

-- Function to generate short numeric IDs (8 digits)
CREATE OR REPLACE FUNCTION generate_short_id()
RETURNS BIGINT AS $$
DECLARE
  new_id BIGINT;
BEGIN
  -- Generate a random 8-digit number (10000000 to 99999999)
  new_id := floor(random() * 90000000 + 10000000)::BIGINT;
  RETURN new_id;
END;
$$ LANGUAGE plpgsql;

-- Function to generate unique short booking ID
CREATE OR REPLACE FUNCTION generate_unique_booking_id()
RETURNS BIGINT AS $$
DECLARE
  new_id BIGINT;
  exists_check BOOLEAN;
BEGIN
  LOOP
    new_id := generate_short_id();
    
    -- Check if ID exists in any booking table
    SELECT EXISTS(
      SELECT 1 FROM bookings WHERE id = new_id
      UNION ALL
      SELECT 1 FROM car_bookings WHERE id = new_id  
      UNION ALL
      SELECT 1 FROM hotel_bookings WHERE id = new_id
    ) INTO exists_check;
    
    IF NOT exists_check THEN
      RETURN new_id;
    END IF;
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Function to generate unique short property ID
CREATE OR REPLACE FUNCTION generate_unique_property_id()
RETURNS BIGINT AS $$
DECLARE
  new_id BIGINT;
  exists_check BOOLEAN;
BEGIN
  LOOP
    new_id := generate_short_id();
    
    -- Check if ID exists in any property table
    SELECT EXISTS(
      SELECT 1 FROM properties WHERE id = new_id
      UNION ALL
      SELECT 1 FROM cars WHERE id = new_id
      UNION ALL  
      SELECT 1 FROM hotels WHERE id = new_id
    ) INTO exists_check;
    
    IF NOT exists_check THEN
      RETURN new_id;
    END IF;
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Function to generate unique short room type ID
CREATE OR REPLACE FUNCTION generate_unique_room_type_id()
RETURNS BIGINT AS $$
DECLARE
  new_id BIGINT;
BEGIN
  LOOP
    new_id := generate_short_id();
    
    IF NOT EXISTS(SELECT 1 FROM room_types WHERE id = new_id) THEN
      RETURN new_id;
    END IF;
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Function to generate unique short review ID
CREATE OR REPLACE FUNCTION generate_unique_review_id()
RETURNS BIGINT AS $$
DECLARE
  new_id BIGINT;
BEGIN
  LOOP
    new_id := generate_short_id();
    
    IF NOT EXISTS(SELECT 1 FROM reviews WHERE id = new_id) THEN
      RETURN new_id;
    END IF;
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Function to generate unique short conversation ID
CREATE OR REPLACE FUNCTION generate_unique_conversation_id()
RETURNS BIGINT AS $$
DECLARE
  new_id BIGINT;
BEGIN
  LOOP
    new_id := generate_short_id();
    
    IF NOT EXISTS(SELECT 1 FROM conversations WHERE id = new_id) THEN
      RETURN new_id;
    END IF;
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Function to generate unique short message ID
CREATE OR REPLACE FUNCTION generate_unique_message_id()
RETURNS BIGINT AS $$
DECLARE
  new_id BIGINT;
BEGIN
  LOOP
    new_id := generate_short_id();
    
    IF NOT EXISTS(SELECT 1 FROM messages WHERE id = new_id) THEN
      RETURN new_id;
    END IF;
  END LOOP;
END;
$$ LANGUAGE plpgsql;
