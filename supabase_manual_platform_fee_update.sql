-- =====================================================
-- MANUAL SUPABASE DATABASE UPDATE SCRIPT
-- Update Platform Fee from 10% to 15%
-- Run this script in Supabase Dashboard > SQL Editor
-- =====================================================

-- 1. Update the calculate_platform_fee function to use 15%
DROP FUNCTION IF EXISTS public.calculate_platform_fee(numeric, text);

CREATE OR REPLACE FUNCTION public.calculate_platform_fee(
  booking_amount numeric,
  booking_type text DEFAULT 'property'
) 
RETURNS NUMERIC
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  platform_fee NUMERIC;
BEGIN
  -- Calculate 15% platform fee (updated from 10%)
  platform_fee := booking_amount * 0.15;
  RETURN platform_fee;
END;
$$;

-- Add comment to document the change
COMMENT ON FUNCTION public.calculate_platform_fee(numeric, text) IS 'Calculates 15% platform fee for bookings. Updated from 10% to 15% on 2025-06-20.';

-- 2. Create helper function for host payout calculation
CREATE OR REPLACE FUNCTION public.calculate_host_payout(
  booking_amount numeric,
  booking_type text DEFAULT 'property'
) 
RETURNS NUMERIC
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  host_payout NUMERIC;
BEGIN
  -- Calculate host payout: 85% of booking amount (after 15% platform fee)
  host_payout := booking_amount * 0.85;
  RETURN host_payout;
END;
$$;

COMMENT ON FUNCTION public.calculate_host_payout(numeric, text) IS 'Calculates host payout amount (85% of booking amount after 15% platform fee).';

-- 3. Check if platform_earnings table exists and has records with old 10% calculations
-- If records exist, you may want to update them (OPTIONAL - only if you want to recalculate historical data)

-- First, let's see if there are any existing platform_earnings records
-- SELECT COUNT(*) as total_records FROM platform_earnings;

-- 4. OPTIONAL: Update existing platform_earnings records to use 15% fee
-- WARNING: This will recalculate historical data. Only run if you want to update past records.
-- Uncomment the following lines if you want to update historical records:

/*
UPDATE platform_earnings 
SET 
  platform_fee = total_booking_amount * 0.15,
  host_payout_amount = total_booking_amount * 0.85
WHERE 
  platform_fee = total_booking_amount * 0.10  -- Only update records that were calculated with 10%
  AND created_at > '2025-01-01'  -- Optional: only update recent records
;
*/

-- 5. Create a view to easily see platform fee calculations
CREATE OR REPLACE VIEW platform_fee_summary AS
SELECT 
  booking_type,
  COUNT(*) as total_bookings,
  SUM(total_booking_amount) as total_booking_value,
  SUM(platform_fee) as total_platform_fees,
  SUM(host_payout_amount) as total_host_payouts,
  ROUND(AVG(platform_fee / total_booking_amount * 100), 2) as avg_fee_percentage
FROM platform_earnings
GROUP BY booking_type;

-- 6. Create a function to validate platform fee calculations
CREATE OR REPLACE FUNCTION validate_platform_fee_calculation(
  booking_amount numeric
)
RETURNS TABLE(
  booking_amount_input numeric,
  calculated_platform_fee numeric,
  calculated_host_payout numeric,
  fee_percentage numeric,
  payout_percentage numeric
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    booking_amount as booking_amount_input,
    public.calculate_platform_fee(booking_amount) as calculated_platform_fee,
    public.calculate_host_payout(booking_amount) as calculated_host_payout,
    ROUND((public.calculate_platform_fee(booking_amount) / booking_amount * 100), 2) as fee_percentage,
    ROUND((public.calculate_host_payout(booking_amount) / booking_amount * 100), 2) as payout_percentage;
END;
$$;

-- 7. Test the updated functions
-- SELECT * FROM validate_platform_fee_calculation(100.00);
-- Expected results: 15.00 platform fee, 85.00 host payout, 15% fee, 85% payout

-- 8. Create a trigger to ensure all new platform_earnings records use correct calculations
CREATE OR REPLACE FUNCTION ensure_correct_platform_fee()
RETURNS TRIGGER AS $$
BEGIN
  -- Recalculate platform fee and host payout to ensure they're correct
  NEW.platform_fee := NEW.total_booking_amount * 0.15;
  NEW.host_payout_amount := NEW.total_booking_amount * 0.85;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop existing trigger if it exists
DROP TRIGGER IF EXISTS platform_earnings_fee_validation ON platform_earnings;

-- Create the trigger
CREATE TRIGGER platform_earnings_fee_validation
  BEFORE INSERT OR UPDATE ON platform_earnings
  FOR EACH ROW
  EXECUTE FUNCTION ensure_correct_platform_fee();

-- 9. Add a check constraint to ensure platform fees are calculated correctly (optional)
-- This will prevent any records with incorrect fee calculations from being inserted
/*
ALTER TABLE platform_earnings 
ADD CONSTRAINT check_platform_fee_15_percent 
CHECK (
  ABS(platform_fee - (total_booking_amount * 0.15)) < 0.01 
  AND ABS(host_payout_amount - (total_booking_amount * 0.85)) < 0.01
);
*/

-- =====================================================
-- VERIFICATION QUERIES
-- Run these to verify the updates worked correctly
-- =====================================================

-- Test the platform fee function
-- SELECT public.calculate_platform_fee(100.00) as platform_fee_for_100;
-- Expected result: 15.00

-- Test the host payout function  
-- SELECT public.calculate_host_payout(100.00) as host_payout_for_100;
-- Expected result: 85.00

-- View the platform fee summary
-- SELECT * FROM platform_fee_summary;

-- Test validation function
-- SELECT * FROM validate_platform_fee_calculation(250.00);

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================
SELECT 'Platform fee update completed successfully! All functions now use 15% platform fee.' as status;
