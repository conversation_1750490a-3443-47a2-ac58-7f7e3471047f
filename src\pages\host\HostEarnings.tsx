import { useState, useEffect } from "react";
import { useAuth } from "@/components/layout/AuthProvider";
import { supabase } from "@/integrations/supabase/client";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  AlertCircle,
  ArrowDownToLine,
  ChevronDown,
  ChevronRight,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";

// Import custom components
import EarningsStats from "@/components/host/EarningsStats";
import PayoutRequestsList from "@/components/host/PayoutRequestsList";
import PaymentMethodsList from "@/components/host/PaymentMethodsList";
import PaymentMethodForm from "@/components/host/PaymentMethodForm";
import PayoutRequestForm from "@/components/host/PayoutRequestForm";
import PaymentSchedule from "@/components/host/PaymentSchedule";

// Import types
import { PayoutRequest } from "@/components/host/PayoutRequestsList";
import { PaymentMethod } from "@/components/host/PaymentMethodsList";

const HostEarnings = () => {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [paymentAccountDialogOpen, setPaymentAccountDialogOpen] =
    useState(false);
  const [payoutRequestDialogOpen, setPayoutRequestDialogOpen] = useState(false);
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [earnings, setEarnings] = useState({
    totalEarnings: 0,
    pendingPayouts: 0,
    completedPayouts: 0,
    availableBalance: 0,
    pastEarnings: [] as any[],
    upcomingPayments: [] as any[],
    payoutRequests: [] as PayoutRequest[],
  });

  useEffect(() => {
    if (user) {
      fetchEarningsData();
      fetchPaymentMethods();
    }
  }, [user]);

  const fetchEarningsData = async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      // Fetch property bookings
      const { data: propBookings, error: propError } = await supabase
        .from("bookings")
        .select(
          `
          *,
          properties(title, images)
        `
        )
        .eq("status", "confirmed")
        .order("created_at", { ascending: false });

      if (propError) throw propError;

      // Fetch car bookings
      const { data: carBookings, error: carError } = await supabase
        .from("car_bookings")
        .select(
          `
          *,
          cars(title, make, model, images)
        `
        )
        .eq("status", "confirmed")
        .order("created_at", { ascending: false });

      if (carError) throw carError;

      // Fetch payout requests using RPC function
      let payoutRequests: PayoutRequest[] = [];
      try {
        const { data, error } = await supabase.rpc("get_host_payout_requests", {
          host_id: user.id,
        });

        if (error) throw error;
        payoutRequests = data as PayoutRequest[];
      } catch (e) {
        console.log("RPC not available, falling back to direct query");
        // Fallback to direct query
        const { data, error } = await supabase
          .from("payout_requests")
          .select("*")
          .eq("host_id", user.id)
          .order("created_at", { ascending: false });

        if (!error) {
          payoutRequests = data as PayoutRequest[];
        }
      }

      // Fetch platform earnings to calculate host's actual earnings after fees
      const { data: platformEarnings, error: platformError } = await supabase
        .from("platform_earnings")
        .select("*");

      if (platformError)
        console.error("Error fetching platform earnings:", platformError);

      // Process bookings data
      const allBookings = [...(propBookings || []), ...(carBookings || [])];

      // Calculate host's earnings after 15% platform fee
      const totalEarnings = allBookings.reduce((sum, booking) => {
        // Host gets 85% of the total booking amount
        const hostEarning =
          (typeof booking.total_price === "number" ? booking.total_price : 0) *
          0.85;
        return sum + hostEarning;
      }, 0);

      // Handle property bookings separately since car_bookings doesn't have payment_status
      const pendingAmount = propBookings
        ? propBookings
            .filter((booking) => booking.payment_status !== "completed")
            .reduce((sum, booking) => {
              const hostEarning =
                (typeof booking.total_price === "number"
                  ? booking.total_price
                  : 0) * 0.85;
              return sum + hostEarning;
            }, 0)
        : 0;

      const completedAmount = propBookings
        ? propBookings
            .filter((booking) => booking.payment_status === "completed")
            .reduce((sum, booking) => {
              const hostEarning =
                (typeof booking.total_price === "number"
                  ? booking.total_price
                  : 0) * 0.85;
              return sum + hostEarning;
            }, 0)
        : 0;

      // Calculate available balance (total earnings minus already paid out)
      const paidOutTotal = payoutRequests
        ? payoutRequests
            .filter((request) => request.status === "approved")
            .reduce(
              (sum, request) =>
                sum + (typeof request.amount === "number" ? request.amount : 0),
              0
            )
        : 0;

      const availableBalance = totalEarnings - paidOutTotal;

      // Get real past payouts from approved payout requests
      const pastPayouts = payoutRequests
        ? payoutRequests
            .filter((request) => request.status === "approved")
            .map((request) => ({
              id: request.id,
              amount: typeof request.amount === "number" ? request.amount : 0,
              date: request.created_at
                ? new Date(request.created_at).toISOString().split("T")[0]
                : "",
              status: "completed",
              method: request.payment_method || "Bank Transfer",
              reference: `REF${request.id.slice(0, 8)}`,
            }))
        : [];

      // Calculate upcoming payouts from recent confirmed bookings that haven't been paid out yet
      const recentBookings = allBookings.filter((booking) => {
        const bookingDate = new Date(booking.created_at);
        const daysSinceBooking =
          (Date.now() - bookingDate.getTime()) / (1000 * 60 * 60 * 24);
        return daysSinceBooking <= 30 && booking.payment_status === "completed"; // Recent completed bookings
      });

      const upcomingPayouts = recentBookings
        .slice(0, 5)
        .map((booking, index) => {
          const hostEarning =
            (typeof booking.total_price === "number"
              ? booking.total_price
              : 0) * 0.85;
          const futureDate = new Date();
          futureDate.setDate(futureDate.getDate() + (index + 1) * 7); // Weekly payouts

          return {
            id: `upcoming_${booking.id}`,
            amount: hostEarning,
            date: futureDate.toISOString().split("T")[0],
            status: index === 0 ? "pending" : "scheduled",
            processing_days: (index + 1) * 7,
            booking_ids: [booking.id],
          };
        });

      setEarnings({
        totalEarnings,
        pendingPayouts: pendingAmount,
        completedPayouts: completedAmount,
        availableBalance,
        pastEarnings: pastPayouts,
        upcomingPayments: upcomingPayouts,
        payoutRequests: payoutRequests || [],
      });
    } catch (error) {
      console.error("Error fetching earnings data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchPaymentMethods = async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from("host_payment_methods")
        .select("*")
        .eq("host_id", user.id);

      if (error) throw error;

      setPaymentMethods(data || []);
    } catch (error) {
      console.error("Error fetching payment methods:", error);
    }
  };

  const handleRequestPayout = () => {
    if (paymentMethods.length === 0) {
      toast.error("Please add a payment method first");
      setPaymentAccountDialogOpen(true);
    } else {
      setPayoutRequestDialogOpen(true);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-800"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">Payments & Earnings</h1>

      <EarningsStats
        totalEarnings={earnings.totalEarnings}
        pendingPayouts={earnings.pendingPayouts}
        completedPayouts={earnings.completedPayouts}
        availableBalance={earnings.availableBalance}
        onRequestPayout={handleRequestPayout}
      />

      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Payment Processing Information</AlertTitle>
        <AlertDescription>
          Payout requests are reviewed within 24-48 hours. Once approved,
          payments are processed every Monday and typically take 3-5 business
          days to reach your account. GescoStay charges a 15% service fee on all
          bookings.
        </AlertDescription>
      </Alert>

      <Tabs defaultValue="payout-requests" className="mt-8">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="payout-requests">Payout Requests</TabsTrigger>
          <TabsTrigger value="upcoming">Upcoming Payouts</TabsTrigger>
          <TabsTrigger value="history">Payout History</TabsTrigger>
          <TabsTrigger value="settings">Payment Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="payout-requests" className="space-y-4 mt-4">
          <PayoutRequestsList
            payoutRequests={earnings.payoutRequests}
            onRequestPayout={handleRequestPayout}
          />
        </TabsContent>

        <TabsContent value="upcoming" className="space-y-4 mt-4">
          {earnings.upcomingPayments.length === 0 ? (
            <div className="text-center p-8 border rounded-lg">
              <p className="text-muted-foreground">
                No upcoming payouts at the moment.
              </p>
            </div>
          ) : (
            <>
              {earnings.upcomingPayments.map((payout) => (
                <Card key={payout.id}>
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-center">
                      <div>
                        <h3 className="text-lg font-medium">
                          ${payout.amount.toFixed(2)}
                        </h3>
                        <p className="text-sm text-muted-foreground">
                          Expected {new Date(payout.date).toLocaleDateString()}
                        </p>
                      </div>
                      <Badge
                        variant={
                          payout.status === "pending" ? "secondary" : "outline"
                        }
                      >
                        {payout.status}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground">
                      Processing time: {payout.processing_days} business days
                    </p>
                    <div className="mt-2">
                      <p className="text-xs text-muted-foreground">
                        From {payout.booking_ids.length}{" "}
                        {payout.booking_ids.length === 1
                          ? "booking"
                          : "bookings"}
                      </p>
                    </div>
                  </CardContent>
                  <CardFooter className="pt-0">
                    <Button variant="ghost" size="sm" className="ml-auto">
                      Details <ChevronRight className="h-4 w-4 ml-1" />
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </>
          )}
        </TabsContent>

        <TabsContent value="history" className="space-y-4 mt-4">
          {earnings.pastEarnings.length === 0 ? (
            <div className="text-center p-8 border rounded-lg">
              <p className="text-muted-foreground">No payout history yet.</p>
            </div>
          ) : (
            <>
              {earnings.pastEarnings.map((payout) => (
                <Card key={payout.id}>
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-center">
                      <div>
                        <h3 className="text-lg font-medium">
                          ${payout.amount.toFixed(2)}
                        </h3>
                        <p className="text-sm text-muted-foreground">
                          Paid on {new Date(payout.date).toLocaleDateString()}
                        </p>
                      </div>
                      <Badge
                        variant="secondary"
                        className="bg-green-100 text-green-800"
                      >
                        {payout.status}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">
                        Payment method:
                      </span>
                      <span>{payout.method}</span>
                    </div>
                    <div className="flex justify-between text-sm mt-1">
                      <span className="text-muted-foreground">Reference:</span>
                      <span className="font-mono">{payout.reference}</span>
                    </div>
                  </CardContent>
                  <CardFooter className="pt-0">
                    <Button variant="outline" size="sm" className="ml-auto">
                      <ArrowDownToLine className="h-4 w-4 mr-1" /> Download
                      Receipt
                    </Button>
                  </CardFooter>
                </Card>
              ))}

              <div className="text-center mt-4">
                <Button variant="ghost">
                  Show More History <ChevronDown className="h-4 w-4 ml-1" />
                </Button>
              </div>
            </>
          )}
        </TabsContent>

        <TabsContent value="settings" className="mt-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <h3 className="text-lg font-medium">Payment Settings</h3>
                <p className="text-sm text-muted-foreground">
                  Configure your payout methods and preferences
                </p>
              </div>
              <Button
                variant="outline"
                className="flex items-center gap-1"
                onClick={() => setPaymentAccountDialogOpen(true)}
              >
                Add Payment Method
              </Button>
            </CardHeader>
            <CardContent className="space-y-4">
              <PaymentMethodsList
                paymentMethods={paymentMethods}
                onAddPaymentMethod={() => setPaymentAccountDialogOpen(true)}
              />

              {paymentMethods.length > 0 && <PaymentSchedule />}
            </CardContent>
            <CardFooter>
              <Button className="ml-auto">Save Settings</Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Add Payment Method Dialog */}
      <PaymentMethodForm
        open={paymentAccountDialogOpen}
        onClose={() => setPaymentAccountDialogOpen(false)}
        onSuccess={() => {
          fetchPaymentMethods();
        }}
      />

      {/* Payout Request Dialog */}
      <PayoutRequestForm
        open={payoutRequestDialogOpen}
        onClose={() => setPayoutRequestDialogOpen(false)}
        onSuccess={() => {
          fetchEarningsData();
        }}
        paymentMethods={paymentMethods}
        availableBalance={earnings.availableBalance}
      />
    </div>
  );
};

export default HostEarnings;
