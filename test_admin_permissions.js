// Test Admin Permissions Script
// Run this in the browser console on the admin listings page after applying the SQL fix

console.log("=== Testing Admin Permissions ===");

async function testAdminPermissions() {
  try {
    // Import supabase client
    const { supabase } = await import('/src/integrations/supabase/client.ts');
    
    console.log("1. Testing current user admin status...");
    
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError) {
      console.error("Error getting user:", userError);
      return;
    }
    
    if (!user) {
      console.error("No user logged in");
      return;
    }
    
    console.log("Current user ID:", user.id);
    console.log("Current user email:", user.email);
    
    // Check user profile and admin status
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id, email, role')
      .eq('id', user.id)
      .single();
    
    if (profileError) {
      console.error("Error getting profile:", profileError);
      return;
    }
    
    console.log("User profile:", profile);
    
    // Test is_admin function
    const { data: adminCheck, error: adminError } = await supabase
      .rpc('is_admin', { uid: user.id });
    
    if (adminError) {
      console.error("Error checking admin status:", adminError);
    } else {
      console.log("is_admin() result:", adminCheck);
    }
    
    console.log("\n2. Testing listing access...");
    
    // Test properties access
    const { data: properties, error: propError } = await supabase
      .from('properties')
      .select('id, title, status, owner_id')
      .limit(3);
    
    if (propError) {
      console.error("Properties access error:", propError);
    } else {
      console.log("Properties accessible:", properties?.length || 0);
      if (properties && properties.length > 0) {
        console.log("Sample property:", properties[0]);
      }
    }
    
    // Test cars access
    const { data: cars, error: carError } = await supabase
      .from('cars')
      .select('id, title, status, owner_id')
      .limit(3);
    
    if (carError) {
      console.error("Cars access error:", carError);
    } else {
      console.log("Cars accessible:", cars?.length || 0);
      if (cars && cars.length > 0) {
        console.log("Sample car:", cars[0]);
      }
    }
    
    // Test hotels access
    const { data: hotels, error: hotelError } = await supabase
      .from('hotels')
      .select('id, title, status, owner_id')
      .limit(3);
    
    if (hotelError) {
      console.error("Hotels access error:", hotelError);
    } else {
      console.log("Hotels accessible:", hotels?.length || 0);
      if (hotels && hotels.length > 0) {
        console.log("Sample hotel:", hotels[0]);
      }
    }
    
    console.log("\n3. Testing update permissions...");
    
    // Test property update (if any properties exist)
    if (properties && properties.length > 0) {
      const testProperty = properties[0];
      console.log(`Testing property update for ID: ${testProperty.id}`);
      
      const { data: updateResult, error: updateError } = await supabase
        .from('properties')
        .update({ 
          status: testProperty.status === 'pending' ? 'approved' : 'pending',
          updated_at: new Date().toISOString()
        })
        .eq('id', testProperty.id)
        .select();
      
      if (updateError) {
        console.error("Property update error:", updateError);
      } else {
        console.log("Property update successful:", updateResult);
        
        // Revert the change
        await supabase
          .from('properties')
          .update({ 
            status: testProperty.status,
            updated_at: new Date().toISOString()
          })
          .eq('id', testProperty.id);
        
        console.log("Property status reverted");
      }
    }
    
    // Test hotel update (if any hotels exist)
    if (hotels && hotels.length > 0) {
      const testHotel = hotels[0];
      console.log(`Testing hotel update for ID: ${testHotel.id}`);
      
      const { data: updateResult, error: updateError } = await supabase
        .from('hotels')
        .update({ 
          status: testHotel.status === 'pending' ? 'approved' : 'pending',
          updated_at: new Date().toISOString()
        })
        .eq('id', testHotel.id)
        .select();
      
      if (updateError) {
        console.error("Hotel update error:", updateError);
      } else {
        console.log("Hotel update successful:", updateResult);
        
        // Revert the change
        await supabase
          .from('hotels')
          .update({ 
            status: testHotel.status,
            updated_at: new Date().toISOString()
          })
          .eq('id', testHotel.id);
        
        console.log("Hotel status reverted");
      }
    }
    
    console.log("\n=== Admin Permissions Test Complete ===");
    console.log("If you see 'successful' messages above, admin permissions are working!");
    console.log("If you see errors, there may still be permission issues.");
    
  } catch (error) {
    console.error("Test failed with error:", error);
  }
}

// Run the test
testAdminPermissions();

// Export for manual testing
window.testAdminPermissions = testAdminPermissions;
