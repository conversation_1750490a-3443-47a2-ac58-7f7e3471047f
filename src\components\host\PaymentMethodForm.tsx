
import { useState } from "react";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from "@/components/ui/dialog";
import { useAuth } from "@/components/layout/AuthProvider";

const paymentMethodSchema = z.object({
  provider: z.enum(["stripe", "wave"]),
  accountId: z.string().min(3, "Account ID must be at least 3 characters").refine(
    (value, ctx) => {
      if (ctx.parent.provider === "wave") {
        // Validate phone number format for Wave
        const phoneRegex = /^\+?[0-9]{8,15}$/;
        return phoneRegex.test(value) || "Please enter a valid phone number";
      }
      return true;
    },
    {
      message: "Please enter a valid phone number for Wave payments",
    }
  ),
  accountDetails: z.string().optional(),
});

interface PaymentMethodFormProps {
  open: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

const PaymentMethodForm = ({ open, onClose, onSuccess }: PaymentMethodFormProps) => {
  const { user } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<z.infer<typeof paymentMethodSchema>>({
    resolver: zodResolver(paymentMethodSchema),
    defaultValues: {
      provider: "wave",
      accountId: "",
      accountDetails: "",
    },
  });

  const selectedProvider = form.watch("provider");

  const onSubmit = async (values: z.infer<typeof paymentMethodSchema>) => {
    if (!user) return;
    setIsSubmitting(true);
    
    try {
      const { data: existingMethods } = await supabase
        .from("host_payment_methods")
        .select("*")
        .eq("host_id", user.id);
        
      const { data, error } = await supabase
        .from("host_payment_methods")
        .insert({
          host_id: user.id,
          provider: values.provider,
          account_id: values.accountId,
          account_details: values.accountDetails || null,
          is_default: !existingMethods || existingMethods.length === 0, // First one becomes default
          status: "pending_verification"
        })
        .select("*")
        .single();
        
      if (error) throw error;
      
      // If provider is Wave, send a verification request to the admin
      if (values.provider === "wave") {
        // Notify the admin about a new Wave payment method to verify
        const { error: notificationError } = await supabase
          .from("admin_notifications")
          .insert({
            type: "payment_method_verification",
            content: `New Wave payment method (${values.accountId}) added by host`,
            metadata: { 
              host_id: user.id,
              payment_method_id: data.id,
              provider: "wave"
            },
            status: "unread"
          });
          
        if (notificationError) {
          console.error("Error sending admin notification:", notificationError);
        }
      }
      
      toast.success("Payment method added successfully");
      form.reset();
      if (onSuccess) onSuccess();
      onClose();
    } catch (error: any) {
      console.error("Error adding payment method:", error);
      toast.error(`Failed to add payment method: ${error.message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Add Payment Method</DialogTitle>
          <DialogDescription>
            Connect your payment account to receive payouts from bookings.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="provider"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Payment Provider</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a payment provider" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="wave">Wave</SelectItem>
                      <SelectItem value="stripe">Stripe</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    {selectedProvider === "wave" 
                      ? "Wave is our recommended payment method in West Africa." 
                      : "Stripe is available for international payments."}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="accountId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {selectedProvider === "wave" ? "Wave Phone Number" : "Stripe Account ID"}
                  </FormLabel>
                  <FormControl>
                    <Input 
                      placeholder={selectedProvider === "wave" 
                        ? "Enter your Wave phone number (e.g. +221XXXXXXXX)" 
                        : "Enter your Stripe account ID"} 
                      {...field} 
                    />
                  </FormControl>
                  <FormDescription>
                    {selectedProvider === "wave" 
                      ? "The phone number connected to your Wave account. Include country code." 
                      : "Your account ID from Stripe's dashboard."}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            {selectedProvider === "wave" && (
              <FormField
                control={form.control}
                name="accountDetails"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Full Name (Required for Wave)</FormLabel>
                    <FormControl>
                      <Input placeholder="Your full name as registered with Wave" {...field} />
                    </FormControl>
                    <FormDescription>
                      This information is required for Wave payment verification.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
            <DialogFooter>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? "Adding..." : "Add Payment Method"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default PaymentMethodForm;
