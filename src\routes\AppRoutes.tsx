import { Routes, Route, Navigate } from "react-router-dom";
import { useAuth } from "@/components/layout/AuthProvider";
import { supabase } from "@/integrations/supabase/client";
import { useState, useEffect } from "react";
import Index from "@/pages/Index";
import NotFound from "@/pages/NotFound";
import ListingsPage from "@/pages/listings/ListingsPage";
import PropertyDetail from "@/pages/listings/PropertyDetail";
import AuthPage from "@/pages/auth/AuthPage";
import OTPVerification from "@/pages/auth/OTPVerification";
import EmailConfirm from "@/pages/auth/EmailConfirm";
import ProtectedRoute from "@/components/layout/ProtectedRoute";
import CreateListing from "@/pages/listings/CreateListing";
import EditListing from "@/pages/listings/EditListing";
import CarsListingPage from "@/pages/cars/CarsListingPage";
import CarDetailPage from "@/pages/cars/CarDetailPage";
import CreateCarListing from "@/pages/cars/CreateCarListing";
import EditCarListing from "@/pages/cars/EditCarListing";
import HotelsListingPage from "@/pages/hotels/HotelsListingPage";
import HotelDetail from "@/pages/hotels/HotelDetail";
import CreateHotel from "@/pages/hotels/CreateHotel";
import EditHotel from "@/pages/hotels/EditHotel";
import TermsOfService from "@/pages/legal/TermsOfService";
import SafetyPolicy from "@/pages/legal/SafetyPolicy";
import PrivacyPolicy from "@/pages/legal/PrivacyPolicy";
import CommunityPage from "@/pages/legal/CommunityPage";
import PaymentSuccess from "@/components/payment/PaymentSuccess";
import PaymentCancelled from "@/pages/payment/PaymentCancelled";
import WavePaymentPage from "@/pages/payment/WavePaymentPage";
import BookingsPage from "@/pages/bookings/BookingsPage";
import ProfilePage from "@/pages/profile/ProfilePage";
import { Database } from "@/integrations/supabase/types";

// Admin pages
import AdminAuth from "@/pages/admin/AdminAuth";
import AdminLayout from "@/components/layout/AdminLayout";
import AdminDashboard from "@/pages/admin/AdminDashboard";
import AdminUsers from "@/pages/admin/AdminUsers";
import AdminListings from "@/pages/admin/AdminListings";
import AdminBookings from "@/pages/admin/AdminBookings";
import AdminPayments from "@/pages/admin/AdminPayments";
import AdminAnalytics from "@/pages/admin/AdminAnalytics";
import AdminSettings from "@/pages/admin/AdminSettings";

// Host pages
import HostLayout from "@/components/layout/HostLayout";
import HostDashboard from "@/pages/host/HostDashboard";
import HostListings from "@/pages/host/HostListings";
import HostEarnings from "@/pages/host/HostEarnings";
import HostReservations from "@/pages/host/HostReservations";
import HostMessages from "@/pages/host/HostMessages";
import MessagesPage from "@/pages/messages/MessagesPage";
import HostSettings from "@/pages/host/HostSettings";
import { SimpleMapTest } from "@/components/maps/SimpleMapTest";

type UserRole = Database["public"]["Enums"]["user_role"];

const AppRoutes = () => {
  const { user, loading, getUserRole, getUserRoles } = useAuth();
  const [userRole, setUserRole] = useState<UserRole | null>(null);
  const [userRoles, setUserRoles] = useState<string[]>([]);
  const [checkingRole, setCheckingRole] = useState(true);

  useEffect(() => {
    let mounted = true;

    const checkUserRole = async () => {
      if (!user) {
        if (mounted) {
          setUserRole(null);
          setUserRoles([]);
          setCheckingRole(false);
        }
        return;
      }

      try {
        // Use AuthProvider's cached role methods instead of direct Supabase queries
        const role = await getUserRole();
        const roles = await getUserRoles();

        if (mounted) {
          setUserRole(role);
          setUserRoles(roles);
        }
      } catch (error) {
        console.error("Error checking user role:", error);
        if (mounted) {
          // Set default role on error
          setUserRole("guest");
          setUserRoles(["guest"]);
        }
      } finally {
        if (mounted) {
          setCheckingRole(false);
        }
      }
    };

    if (!loading) {
      checkUserRole();
    }

    // Fallback timeout to ensure checkingRole is never stuck
    const fallbackTimeout = setTimeout(() => {
      if (mounted) {
        setCheckingRole(false);
        if (user && !userRole) {
          setUserRole("guest");
          setUserRoles(["guest"]);
        }
      }
    }, 5000); // 5 second timeout

    return () => {
      mounted = false;
      clearTimeout(fallbackTimeout);
    };
  }, [user, loading, getUserRole, getUserRoles]);

  // Check if user is admin
  const isAdmin = userRole === "admin" || userRoles.includes("admin");
  const isHost = userRole === "host" || userRoles.includes("host");

  // Admins can also access host features, but they have their own dedicated interface

  if (loading || checkingRole) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-accent"></div>
      </div>
    );
  }

  return (
    <Routes>
      {/* Main App Routes */}
      <Route path="/" element={<Index />} />
      <Route path="/listings" element={<ListingsPage />} />
      <Route
        path="/listings/create"
        element={
          <ProtectedRoute>
            <CreateListing />
          </ProtectedRoute>
        }
      />
      <Route
        path="/listings/edit/:id"
        element={
          <ProtectedRoute>
            <EditListing />
          </ProtectedRoute>
        }
      />
      <Route path="/listings/:id" element={<PropertyDetail />} />
      <Route path="/payment-success" element={<PaymentSuccess />} />
      <Route path="/payment-cancelled" element={<PaymentCancelled />} />
      <Route path="/wave-payment" element={<WavePaymentPage />} />

      {/* Car Booking Success Routes */}
      <Route
        path="/car-booking-success/payment-success"
        element={<PaymentSuccess />}
      />
      <Route
        path="/car-booking-success/payment-cancelled"
        element={<PaymentCancelled />}
      />

      {/* Car Rental Routes */}
      <Route path="/cars" element={<CarsListingPage />} />
      <Route path="/cars/:id" element={<CarDetailPage />} />
      <Route
        path="/cars/create"
        element={
          <ProtectedRoute>
            <CreateCarListing />
          </ProtectedRoute>
        }
      />
      <Route
        path="/cars/edit/:id"
        element={
          <ProtectedRoute>
            <EditCarListing />
          </ProtectedRoute>
        }
      />

      {/* Hotel Routes */}
      <Route path="/hotels" element={<HotelsListingPage />} />
      <Route path="/hotels/:id" element={<HotelDetail />} />
      <Route
        path="/hotels/create"
        element={
          <ProtectedRoute>
            <CreateHotel />
          </ProtectedRoute>
        }
      />
      <Route
        path="/hotels/edit/:id"
        element={
          <ProtectedRoute>
            <EditHotel />
          </ProtectedRoute>
        }
      />

      {/* Booking Management Routes */}
      <Route
        path="/bookings"
        element={
          <ProtectedRoute>
            <BookingsPage />
          </ProtectedRoute>
        }
      />

      {/* Profile Route - for regular users */}
      <Route
        path="/profile"
        element={
          <ProtectedRoute>
            <ProfilePage />
          </ProtectedRoute>
        }
      />

      {/* Messages Route - for all users */}
      <Route
        path="/messages"
        element={
          <ProtectedRoute>
            <MessagesPage />
          </ProtectedRoute>
        }
      />

      <Route
        path="/auth"
        element={
          !user ? (
            <AuthPage />
          ) : isHost ? (
            <Navigate to="/host" replace />
          ) : isAdmin ? (
            <Navigate to="/admin/dashboard" replace />
          ) : (
            <Navigate to="/" replace />
          )
        }
      />

      {/* OTP Verification Route - accessible without authentication */}
      <Route path="/auth/verify-otp" element={<OTPVerification />} />

      {/* Email Confirmation Route - accessible without authentication */}
      <Route path="/auth/confirm" element={<EmailConfirm />} />

      {/* Host Routes */}
      <Route
        path="/host"
        element={
          user ? (
            isHost ? (
              <HostLayout />
            ) : isAdmin ? (
              <Navigate to="/admin/dashboard" replace />
            ) : (
              <Navigate to="/profile" replace />
            )
          ) : (
            <Navigate to="/auth" replace />
          )
        }
      >
        <Route index element={<HostDashboard />} />
        <Route path="listings" element={<HostListings />} />
        <Route path="reservations" element={<HostReservations />} />
        <Route path="earnings" element={<HostEarnings />} />
        <Route path="messages" element={<HostMessages />} />
        <Route path="settings" element={<HostSettings />} />
      </Route>

      {/* Admin Routes */}
      <Route path="/admin/auth" element={<AdminAuth />} />
      <Route
        path="/admin"
        element={
          user && isAdmin ? (
            <Navigate to="/admin/dashboard" replace />
          ) : (
            <Navigate to="/admin/auth" replace />
          )
        }
      />
      <Route
        path="/admin"
        element={
          user && isAdmin ? (
            <AdminLayout />
          ) : (
            <Navigate to="/admin/auth" replace />
          )
        }
      >
        <Route path="dashboard" element={<AdminDashboard />} />
        <Route path="users" element={<AdminUsers />} />
        <Route path="listings" element={<AdminListings />} />
        <Route path="bookings" element={<AdminBookings />} />
        <Route path="payments" element={<AdminPayments />} />
        <Route path="analytics" element={<AdminAnalytics />} />
        <Route path="settings" element={<AdminSettings />} />
      </Route>

      {/* Test Routes */}
      <Route path="/test/maps" element={<SimpleMapTest />} />

      {/* Legal Pages */}
      <Route path="/terms" element={<TermsOfService />} />
      <Route path="/safety" element={<SafetyPolicy />} />
      <Route path="/privacy" element={<PrivacyPolicy />} />
      <Route path="/community" element={<CommunityPage />} />

      <Route path="*" element={<NotFound />} />
    </Routes>
  );
};

export default AppRoutes;
